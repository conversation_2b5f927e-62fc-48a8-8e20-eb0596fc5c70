<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="mapping.SignTemplatePushLogMapper" >
  <resultMap id="BaseResultMap" type="kd.entity.SignTemplatePushLog" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Oct 17 10:41:18 CST 2024.
    -->
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="sign_template_id" property="signTemplateId" jdbcType="VARCHAR" />
    <result column="claim_case_no" property="claimCaseNo" jdbcType="VARCHAR" />
    <result column="claim_case_object_id" property="claimCaseObjectId" jdbcType="VARCHAR" />
    <result column="json_data" property="jsonData" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="error_msg" property="errorMsg" jdbcType="VARCHAR" />
    <result column="call_back_json" property="callBackJson" jdbcType="VARCHAR" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="modifier" property="modifier" jdbcType="VARCHAR" />
    <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    a.id, a.sign_template_id, a.claim_case_no, a.claim_case_object_id, a.json_data, a.status, a.error_msg,
    a.call_back_json,a.creator, a.create_time, a.modifier, a.modify_time
  </sql>
  <sql id="selectByPrimaryKey">
    select 
    <include refid="mapping.SignTemplatePushLogMapper.Base_Column_List" />
    from t_sign_template_push_log a
    where a.id = #{id,jdbcType=VARCHAR}
  </sql>
  <sql id="deleteByPrimaryKey">
    delete from t_sign_template_push_log
    where id = #{id,jdbcType=VARCHAR}
  </sql>
  <sql id="insertSelective">
    insert into t_sign_template_push_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="signTemplateId != null" >
        sign_template_id,
      </if>
      <if test="claimCaseNo != null" >
        claim_case_no,
      </if>
      <if test="claimCaseObjectId != null" >
        claim_case_object_id,
      </if>
      <if test="jsonData != null" >
        json_data,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="errorMsg != null" >
        error_msg,
      </if>
      <if test="callBackJson != null" >
        call_back_json,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="modifier != null" >
        modifier,
      </if>
      <if test="modifyTime != null" >
        modify_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="signTemplateId != null" >
        #{signTemplateId,jdbcType=VARCHAR},
      </if>
      <if test="claimCaseNo != null" >
        #{claimCaseNo,jdbcType=VARCHAR},
      </if>
      <if test="claimCaseObjectId != null" >
        #{claimCaseObjectId,jdbcType=VARCHAR},
      </if>
      <if test="jsonData != null" >
        #{jsonData,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="errorMsg != null" >
        #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="callBackJson != null" >
        #{call_back_json,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null" >
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null" >
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </sql>
  <sql id="updateByPrimaryKeySelective">
    update t_sign_template_push_log
    <set >
      <if test="signTemplateId != null" >
        sign_template_id = #{signTemplateId,jdbcType=VARCHAR},
      </if>
      <if test="claimCaseNo != null" >
        claim_case_no = #{claimCaseNo,jdbcType=VARCHAR},
      </if>
      <if test="claimCaseObjectId != null" >
        claim_case_object_id = #{claimCaseObjectId,jdbcType=VARCHAR},
      </if>
      <if test="jsonData != null" >
        json_data = #{jsonData,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="errorMsg != null" >
        error_msg = #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="errorMsg != null" >
        error_msg = #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="callBackJson != null" >
        call_back_json = #{callBackJson,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null" >
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null" >
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </sql>
</mapper>