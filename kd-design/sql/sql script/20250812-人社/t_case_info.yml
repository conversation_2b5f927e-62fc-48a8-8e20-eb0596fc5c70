dataSourceKey: defaultDS
destination: example
groupId: g1
esMapping:
  _index: t_case_info
  _id: _id
  sql: "SELECT
                    p.id AS _id,
                    p.id AS id,
                    p.case_number AS caseNumber,
                    p.rider_name AS riderName,
                    p.rider_id_card AS riderIdCard,
                    p.responsible_person AS responsiblePerson,
                    p.accident_tags AS accidentTags,
                    p.reporter_name AS reporterName,
                    p.reporter_mobile AS reporterMobile,
                    p.case_status AS caseStatus,
                    p.case_service_type AS caseServiceType,
                    p.external_state AS externalState,
                    p.report_time AS reportTime,
                    p.rider_mobile AS riderMobile,
                    p.rider_type AS riderType,
                    p.has_waybill AS hasWaybill,
                    p.related_waybill_number AS relatedWaybillNumber,
                    p.order_accept_time AS orderAcceptTime,
                    p.merchant_address AS merchantAddress,
                    p.no_waybill_scenario_type AS noWaybillScenarioType,
                    p.merchant_type AS merchantType,
                    p.merchant_id AS merchantId,
                    p.site_city AS siteCity,
                    p.contact_name AS contactName,
                    p.contact_phone AS contactPhone,
                    p.station_manager_name AS stationManagerName,
                    p.station_manager_phone AS stationManagerPhone,
                    p.accident_time AS accidentTime,
                    p.accident_address AS accidentAddress,
                    p.accident_type AS accidentType,
                    p.accident_cause AS accidentCause,
                    p.accident_process AS accidentProcess,
                    p.accident_province AS accidentProvince,
                    p.accident_city AS accidentCity,
                    p.accident_district AS accidentDistrict,
                    p.merchant_name AS merchantName,
                    p.site_name AS siteName,
                    p.is_remote_medical AS isRemoteMedical,
                    p.medical_institution_name AS medicalInstitutionName,
                    p.medical_institution_level AS medicalInstitutionLevel,
                    p.medical_institution_address AS medicalInstitutionAddress,
                    p.medical_institution_contact AS medicalInstitutionContact,
                    p.platform_enterprise_opinion AS platformEnterpriseOpinion,
                    p.injured_parts AS injuredParts,
                    p.diagnosis_content AS diagnosisContent,
                    p.first_visit_time AS firstVisitTime,
                    p.first_visit_remark AS firstVisitRemark,
                    p.case_closed_time AS caseClosedTime,
                    p.case_closed_remark AS caseClosedRemark,
                    p.suspension_time AS suspensionTime,
                    p.suspension_remark AS suspensionRemark,
                    p.declaration_time AS declarationTime,
                    p.declaration_remark AS declarationRemark,
                    p.return_time AS returnTime,
                    p.return_remark AS returnRemark,
                    p.finish_time AS finishTime,
                    p.finish_remark AS finishRemark,
                    p.determine_if_successful AS determineIfSuccessful,
                    p.determine_time AS determineTime,
                    p.determine_failure_remark AS determineFailureRemark,
                    p.distribution_amount AS distributionAmount,
                    p.reviewer AS reviewer,
                    p.create_time AS createTime,
                    p.update_time AS updateTime
                FROM
                 t_case_info p"
  etlCondition: "where a.c_time>={}"
  commitBatch: 3000