-- 向 t_case_info 表中添加字段，并带上注释
ALTER TABLE t_case_info
ADD COLUMN case_service_type VARCHAR(200) AFTER case_status,
ADD COLUMN has_waybill VARCHAR(50) AFTER rider_type,
ADD COLUMN related_waybill_number VARCHAR(500) AFTER has_waybill,
ADD COLUMN order_accept_time DATETIME AFTER related_waybill_number,
ADD COLUMN merchant_address VARCHAR(500) AFTER order_accept_time,
ADD COLUMN no_waybill_scenario_type VARCHAR(100) AFTER merchant_address,
ADD COLUMN merchant_type VARCHAR(100) AFTER merchant_address,
ADD COLUMN merchant_id VARCHAR(100) AFTER merchant_type,
ADD COLUMN site_city VARCHAR(200) AFTER merchant_id,
ADD COLUMN contact_name VARCHAR(150) AFTER site_city,
ADD COLUMN contact_phone VARCHAR(64) AFTER contact_name,
ADD COLUMN station_manager_name VARCHAR(100) AFTER contact_phone,
ADD COLUMN station_manager_phone VARCHAR(64) AFTER station_manager_name;

-- 添加字段注释
ALTER TABLE t_case_info
MODIFY COLUMN case_service_type VARCHAR(200) COMMENT '案件服务类型',
MODIFY COLUMN has_waybill VARCHAR(50) COMMENT '是否有运单',
MODIFY COLUMN related_waybill_number VARCHAR(500) COMMENT '关联运单号',
MODIFY COLUMN order_accept_time DATETIME COMMENT '接单时间',
MODIFY COLUMN merchant_address VARCHAR(500) COMMENT '商家地址',
MODIFY COLUMN no_waybill_scenario_type VARCHAR(100) COMMENT '无运单场景类型',
MODIFY COLUMN merchant_type VARCHAR(100) COMMENT '商类型',
MODIFY COLUMN merchant_id VARCHAR(100) COMMENT '商ID',
MODIFY COLUMN site_city VARCHAR(200) COMMENT '站点城市',
MODIFY COLUMN contact_name VARCHAR(150) COMMENT '联系人姓名',
MODIFY COLUMN contact_phone VARCHAR(64) COMMENT '联系人电话',
MODIFY COLUMN station_manager_name VARCHAR(100) COMMENT '站长姓名',
MODIFY COLUMN station_manager_phone VARCHAR(64) COMMENT '站长电话';