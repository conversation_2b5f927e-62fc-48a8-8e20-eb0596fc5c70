package kd.main.common;

import java.util.LinkedHashMap;
import java.util.Map;

public enum ClaimCaseObjectStatusEnum {

    BAX10("BAX10","初始化生成"),

    BAX11("BAX11","待补充影像材料"),
    BAX12("BAX12","影像材料已补充"),

    BAX21("BAX21","估损待提交"),
    BAX22("BAX22","估损初审进行中"),
    BAX23("BAX23","估损初审通过"),
    BAX24("BAX24","估损初审驳回"),
    BAX25("BAX25","估损复审审核中"),
    BAX26("BAX26","估损复审审核通过"),
    BAX27("BAX27","估损复审审核驳回"),

    BAX31("BAX31","理算待提交"),
    BAX32("BAX32","理算初审进行中"),
    BAX33("BAX33","理算初审通过"),
    BAX34("BAX34","理算初审驳回"),
    BAX35("BAX35","理算复审审核中"),
    BAX36("BAX36","理算复审审核通过"),
    BAX37("BAX37","理算复审审核驳回"),
    BAX38("BAX38","签字待发起"),
    BAX39("BAX39","签字待返回"),
    BAX40("BAX40","签章进行中"),

    BAX99("BAX99","完成")

    ;

    private String code;
    private String msg;


    ClaimCaseObjectStatusEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }


    public static String codeToMsg(String code){
        ClaimCaseObjectStatusEnum[] i = values();
        for (ClaimCaseObjectStatusEnum area : i) {
            if (area.getCode().equals(code)){
                return area.getMsg();
            }
        }
        return "未知";
    }


    private static final Map<String, Object> statusMap = new LinkedHashMap<>();

    /**
     * 获取该枚举类的map形式
     * @return Map<String, String>
     */
    public static Map<String, Object> getStatusMap() {
        if (statusMap.isEmpty()) {
            for (ClaimCaseObjectStatusEnum item : ClaimCaseObjectStatusEnum.values()) {
                statusMap.put(item.getCode(), item.getMsg());
            }
        }
        return statusMap;
    }
}
