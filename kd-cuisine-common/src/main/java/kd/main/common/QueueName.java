package kd.main.common;

/**
 * @Author: Pater
 * @Date: 2021/5/31 15:42
 */
public class QueueName {

    //AntQueneName

    public static final String KD_ANT_RECEPTION = "kd_ant_reception";
    public static final String KD_ANT_DATA_COLLECTION_SEND = "kd_ant_data_collection_send";
    public static final String KD_ANT_DATA_COLLECTION_SEND_TO_BACK = "kd_ant_data_collection_send_to_back";
    public static final String KD_ANT_RECEPTION_TO_BACK = "kd_ant_reception_to_back";


    public static final String KD_ELE_LOCAL_POLICY_DEAL = "kd_ele_local_policy_deal";
    public static final String KD_CUISINE_POLICY_EMAIL = "kd_cuisine_policy_email";

    public static final String KD_SCALLION_STATUS_BACK = "kd_scallion_status_back";
    public static final String KD_SCALLION_STATUS_BACK_TTL = "kd_scallion_status_back_ttl";
    public static final String KD_SCALLION_STATUS_BACK_TTL_EXCHANGE = "kd_scallion_status_back_ttl_exchange";
    public static final String KD_SCALLION_STATUS_BACK_EXCHANGE = "kd_scallion_status_back_exchange";

    public static final String KD_CLAIM_CASE_TIME_OUT = "kd_claim_case_time_out";

    public static final String KD_SET_CLAIM_CAST_LABEL="kd_set_claim_cast_label";

    public static final String KD_ES_HEALTH = "kd_es_health";

    /* 短信平台 */
    public static final String KD_TURNIP = "kd_turnip";

    /* 文件重新上传 */
    public static final String KD_GREEN_RADISH = "KD_GREEN_RADISH";

    /* 定时任务加载服务 */
    public static final String KD_CARROT = "kd_carrot";

    /* 文件生成任务创建 */
    public static final String KD_RADISH = "kd_radish";
    public static final String KD_RADISH_TTL = "kd_radish_ttl";
    public static final String KD_RADISH_TTL_EXCHANGE = "kd_radish_ttl_exchange";
    public static final String KD_RADISH_EXCHANGE = "kd_radish_exchange";



    public static final String KD_PUSH_TASK_ADD = "kd_push_task_add";
    public static final String KD_PUSH_TASK = "kd_push_task";
    public static final String KD_PUSH_TASK_TTL = "kd_push_task_ttl";
    public static final String KD_PUSH_TASK_TTL_EXCHANGE = "kd_push_task_ttl_exchange";
    public static final String KD_PUSH_TASK_EXCHANGE = "kd_push_task_exchange";


    /**
     * 被限流的请求，重新请求
     */
    public static final String KD_REPUSH_OVERLOAD_REQUEST = "kd_repush_overload_request";
    public static final String KD_REPUSH_OVERLOAD_REQUEST_TTL = "kd_repush_overload_request_ttl";
    public static final String KD_REPUSH_OVERLOAD_REQUEST_TTL_EXCHANGE = "kd_repush_overload_request_ttl_exchange";
    public static final String KD_REPUSH_OVERLOAD_REQUEST_EXCHANGE = "kd_repush_overload_request_exchange";

    /**
     * MongoDB插入监听mq
     */
    public static final String KD_CHANGE_STREAM = "kd_change_stream";

    public static final String KD_HMRB_CHANGE_STREAM = "kd_hmrb_change_stream";

    /**
     * MongoDB的redis_dict表监听mq
     */
    public static final String KD_REDIS_DICT_EXCHANGE = "kd_redis_dict_exchange";
    public static final String KD_REDIS_DICT_QUEUE = "kd_redis_dict_queue";
    public static final String KD_HM_REDIS_DICT_QUEUE = "kd_hm_redis_dict_queue";

    /* 用于cannal*/
    public static final String CANAL_QUEUE = "canal_queue";
    public static final String CANAL_EXCHANGE = "canal_exchange";
    public static final String CANAL_KEY = "canal_key";


    public static final String CANAL_PUSH = "canal_push";
    public static final String CANAL_PUSH_EXCHANGE = "canal_push_exchange";
    public static final String CANAL_PUSH_KEY = "canal_push_key";

    //用于同步 es表 subject

    public static final String CANAL_TABLE_EXCHANGE = "canal_table_exchange";
    public static final String CANAL_TABLE_SUBJECT = "canal_table_subject";
    public static final String CANAL_TABLE_SUBJECT_KEY = "canal_table_subject_key"; //用于同步的 key
    public static final String CANAL_TABLE_POLICY_PERSON_STATISTICS = "canal_table_policy_person_statistics"; //用于同步的 key
    public static final String CANAL_TABLE_POLICY_PERSON_STATISTICS_KEY = "canal_table_policy_person_statistics_key"; //用于同步的 key




    /* job所需*/
    //重新上载文件
    public static final String COVER_FILE_TASK_JOB = "cover_file_task_job";

    /* job所需*/
    //重试推送
    public static final String RECALL_PUSH_TASK_JOB = "recall_push_task_job";

    public static final String GEN_CACHE_FLOAT_PRODUCT_JOB = "gen_cache_float_product_job";

    public static final String KD_CCIC_POLICY_PUSH_IMMEDIATE = "kd_ccic_policy_push_immediate";  //废弃
    public static final String KD_INS_POLICY_PUSH_IMMEDIATE = "kd_ins_policy_push_immediate";

    public static final String KD_LOCAL_FILE_UPLOAD = "kd_local_file_upload";

    public static final String KD_CASE_DEATH_PUSH_BS = "kd_case_death_push_bs";

    public static final String KD_CCIC_POLICY_PUSH_DAYLY = "kd_ccic_policy_push_dayly";  //废弃
    public static final String KD_INS_POLICY_PUSH_DAYLY = "kd_ins_policy_push_dayly";
    public static final String KD_INS_POLICY_PUSH_RB_DAYLY = "kd_ins_policy_push_rb_dayly";     // 00:30 清空人保推送Redis

    public static final String KD_CCIC_POLICY_REPUSH_WITHOUT_POLICYNO_DAYLY = "kd_ccic_policy_repush_without_policyno_dayly"; //废弃
    public static final String KD_INS_POLICY_REPUSH_WITHOUT_POLICYNO_DAYLY = "kd_ins_policy_repush_without_policyno_dayly"; //废弃

    public static final String KD_POLICY_PUSH_CCIC = "kd_policy_push_ccic"; //即将废弃
    public static final String KD_POLICY_PUSH_INS = "kd_policy_push_ins";

    //lily.Yin 2025/3/25 16:29: 延迟推送MQ（推送保全，增加ttl）
    public static final String KD_POLICY_DELAY_PUSH_INS = "kd_policy_delay_push_ins";//延时推送MQ
    public static final String KD_POLICY_DELAY_PUSH_INS_TTL = "kd_policy_delay_push_ins_ttl";
    public static final String KD_POLICY_DELAY_PUSH_INS_TTL_EXCHANGE = "kd_policy_delay_push_ins_ttl_exchange";
    public static final String KD_POLICY_DELAY_PUSH_INS_EXCHANGE = "kd_policy_delay_push_ins_exchange";

    //添加error log
    public static final String KD_ERROR_LOG_WRITE = "kd_error_log_write";
    public static final String KD_ERROR_LOG_WRITE_TTL = "kd_error_log_write_ttl";
    public static final String KD_ERROR_LOG_WRITE_TTL_EXCHANGE = "kd_error_log_write_ttl_exchange";
    public static final String KD_ERROR_LOG_WRITE_EXCHANGE = "kd_error_log_write_exchange";

    public static final String KD_POLICY_PUSH_INS_TEMP = "kd_policy_push_ins_temp";
    public static final String KD_POLICY_PUSH_INS_TEMP_JOB = "kd_policy_push_ins_temp_job";

    // 人保推送保全，人保单保全一批推送，防止队列积压
    public static final String KD_POLICY_PUSH_RB_INS = "kd_policy_push_rb_ins";
    //人保推送保全，增加ttl
    public static final String KD_POLICY_PUSH_RB_INS_TTL = "kd_policy_push_rb_ins_ttl";
    public static final String KD_POLICY_PUSH_RB_INS_TTL_EXCHANGE = "kd_policy_push_rb_ins_ttl_exchange";
    public static final String KD_POLICY_PUSH_RB_INS_EXCHANGE = "kd_policy_push_rb_ins_exchange";

    /*北京烤鸭*/
    public static final String KD_ROASTDUCK_RABBIT_SEND_MESSAGE ="kd_roastduck_rabbit_send_message";
    public static final String KD_ROASTDUCK_RABBIT_SUPPLEMENTARY_MATERIALS ="kd_roastduck_rabbit_supplementary_materials";
    public static final String KD_ROASTDUCK_CLAIM_CASE_VERIFY_CENTER_LISTENER ="kd_roastduck_claim_case_verify_center_listener";
    public static final String KD_ROASTDUCK_BUSINESS_DATA_EXPORT = "kd_roastduck_business_data_export";
//    public static final String KD_ROASTDUCK_SYNC_ASSESSMENT_REPORT_LISTENER ="kd_roastduck_sync_assessment_report_listener";

    // 超赔案件匹配底层案件
    public static final String KD_ROASTDUCK_CLAIM_CASE_MATCH_ORIGINAL_JOB = "kd_roastduck_claim_case_match_original_job";
    public static final String KD_ROASTDUCK_CLAIM_CASE_MATCH_ORIGINAL = "kd_roastduck_claim_case_match_original";

    // 超赔案件同步标签
    public static final String KD_ROASTDUCK_CLAIM_CASE_SYNC_LABEL_JOB = "KD_ROASTDUCK_CLAIM_CASE_SYNC_LABEL_JOB";
    public static final String KD_ROASTDUCK_CLAIM_CASE_SYNC_LABEL = "KD_ROASTDUCK_CLAIM_CASE_SYNC_LABEL";


    // APP案件关闭
    public static final String KD_APP_CLOSE_CASE = "kd_app_close_case";
    // APP案件报案
    public static final String KD_APP_REPORT_CASE = "kd_app_report_case";
    // APP案件补材
    public static final String KD_APP_UPLOAD_IMG_CASE = "kd_app_upload_img_case";

    public static final String KD_CLAIM_CASE_APPLY_BOOK = "kd_claim_case_apply_book";

    public static final String KD_CLAIM_CASE_ATTACH_OCR = "kd_claim_case_attach_ocr";

    public static final String KD_CLAIM_CASE_BIND_POLICY_PERSON = "kd_claim_case_bind_policy_person";


    /* 窝窝头项目 */
    /* job所需*/
    public static final String KD_SYNC_POLICY_PERSON_JOB = "kd_sync_policy_person_job";
    public static final String KD_SYNC_AGENT_INFO_JOB = "kd_sync_agent_info_job";
    public static final String KD_SYNC_AREA_JOB = "kd_sync_area_job";
    public static final String KD_SYNC_FLOAT_PRODUCT_JOB = "kd_sync_float_product_job";
    public static final String KD_SYNC_CLAIM_JOB = "kd_sync_claim_job";
    /**
     * 定时同步昨日案件推送保司最新记录
     */
    public static final String KD_SYNC_INSURANCE_COMPANY_CASE_PUSH_LOG_JOB = "kd_sync_insurance_company_case_push_log_job";
    /**
     * 同步特定日期案件推送保司最新记录
     */
    public static final String KD_SYNC_INSURANCE_COMPANY_CASE_PUSH_LOG = "kd_sync_insurance_company_case_push_log";

    /**
     * 案件推送保司统计基础数据更新
     */
    public static final String KD_GEN_INSURANCE_STATISTICS = "kd_gen_insurance_statistics";
    public static final String KD_GEN_INSURANCE_STATISTICS_TTL = "kd_gen_insurance_statistics_ttl";
    public static final String KD_GEN_INSURANCE_STATISTICS_TTL_EXCHANGE = "kd_gen_insurance_statistics_ttl_exchange";
    public static final String KD_GEN_INSURANCE_STATISTICS_EXCHANGE = "kd_gen_insurance_statistics_exchange";

    /**
     * 定时任务同步三库案件专用
     */
    public static final String KD_SYNC_CLAIM_4BS_JOB = "kd_sync_claim_4bs_job";


    public static final String KD_EXPORT_POLICY_LN_JOB = "kd_export_policy_ln_job";
    public static final String KD_EXPORT_PICC_POLICY_JOB = "kd_export_picc_policy_job";
    public static final String KD_EXPORT_HX_POLICY_JOB = "kd_export_hx_policy_job";
    //盒马人保投保数据导出sftp
    public static final String KD_EXPORT_HMRB_POLICY_JOB = "kd_export_hmrb_policy_job";
    public static final String KD_EXPORT_CASE_DD_JOB = "kd_export_case_dd_job";

    public static final String KD_SEND_POLICY_EMAILS_JOB = "kd_send_policy_emails_job";

    // 同步 被保人 根据模板数据
    public static final String KD_SYNC_POLICY_PERSON = "kd_sync_policy_person";

    // 同步 案件数据 根据模板数据
    public static final String KD_SYNC_CLAIM_CASE = "kd_sync_claim_case";

    // 镜像案件 案件赔付对象 案件预处理信息
    public static final String KD_GEN_CLAIM_CASE_MIRROR = "kd_gen_claim_case_mirror";
    public static final String KD_GEN_CLAIM_CASE_MIRROR_STATICS = "kd_gen_claim_case_mirror_statics";
    public static final String KD_GEN_CLAIM_CASE_MIRROR_STATICS_EXCHANGE = "kd_gen_claim_case_mirror_statics_exchange";
    public static final String KD_GEN_CLAIM_CASE_MIRROR_STATICS_TTL = "kd_gen_claim_case_mirror_statics_ttl";
    public static final String KD_GEN_CLAIM_CASE_MIRROR_STATICS_TTL_EXCHANGE = "kd_gen_claim_case_mirror_statics_ttl_exchange";

    // 案件推送信息
    public static final String KD_SYNC_CLAIM_CASE_PUSH_OTHER_JOB = "kd_sync_claim_case_push_other_job";
    public static final String KD_SYNC_CLAIM_CASE_PUSH_OTHER= "kd_sync_claim_case_push_other";
    public static final String KD_GEN_CLAIM_CASE_PUSH_OTHER_STATICS = "kd_gen_claim_case_push_other_statics";
    public static final String KD_GEN_CLAIM_CASE_PUSH_OTHER_STATICS_EXCHANGE = "kd_gen_claim_case_push_other_statics_exchange";
    public static final String KD_GEN_CLAIM_CASE_PUSH_OTHER_STATICS_TTL = "kd_gen_claim_case_push_other_statics_ttl";
    public static final String KD_GEN_CLAIM_CASE_PUSH_OTHER_STATICS_TTL_EXCHANGE = "kd_gen_claim_case_push_other_statics_ttl_exchange";

    //进行 保单统计基础数据插入
    public static final String KD_GEN_POLICY_STATISTICS = "kd_gen_policy_statistics";
    public static final String KD_GEN_POLICY_STATISTICS_TTL = "kd_gen_policy_statistics_ttl";
    public static final String KD_GEN_POLICY_STATISTICS_TTL_EXCHANGE = "kd_gen_policy_statistics_ttl_exchange";
    public static final String KD_GEN_POLICY_STATISTICS_EXCHANGE = "kd_gen_policy_statistics_exchange";

    //生成理赔导出任务
    public static final String KD_GEN_EXPORT_TASK = "KD_GEN_EXPORT_TASK";

    //理赔案件全量导出
    public static final String KD_EXPORT_TASK_CLAIM = "kd_export_task_claim";

    // 验证所有赔付对象
    public static final String KD_CLAIM_CASE_OBJECT_VERIFY = "kd_claim_case_object_verify";


    //kd-ins-push
    public static final String KD_INS_PUSH_GY_SEND_STATUS_ELM = "kd_ins_push_gy_send_status_elm";
    public static final String KD_INS_PUSH_GY_SEND_EMPOWER_CONFIRM_ELM = "kd_ins_push_gy_send_empower_confirm_elm";
    public static final String KD_INS_PUSH_GY_SEND_FLOAT_FEE_CONFIRM_ELM = "kd_ins_push_gy_send_float_fee_confirm_elm";

    //kd-picc-ins-push 人保
    public static final String KD_INS_PUSH_RB_SEND_STATUS_ELM = "kd_ins_push_rb_send_status_elm";       // 理赔状态回传
    public static final String KD_INS_PUSH_RB_SEND_EMPOWER_CONFIRM_ELM = "kd_ins_push_rb_send_empower_confirm_elm";     // 投保授权回传
    public static final String KD_INS_PUSH_RB_SEND_FLOAT_FEE_CONFIRM_ELM = "kd_ins_push_rb_send_float_fee_confirm_elm";     // 浮动告知函确认回传

    //kd-hgic-ins-push 海峡
    public static final String KD_INS_PUSH_HX_SEND_EMPOWER_CONFIRM_ELM = "kd_ins_push_hx_send_empower_confirm_elm";     // 投保授权回传
    public static final String KD_INS_PUSH_HX_SEND_FLOAT_FEE_CONFIRM_ELM = "kd_ins_push_hx_send_float_fee_confirm_elm";     // 浮动告知函确认回传
    public static final String KD_INS_PUSH_HX_SEND_STATUS_ELM = "kd_ins_push_hx_send_status_elm";       // 理赔状态回传

    //kd-fb-ins-push 富邦
    public static final String KD_INS_PUSH_FB_SEND_EMPOWER_CONFIRM_ELM = "kd_ins_push_fb_send_empower_confirm_elm";     // 投保授权回传
    public static final String KD_INS_PUSH_FB_SEND_FLOAT_FEE_CONFIRM_ELM = "kd_ins_push_fb_send_float_fee_confirm_elm";     // 浮动告知函确认回传
    public static final String KD_INS_PUSH_FB_SEND_STATUS_ELM = "kd_ins_push_fb_send_status_elm";       // 理赔状态回传
    public static final String KD_FB_NOT_POLICY_NUMBER_SEND_EMAIL="kd_fb_not_policy_number_send_email";       //富邦无保单号定时重试

    // 延迟推送大地理赔数据
    public static final String KD_CLAIM_DATA_PUSH_CCIC_DELAY = "kd_claim_data_push_ccic_delay";
    public static final String KD_CLAIM_DATA_PUSH_CCIC_DELAY_EXCHANGE = "kd_claim_data_push_ccic_delay_exchange";
    public static final String KD_CLAIM_DATA_PUSH_CCIC_DELAY_TTL = "kd_claim_data_push_ccic_delay_ttl";
    public static final String KD_CLAIM_DATA_PUSH_CCIC_DELAY_TTL_EXCHANGE = "kd_claim_data_push_ccic_delay_ttl_exchange";

    // 延迟推送人保理赔数据
    public static final String KD_CLAIM_DATA_PUSH_PICC_DELAY = "kd_claim_data_push_picc_delay";
    public static final String KD_CLAIM_DATA_PUSH_PICC_DELAY_EXCHANGE = "kd_claim_data_push_picc_delay_exchange";
    public static final String KD_CLAIM_DATA_PUSH_PICC_DELAY_TTL = "kd_claim_data_push_picc_delay_ttl";
    public static final String KD_CLAIM_DATA_PUSH_PICC_DELAY_TTL_EXCHANGE = "kd_claim_data_push_picc_delay_ttl_exchange";

    /**
     * 延迟三库推送人保未决
     */
    public static final String KD_CLAIM_SUPPLEMENT_PUSH_PICC_DELAY = "kd_claim_supplement_push_picc_delay";
    public static final String KD_CLAIM_SUPPLEMENT_PUSH_PICC_DELAY_EXCHANGE = "kd_claim_supplement_push_picc_delay_exchange";
    public static final String KD_CLAIM_SUPPLEMENT_PUSH_PICC_DELAY_TTL = "kd_claim_supplement_push_picc_delay_ttl";
    public static final String KD_CLAIM_SUPPLEMENT_PUSH_PICC_DELAY_TTL_EXCHANGE = "kd_claim_supplement_push_picc_delay_ttl_exchange";

    // 延迟推送海峡
    public static final String KD_CLAIM_DATA_PUSH_HAIXIA_DELAY = "kd_claim_data_push_haixia_delay";
    public static final String KD_CLAIM_DATA_PUSH_HAIXIA_DELAY_EXCHANGE = "kd_claim_data_push_haixia_delay_exchange";
    public static final String KD_CLAIM_DATA_PUSH_HAIXIA_DELAY_TTL = "kd_claim_data_push_haixia_delay_ttl";
    public static final String KD_CLAIM_DATA_PUSH_HAIXIA_DELAY_TTL_EXCHANGE = "kd_claim_data_push_haixia_delay_ttl_exchange";

    // 理赔数据推送大地
    public static final String KD_CLAIM_DATA_PUSH_INSURANCE_COMPANY = "kd_claim_data_push_insurance_company";

    // 案件数据重推保司
    public static final String KD_CLAIM_DATA_PUSH_UPLOAD_IMG_INSURANCE_COMPANY = "kd_claim_data_push_upload_img_insurance_company";

    // 定时推送大地理赔数据
    public static final String KD_CLAIM_CASE_PUSH_CCIC = "kd_claim_case_push_ccic";

    public static final String KD_CLAIM_CASE_STATUS_NOTIFY = "kd_claim_case_status_notify";

    // 校验挂起是否失效
    public static final String KD_CLAIM_CASE_HANG_UP_VERIFY = "kd_claim_case_hang_up_verify";
    // 大地重复投保黑名单添加
    public static final String KD_DD_POLICY_BLACK_LIST_ADD = "kd_dd_policy_black_list_add";
    // 人保重复投保黑名单添加
    public static final String KD_RB_POLICY_BLACK_LIST_ADD = "kd_rb_policy_black_list_add";
    // 保司重复投保黑名单添加
    public static final String KD_BS_POLICY_BLACK_LIST_ADD = "kd_bs_policy_black_list_add";

    // 未决（修改估损金额）推送饿了么
    public static final String KD_CLAIM_CASE_PUSH_ELM = "kd_claim_case_push_elm";
    // 保单号修改队列
    public static final String KD_INS_POLICY_CHANGE = "kd_ins_policy_change";

    // 三库数据邮箱发送队列
    public static final String KD_CLAIM_CASE_TRIPARTITE_DATA_EXPORT = "kd_claim_case_tripartite_data_export";

    //盒马报案接口
    public static final String KD_REPORT_CASE_HM = "kd_report_case_hm";

    //e签宝
    public static final String KD_E_SIGN = "kd_e_sign";

    public static final String KD_POLICY_PUSH_RB_REAL_TIME = "kd_policy_push_rb_real_time";

    // 海峡案件状态同步
    public static final String KD_CLAIM_CASE_STATUS_NOTIFY_HGIC = "kd_claim_case_status_notify_hgic";

    public static final String KD_CLAIM_CASE_PUSH_IMG_HGIC = "kd_claim_case_push_img_hgic";

    public static final String KD_CLAIM_CASE_PUSH_IMG_HGIC_EXCHANGE="kd_claim_case_push_img_hgic_exchange";

    public static final String KD_CLAIM_CASE_PUSH_IMG_HGIC_TTL="kd_claim_case_push_img_hgic_ttl";

    public static final String KD_CLAIM_CASE_PUSH_IMG_HGIC_TTL_EXCHANGE="kd_claim_case_push_img_hgic_ttl_exchange";

    //太保本地校验入库处理
    public static final String KD_LOCAL_POLICY_DEAL_TB = "kd_local_policy_deal_tb";
    //太保刘蓉入库
    public static final String KD_POLICY_PERSON_TBLR_WAREHOUSING = "kd_policy_person_tblr_warehousing";

    //
    public static final String KD_POLICY_PERSON_SINGLE_PUSH_SCALLION = "kd_policy_person_single_push_scallion";
    public static final String KD_POLICY_PERSON_SINGLE_PUSH_SCALLION_TTL = "kd_policy_person_single_push_scallion_ttl";
    public static final String KD_POLICY_PERSON_SINGLE_PUSH_SCALLION_TTL_EXCHANGE = "kd_policy_person_single_push_scallion_ttl_exchange";
    public static final String KD_POLICY_PERSON_SINGLE_PUSH_SCALLION_EXCHANGE = "kd_policy_person_single_push_scallion_exchange";


    public static final String KD_POLICY_PERSON_BATCH_PUSH_JOB =  "kd_policy_person_batch_push_job";
    public static final String KD_POLICY_PERSON_BATCH_ASSEMBLE_DATA = "kd_policy_person_batch_assemble_data";

    public static final String KD_POLICY_PERSON_BATCH_STATUS =  "kd_policy_person_batch_status";
    public static final String KD_POLICY_PERSON_BATCH_STATUS_TTL =  "kd_policy_person_batch_status_ttl";
    public static final String KD_POLICY_PERSON_BATCH_STATUS_TTL_EXCHANGE =  "kd_policy_person_batch_status_ttl_exchange";
    public static final String KD_POLICY_PERSON_BATCH_STATUS_EXCHANGE =  "kd_policy_person_batch_status_exchange";

    public static final String KD_POLICY_PERSON_PUSH_DONE =  "kd_policy_person_push_done";
    public static final String KD_POLICY_PERSON_PUSH_DONE_TTL =  "kd_policy_person_push_done_ttl";
    public static final String KD_POLICY_PERSON_PUSH_DONE_TTL_EXCHANGE =  "kd_policy_person_push_done_ttl_exchange";
    public static final String KD_POLICY_PERSON_PUSH_DONE_EXCHANGE =  "kd_policy_person_push_done_exchange";

    public static final String KD_POLICY_PERSON_STORE = "kd_policy_person_store";

    //生成车物定损单影像信息
    public static final String KD_GEN_CASE_OBJECT_IMAGE_INFO = "kd_gen_case_object_image_info";

    /**
     * 验证是否推送人保核损
     */
    public static final String KD_CLAIM_CASE_PUSH_PICC = "kd_claim_case_push_picc";

    /**
     * 定时查询人保支付结果
     */
    public static final String KD_CLAIM_QUERY_PAY_RESULT_DELAY = "kd_claim_query_pay_result_delay";

    /**
     * 三库数据处理，将其过滤
     */
    public static final String KD_CLAIM_QUERT_THREE_DATA_FILTER = "kd_claim_query_three_data_filter";
    /**
     * 海峽未决推送
     */
    public static final String KD_REPORT_CASE_PUSH_HGIC = "kd_report_case_push_hgic";


    public static final String KD_PUSH_COMMON_PROPOSAL = "kd_push_common_proposal";
    public static final String KD_PUSH_IMAGE_UPLOAD = "kd_push_image_upload";
    public static final String KD_PUSH_IMAGE_UPLOAD_EXCHANGE = "kd_push_image_upload_exchange";
    public static final String KD_PUSH_IMAGE_UPLOAD_TTL = "kd_push_image_upload_ttl";
    public static final String KD_PUSH_IMAGE_UPLOAD_TTL_EXCHANGE = "kd_push_image_upload_ttl_exchange";

    public static final String KD_PUSH_COMMON_SUBMIT_UW = "kd_push_common_submit_uw";
    public static final String KD_PUSH_COMMON_SUBMIT_UW_EXCHANGE = "kd_push_common_submit_uw_exchange";
    public static final String KD_PUSH_COMMON_SUBMIT_UW_TTL = "kd_push_common_submit_uw_ttl";
    public static final String KD_PUSH_COMMON_SUBMIT_UW_TTL_EXCHANGE = "kd_push_common_submit_uw_ttl_exchange";


    //盒马理赔数据
    public static final String KD_EXPORT_SETTLEMENT_CLAIM_HMRB_SEND_EMAIL = "kd_export_settlement_claim_hmrb_send_email";

    //盒马理赔清单全量
    public static final String KD_EXPORT_SETTLEMENT_CLAIM_ALL_HMRB_SEND_EMAIL = "kd_export_settlement_claim_all_hmrb_send_email";

    //盒马10点定时承保数据发送
    public static final String KD_SEND_EMAIL_UNDERWRITING_DATA_HMRB_T10 = "kd_send_email_underwriting_data_hmrb_t10";
    //盒马16点定时承保数据发送
    public static final String KD_SEND_EMAIL_UNDERWRITING_DATA_HMRB_T16 = "kd_send_email_underwriting_data_hmrb_t16";

    //支付成功后推送饿了么结案
    public static final String KD_PAY_SUCCESS_PUSH_ELM = "kd_pay_success_push_elm";

    //推送签字签名数据
    public static final String SIGN_TEMPLATE_PUSH="sign_template_push";

    //京东家政
    public static final String KD_POLICY_PERSON_JDJZ_PUSH_SCALLION = "kd_policy_person_jdjz_push_scallion";
    public static final String KD_POLICY_PERSON_JDJZ_PUSH_SCALLION_TTL = "kd_policy_person_jdjzpush_scallion_ttl";
    public static final String KD_POLICY_PERSON_JDJZ_PUSH_SCALLION_TTL_EXCHANGE = "kd_policy_person_jdjz_push_scallion_ttl_exchange";
    public static final String KD_POLICY_PERSON_JDJZ_PUSH_SCALLION_EXCHANGE = "kd_policy_person_jdjz_push_scallion_exchange";
    public static final String KD_POLICY_BATCH_PUSH_RB = "kd_policy_batch_push_rb";
    public static final String KD_POLICY_BATCH_PUSH_SUPPLEMENT_RB = "kd_policy_batch_push_supplement_rb";
    public static final String KD_POLICY_PERSON_LOCAL_DEAL_SCHEDULED = "kd_policy_person_local_deal_scheduled";
    public static final String KD_POLICY_PUSH_TB = "kd_policy_push_tb";
    public static final String KD_POLICY_NO_LOCAL_DEAL_SQL_TB = "kd_policy_no_local_deal_sql_tb";
    public static final String KD_POLICY_NO_LOCAL_DEAL_SQL_TB_TTL = "kd_policy_no_local_deal_sql_tb_ttl";
    public static final String KD_POLICY_NO_LOCAL_DEAL_SQL_TB_EXCHANGE = "kd_policy_no_local_deal_sql_tb_exchange";
    public static final String KD_POLICY_NO_LOCAL_DEAL_SQL_TB_TTL_EXCHANGE = "kd_policy_no_local_deal_sql_tb_ttl_exchange";
    public static final String KD_POLICY_NO_LOCAL_DEAL_TB = "kd_policy_no_local_deal_tb";
    public static final String KD_POLICY_NO_LOCAL_DEAL_TB_TTL = "kd_policy_no_local_deal_tb_ttl";
    public static final String KD_POLICY_NO_LOCAL_DEAL_TB_EXCHANGE = "kd_policy_no_local_deal_tb_exchange";
    public static final String KD_POLICY_NO_LOCAL_DEAL_TB_TTL_EXCHANGE = "kd_policy_no_local_deal_tb_ttl_exchange";


    //批次推送结果返回
    public static final String CLAIM_CASE_PUSH_OTHER_BATCH_BACK = "claim_case_push_other_batch_back";
    public static final String CLAIM_CASE_PUSH_OTHER_BATCH_BACK_TTL = "claim_case_push_other_batch_back_ttl";
    public static final String CLAIM_CASE_PUSH_OTHER_BATCH_BACK_EXCHANGE = "claim_case_push_other_batch_back_exchange";
    public static final String CLAIM_CASE_PUSH_OTHER_BATCH_BACK_TTL_EXCHANGE = "claim_case_push_other_batch_back_ttl_exchange";


    /**
     * 人保案件下发任务推送
     */
    public static final String KD_CLAIM_CASE_DISPATCH_TASK_PUSH = "kd_claim_case_dispatch_task_push";
    public static final String KD_CLAIM_CASE_DISPATCH_TASK_PUSH_EXCHANGE = "kd_claim_case_dispatch_task_push_exchange";
    public static final String KD_CLAIM_CASE_DISPATCH_TASK_PUSH_TTL = "kd_claim_case_dispatch_task_push_ttl";
    public static final String KD_CLAIM_CASE_DISPATCH_TASK_PUSH_TTL_EXCHANGE = "kd_claim_case_dispatch_task_push_ttl_exchange";

    /**
     * 推送保司中台
     */
    public static final String KD_PUSH_INSURANCE_CENTER = "kd_push_insurance_center";
    public static final String KD_PUSH_INSURANCE_CENTER_EXCHANGE = "kd_push_insurance_center_exchange";
    public static final String KD_PUSH_INSURANCE_CENTER_TTL = "kd_push_insurance_center_ttl";
    public static final String KD_PUSH_INSURANCE_CENTER_TTL_EXCHANGE = "kd_push_insurance_center_ttl_exchange";

    /**
     * 推送精友
     */
    public static final String KD_PUSH_JY_CENTER = "kd_push_jy_center";
    public static final String KD_PUSH_JY_CENTER_EXCHANGE = "kd_push_jy_center_exchange";
    public static final String KD_PUSH_JY_CENTER_TTL = "kd_push_jy_center_ttl";
    public static final String KD_PUSH_JY_CENTER_TTL_EXCHANGE = "kd_push_jy_center_ttl_exchange";


    /**
     * 立案比例推送
     */
    public static final String KD_CLAIM_CASE_REPORT_PROPORTION = "kd_claim_case_report_proportion";

    /**
     * 定时推送特殊标签案件
     */
    public static final String KD_PUSH_SPECIAL_CASE_TIMER = "kd_push_special_case_timer";

    /**
     * 定时推送估损下降案件
     */
    public static final String KD_OBJECT_ESTIMATED_REDUCTION = "kd_object_estimated_reduction";

    /**
     * error_log数据入库队列
     */
    public static final String KD_REDIS_DICT_PLATFORM_QUEUE = "kd_redis_dict_platform_queue";

    /**
     * 定时推送海峡理算案件
     */
    public static final String KD_CLAIM_END_AUTO_PUSH_HGIC = "kd_claim_end_auto_push_hgic";

    /**
     * 三库统计-保司
     */
    public static final String KD_SYNC_CLAIM_CASE_4BS = "kd_sync_claim_case_4bs";
    /**
     * 推送保费测算表
     */
    public static final String KD_PUSH_PREMIUM_CALCULATION_QUEUE = "kd_push_premium_calculation_queue";
    public static final String KD_PUSH_PREMIUM_CALCULATION_EXCHANGE = "kd_push_premium_calculation_exchange";
    public static final String KD_PUSH_PREMIUM_CALCULATION_BINDING = "kd_push_premium_calculation_binding";

    /**
     * 获取签署链接
     */
    public static final String GET_SIGN_LINK = "get_sign_link";

    /**
     * e签宝签署完成后文件转图片
     */
    public static final String E_SIGN_PDF_TO_PHOTO = "e_sign_pdf_to_photo";

    /**
     * 案件状态流转（新增）
     */
    public static final String KD_CLAIM_CASE_STATE_TRANSITION_ADD = "kd_claim_case_state_transition_add";

    /**
     * 案件状态流转（组装）
     */
    public static final String KD_CLAIM_CASE_STATE_TRANSITION_PACK = "kd_claim_case_state_transition_pack";

    /**
     * 错误日志入库（单独给数据备份使用）
     */
    public static final String KD_PUSH_ERROR_LOG_QUEUE = "kd_push_error_log_queue";

    /**
     * 错误日志入库
     */
    public static final String KD_INSERT_ERROR_LOG = "kd_insert_error_log";

    /**
     * 告警信息推送钉钉
     */
    public static final String KD_ERROR_MSG_PUSH_DD = "kd_error_msg_push_dd";

    /**
     * 人保结案自动化推送
     */
    public static final String KD_CLAIM_END_AUTO_PUSH_PICC = "kd_claim_end_auto_push_picc";

    /**
     * 每日8:00am定时邮件推送T-1日（00:00:00-23:59:59）众安达达雇主立案的案件给保司
     */
    public static final String KD_EXPORT_CLAIM_DDZA_SEND_EMAIL_DAILY = "kd_export_claim_ddza_send_email_daily";

    public static final String KD_QR_REPORT_CASE = "KD_QR_REPORT_CASE";

    /**
     * 数据备份：报错推送钉钉
     */
    public static final String KD_BACKUP_ERROR_MSG_PUSH_DD = "kd_backup_error_msg_push_dd";

    /**
     * 数据备份：报错邮件推送
     */
    public static final String KD_BACKUP_SEND_EMAIL = "kd_backup_send_email";

    /**
     * ES数据备份：定时任务
     */
    public static final String KD_ES_BACKUP_LISTENER = "kd_es_backup_listener";

    /**
     * ES数据备份：查询es数据队列
     */
    public static final String KD_QUERY_ES_DATA_LISTENER = "kd_query_es_data_listener";


    /**
     * 数据备份：ES数据处理
     */
    public static final String KD_BACKUP_ES_DATA = "kd_backup_es_data";
    public static final String KD_BACKUP_ES_DATA_TTL = "kd_backup_es_data_ttl";
    public static final String KD_BACKUP_ES_DATA_TTL_EXCHANGE = "kd_backup_es_data_ttl_exchange";
    public static final String KD_BACKUP_ES_DATA_EXCHANGE = "kd_backup_es_data_exchange";


    /**
     * mongoDB数据备份
     */
    public static final String KD_BACKUP_MONGO_DATA = "kd_backup_mongo_data";

    /**
     * 人保标签案件推送定时任务
     */
    public static final String KD_CLAIM_LABEL_PUSH_PICC = "kd_claim_label_push_picc";

    /**
     * 海峡标签案件推送定时任务
     */
    public static final String KD_CLAIM_LABEL_PUSH_HGIC = "kd_claim_label_push_hgic";

    /**
     * 富邦标签案件推送定时任务
     */
    public static final String KD_CLAIM_LABEL_PUSH_FUBON = "kd_claim_label_push_fubon";

}
