mybatis:
  mapper-locations: classpath*:mapping/*.xml  # 对应mapper xml路径
  type-aliases-package: kd.entity  # 对应实体路径

#hystrix的超时时间
feign:
  hystrix:
    enabled: true
  client:
    config:
      default:           #服务名，填写default为所有服务
        connectTimeout: 60000
        readTimeout: 60000
#hystrix的超时时间
hystrix:
  threadpool:
    default:
      coreSize: 30 #并发执行的最大线程数，默认10
      maxQueueSize: 500 #BlockingQueue的最大队列数，默认值-1
      queueSizeRejectionThreshold: 500 #即使maxQueueSize没有达到，达到queueSizeRejectionThreshold该值后，请求也会被拒绝，默认值5
  command:
    default:
      execution:
        timeout:
          enabled: true
        isolation:
          thread:
            timeoutInMilliseconds: 13000
#ribbon的超时时间
ribbon:
  ReadTimeout: 6000
  ConnectTimeout: 6000
  MaxAutoRetries: 1 #同一台实例最大重试次数,不包括首次调用
  MaxAutoRetriesNextServer: 1 #重试负载均衡其他的实例最大重试次数,不包括首次调用
  OkToRetryOnAllOperations: false  #是否所有操作都重试

spring:
  devtools:
    restart:
      enabled: true  #设置开启热部署
  datasource:
    name: test
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    initialSize: 5
    minIdle: 5
    maxActive: 20
    maxWait: 60000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 300000
    validationQuery: SELECT 1 FROM DUAL
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
    poolPreparedStatements: true
    #   配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
    filters: stat,wall,log4j
    maxPoolPreparedStatementPerConnectionSize: 20
    useGlobalDataSourceStat: true
    connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=500
  freemarker:
    cache: false
    content-type: text/html; charset=UTF-8
    expose-request-attributes: true
    expose-session-attributes: true
    expose-spring-macro-helpers: true
    request-context-attribute: request
    suffix: .html
    charset: utf-8
    template-loader-path: classpath:/templates/
    settings:
      classic_compatible: true
      whitespace_stripping: true
      template_update_delay: 1
      locale: zh_CN
      default_encoding: UTF-8
      url_escaping_charset: UTF-8
      output_encoding: UTF-8
      date_format: yyyy-MM-dd
      time_format: HH:mm:ss
      tag_syntax: auto_detect
      datetime_format: yyyy-MM-dd
      number_format: 0.##
      object_wrapper: freemarker.ext.beans.BeansWrapper
      auto_import: /template/sysComponent.html as sc
  rabbitmq:
    host: *************
    port: 5672 #15672是Web管理界面的端口；5672是MQ访问的端口。
    username: pater
    password: Abc123
    listener:
      simple:
        retry:
          enabled: true #是否支持重试,默认为true开启
          max-attempts: 5
        acknowledge-mode: manual #消息确认模式即应答模式，auto自动确认，manual手动确认,默认none无ack默认（就是消息不需要确认）

logging:
  config: classpath:conf/logback-dev.xml
  showDetailMes: true
debug: true

es:
  defaultAppEnv: "statistics"
  data:
    - appEnv: "data"
      username: "elastic"
      password: "elastic"
      data:
        - host: ************
          port: 9200
          scheme: http
    - appEnv: "statistics"
      username: "elastic"
      password: "elastic"
      data:
        - host: ************
          port: 9200
          scheme: http

redis:
  host: *************
  port: 6379
  password: 123
  maxTotal: 100
  maxIdle: 100
  maxWaitMillis: 10000
  timeout: 10000
  testOnBorrow: true
  project:
    prefix: kd-cuisine-

mail:
  code: kd
  defaultCode: kd
  data:
    - code: kd
      protocol: smtp
      host: smtp.qiye.163.com
      auth: true
      enable: true
      sslPort: 465
      defaultPort: 25
      username: <EMAIL>
      password: Q6ahPuDjzsLaVeaK
      cc:
      onOff: true
      debug: true
