package kd.beijingRoastDuck.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import kd.beijingRoastDuck.controller.ClaimCaseController;
import kd.beijingRoastDuck.controller.DownLoadCenterController;
import kd.beijingRoastDuck.dao.*;
import kd.beijingRoastDuck.service.ClaimCaseObjectAssessmentService;
import kd.beijingRoastDuck.service.ClaimCaseObjectService;
import kd.beijingRoastDuck.service.ClaimCaseService;
import kd.beijingRoastDuck.service.ManagerService;
import kd.beijingRoastDuck.support.Assessment;
import kd.beijingRoastDuck.util.DateUtils;
import kd.beijingRoastDuck.util.Excel2ImageUtil;
import kd.beijingRoastDuck.util.SearchPrefix;
import kd.beijingRoastDuck.util.Tool;
import kd.beijingRoastDuck.vo.*;
import kd.common.context.RedisCommonKeyEnum;
import kd.common.pager.PageParam;
import kd.common.tool.AliOssToolV3;
import kd.common.tool.ESHighLevelRESTClientTool;
import kd.common.tool.JsonTool;
import kd.common.tool.RedisTool3;
import kd.entity.*;
import kd.main.common.*;
import kd.main.util.ConvertUpMoneyTool;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.ParsedSum;
import org.elasticsearch.search.aggregations.metrics.TopHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;

import javax.imageio.ImageIO;
import javax.management.Query;
import java.awt.*;
import java.awt.Color;
import java.awt.Font;
import java.awt.image.BufferedImage;
import java.io.*;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;
import static kd.beijingRoastDuck.util.AliOssBucketPrefix.KD_DUCK_INTERNAL;
import static kd.beijingRoastDuck.util.AliOssBucketPrefix.valueOf;

@Service
@Transactional
public class ClaimCaseObjectServiceImpl implements ClaimCaseObjectService {

    private final static org.slf4j.Logger logger = LoggerFactory.getLogger(ClaimCaseObjectServiceImpl.class.getName());

    @Autowired
    private ClaimCaseObjectDao claimCaseObjectDao;

    @Autowired
    private ClaimCaseObjectAssessmentDao claimCaseObjectAssessmentDao;

    @Autowired
    private ClaimCaseLogDao claimCaseLogDao;

    @Autowired
    private TrManagerRoleDao trManagerRoleDao;

    @Autowired
    private RoleDao roleDao;

    @Autowired
    private ClaimCaseObjectAssessmentService claimCaseObjectAssessmentService;

    @Autowired
    private ClaimCaseAttachDao claimCaseAttachDao;

    @Autowired
    private ClaimCaseService claimCaseService;

    @Autowired
    private ManagerService managerService;

    @Override
    public List<ClaimCaseObject> findByParam(Map<String, Object> paramMap) {

        if (paramMap.keySet().size() == 0) {
            return null;
        }

        List<ClaimCaseObject> claimCaseObjectList = new ArrayList<>();

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);
        searchSourceBuilder.sort("createTime", SortOrder.DESC);
        BoolQueryBuilder boolBuilder = new BoolQueryBuilder();

        if (paramMap.containsKey("claimCaseId")) {
            boolBuilder.must(QueryBuilders.termQuery("claimCaseId.keyword", paramMap.get("claimCaseId")));
        }

        if (paramMap.containsKey("idList")) {
            boolBuilder.must(QueryBuilders.termsQuery("id.keyword", (ArrayList<String>) paramMap.get("idList")));
        }

        if (paramMap.containsKey("name")) {
            boolBuilder.must(QueryBuilders.wildcardQuery("name.keyword", "*" + paramMap.get("name") + "*"));
        }

        if (paramMap.containsKey("mobile")) {
            boolBuilder.must(QueryBuilders.wildcardQuery("mobile.keyword", "*" + paramMap.get("mobile") + "*"));
        }

        if (paramMap.containsKey("carNumber")) {
            boolBuilder.must(QueryBuilders.wildcardQuery("carNumber.keyword", "*" + paramMap.get("carNumber") + "*"));
        }


        searchSourceBuilder.query(boolBuilder);

        SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
        SearchHits searchHits = searchResponse.getHits();
        Long searchHitsValue = searchHits.getTotalHits().value;
        int total = searchHitsValue.intValue();
        if (total == 0) {
            return null;
        }
        searchSourceBuilder.size(total);

        searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
        searchHits = searchResponse.getHits();
        SearchHit[] hits = searchHits.getHits();

        for (SearchHit searchHit : hits) {
            ClaimCaseObject claimCaseObject = JSON.parseObject(searchHit.getSourceAsString(), ClaimCaseObject.class);
            claimCaseObjectList.add(claimCaseObject);
        }

        return claimCaseObjectList;
    }


    @Override
    public ClaimCaseObject selectByPrimaryKey(String id) {
        return claimCaseObjectDao.selectByPrimaryKey(id);
    }

    @Override
    public List<ClaimCaseObject> selectByClaimCaseId(String claimCaseId) {
        if (StringUtils.isBlank(claimCaseId)) {
            return null;
        }

        List<ClaimCaseObject> claimCaseObjectList = new ArrayList<>();

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);
        BoolQueryBuilder boolBuilder = new BoolQueryBuilder();
        boolBuilder.must(QueryBuilders.termQuery("claimCaseId.keyword", claimCaseId));

        searchSourceBuilder.query(boolBuilder);

        SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
        SearchHits searchHits = searchResponse.getHits();
        Long searchHitsValue = searchHits.getTotalHits().value;
        int total = searchHitsValue.intValue();
        if (total == 0) {
            return null;
        }
        searchSourceBuilder.size(total);

        searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
        searchHits = searchResponse.getHits();
        SearchHit[] hits = searchHits.getHits();

        for (SearchHit searchHit : hits) {
            ClaimCaseObject claimCaseObject = JSON.parseObject(searchHit.getSourceAsString(), ClaimCaseObject.class);
            claimCaseObjectList.add(claimCaseObject);
        }

        return claimCaseObjectList;
    }

    @Override
    public List<ClaimCaseObject> findByParam01(Map<String, Object> paramMap) {

        if (paramMap.keySet().size() == 0) {
            return null;
        }

        List<ClaimCaseObject> claimCaseObjectList = new ArrayList<>();

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);
        searchSourceBuilder.sort("createTime", SortOrder.DESC);
        BoolQueryBuilder boolBuilder = new BoolQueryBuilder();

        if (paramMap.containsKey("claimCaseId")) {
            boolBuilder.must(QueryBuilders.termQuery("claimCaseId.keyword", paramMap.get("claimCaseId")));
        }

        if (paramMap.containsKey("idList")) {
            boolBuilder.must(QueryBuilders.termsQuery("id.keyword", (ArrayList<String>) paramMap.get("idList")));
        }

        if (paramMap.containsKey("name")) {
            boolBuilder.must(QueryBuilders.wildcardQuery("name.keyword", "*" + paramMap.get("name") + "*"));
        }

        if (paramMap.containsKey("mobile")) {
            boolBuilder.must(QueryBuilders.wildcardQuery("mobile.keyword", "*" + paramMap.get("mobile") + "*"));
        }

        if (paramMap.containsKey("carNumber")) {
            boolBuilder.must(QueryBuilders.wildcardQuery("carNumber.keyword", "*" + paramMap.get("carNumber") + "*"));
        }

        searchSourceBuilder.query(boolBuilder);

//        SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
//        SearchHits searchHits = searchResponse.getHits();
//        Long searchHitsValue = searchHits.getTotalHits().value;
//        int total = searchHitsValue.intValue();
//        if (total == 0) {
//            return null;
//        }else if(total>101){
//            total=101;
//        }
        searchSourceBuilder.size(100);

        SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
        SearchHits searchHits = searchResponse.getHits();
        SearchHit[] hits = searchHits.getHits();

        for (SearchHit searchHit : hits) {
            ClaimCaseObject claimCaseObject = JSON.parseObject(searchHit.getSourceAsString(), ClaimCaseObject.class);
            claimCaseObjectList.add(claimCaseObject);
        }

        return claimCaseObjectList;
    }


    @Override
    public int updateByPrimaryKeySelective(ClaimCaseObject claimCaseObject) {
        return claimCaseObjectDao.updateByPrimaryKeySelective(claimCaseObject);
    }

    @Override
    public int updateByPrimaryKeySelectiveAuditerCanNull(ClaimCaseObject claimCaseObject) {
        return claimCaseObjectDao.updateByPrimaryKeySelectiveAuditerCanNull(claimCaseObject);
    }

    @Override
    public List<ClaimCaseObject> findByUnique(Map<String, Object> paramMap) {

        if (paramMap.keySet().size() == 0) {
            return null;
        }

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);
        BoolQueryBuilder boolBuilder = new BoolQueryBuilder();

        if (paramMap.containsKey("claimCaseId")) {
            boolBuilder.must(QueryBuilders.termQuery("claimCaseId.keyword", paramMap.get("claimCaseId")));
        }

        if (paramMap.containsKey("treatName")) {
            boolBuilder.must(QueryBuilders.termQuery("treatName.keyword", paramMap.get("treatName")));
        }

        if (paramMap.containsKey("treatIdNum")) {
            boolBuilder.must(QueryBuilders.termQuery("treatIdNum.keyword", paramMap.get("treatIdNum")));
        }

        if (paramMap.containsKey("type")) {
            boolBuilder.must(QueryBuilders.termQuery("type", paramMap.get("type")));
        }

        if (paramMap.containsKey("category")) {
            boolBuilder.must(QueryBuilders.termQuery("category", paramMap.get("category")));
        }

        if (paramMap.containsKey("carNumber")) {
            boolBuilder.must(QueryBuilders.termQuery("carNumber.keyword", paramMap.get("carNumber")));
        }

        if (paramMap.containsKey("carEncoding")) {
            boolBuilder.must(QueryBuilders.termQuery("carEncoding.keyword", paramMap.get("carEncoding")));
        }

        if (paramMap.containsKey("name")) {
            boolBuilder.must(QueryBuilders.termQuery("name.keyword", paramMap.get("name")));
        }

        if (paramMap.containsKey("mobile")) {
            boolBuilder.must(QueryBuilders.termQuery("mobile.keyword", paramMap.get("mobile")));
        }

        if (paramMap.containsKey("carNumber")) {
            boolBuilder.must(QueryBuilders.termQuery("carNumber.keyword", paramMap.get("carNumber")));
        }


        searchSourceBuilder.query(boolBuilder);
        SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
        SearchHits searchHits = searchResponse.getHits();


        Long totalHits = searchHits.getTotalHits().value;
        Integer total = totalHits.intValue();
        // 再次查询查询，全数据返回
        searchSourceBuilder.clearRescorers();
        searchSourceBuilder.from(0);
        searchSourceBuilder.size(total);
        searchSourceBuilder.query(boolBuilder);
        searchSourceBuilder.sort("createTime", SortOrder.DESC);
        searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
        searchHits = searchResponse.getHits();

        if (searchHits.getTotalHits().value == 0) {
            return new ArrayList<>();
        } else {
            SearchHit[] hits = searchHits.getHits();
            List<ClaimCaseObject> claimCaseObjectList = new ArrayList<>();
            for (SearchHit searchHit : hits) {
                claimCaseObjectList.add(JSON.parseObject(searchHit.getSourceAsString(), ClaimCaseObject.class));
            }
            return claimCaseObjectList;
        }

    }

    @Override
    public int updateEstimatedOverallLoss(List<ClaimCaseObject> modifyClaimCaseObjectList, List<ClaimCaseLog> modifyDataLog) {
        if (CollectionUtils.isNotEmpty(modifyClaimCaseObjectList)) {
            modifyClaimCaseObjectList.stream().forEach(claimCaseObject -> {
                claimCaseObjectDao.updateByPrimaryKeySelective(claimCaseObject);
            });
        }
        if (CollectionUtils.isNotEmpty(modifyDataLog)) {
            modifyDataLog.stream().forEach(claimCaseLog -> {
                claimCaseLogDao.insertSelective(claimCaseLog);
            });
        }
        return 1;
    }

    @Override
    public List<ClaimCaseObject> findByCaseIdWithoutAuditer(Map<String, Object> paramMap) {
        if (paramMap.keySet().size() == 0) {
            return null;
        }

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);
        BoolQueryBuilder boolBuilder = new BoolQueryBuilder();

        if (paramMap.containsKey("claimCaseId")) {
            boolBuilder.must(QueryBuilders.termQuery("claimCaseId.keyword", paramMap.get("claimCaseId")));
        }

        // 为分配审核人员
        boolBuilder.mustNot(QueryBuilders.existsQuery("auditer"));

        searchSourceBuilder.query(boolBuilder);
        SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
        SearchHits searchHits = searchResponse.getHits();


        Long totalHits = searchHits.getTotalHits().value;
        Integer total = totalHits.intValue();
        // 再次查询查询，全数据返回
        searchSourceBuilder.clearRescorers();
        searchSourceBuilder.from(0);
        searchSourceBuilder.size(total);
        searchSourceBuilder.query(boolBuilder);
        searchSourceBuilder.sort("createTime", SortOrder.DESC);
        searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
        searchHits = searchResponse.getHits();

        if (searchHits.getTotalHits().value == 0) {
            return new ArrayList<>();
        } else {
            SearchHit[] hits = searchHits.getHits();
            List<ClaimCaseObject> claimCaseObjectList = new ArrayList<>();
            for (SearchHit searchHit : hits) {
                claimCaseObjectList.add(JSON.parseObject(searchHit.getSourceAsString(), ClaimCaseObject.class));
            }
            return claimCaseObjectList;
        }
    }

    @Override
    public Map<String, Integer> findAuditerDoingCountMap() {
        String aggsName = "auditer";
        String topName = "top";

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);
        searchSourceBuilder.size(0);
        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();

        // 已分配审核人员
        boolBuilder.must(QueryBuilders.existsQuery("auditer"));

        boolBuilder.must(QueryBuilders.termQuery("isCaseClosed", 0));

        // 进行中
        List<String> completeStatus = new ArrayList<>();
        completeStatus.add(ClaimCaseObjectStatusEnum.BAX99.getCode());// 通过
        boolBuilder.mustNot(QueryBuilders.termsQuery("status.keyword", completeStatus));

        searchSourceBuilder.query(boolBuilder);

        List<String> keysArray = new ArrayList<String>();

        Method[] methods = ClaimCaseObject.class.getMethods();
        try {
            for (Method method : methods) {
                String methodName = method.getName();
                //反射获取属性与属性值的方法很多，以下是其一；也可以直接获得属性，不过获取的时候需要用过设置属性私有可见
                if (methodName.contains("get")) {
                    //根据setXXXX 通过以下算法取得属性名称
                    String key = methodName.substring(methodName.indexOf("get") + 3);
                    String temp = key.substring(0, 1).toString().toLowerCase();
                    key = key.substring(1);
                    //最终得到属性名称
                    key = temp + key;
                    if ("class".equals(key)) {
                        continue;
                    }
                    keysArray.add(key);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 分组查询，获取分组总数
        searchSourceBuilder.aggregation(AggregationBuilders.terms(aggsName).field("auditer.keyword").size(1000)
                .subAggregation(AggregationBuilders.topHits(topName).size(1)
                        .sort("createTime", SortOrder.DESC)
                        .fetchSource(keysArray.toArray(new String[keysArray.size()]), null)));

        // 查询聚合根据baseUserId分组总数
        SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));

        // 获取聚合结果aggregations
        Aggregations aggregations = searchResponse.getAggregations();


        Aggregation aggregation = aggregations.get(aggsName);// auditer 部分
        ParsedStringTerms stringTerms = (ParsedStringTerms) aggregation;

        List<ClaimCaseObject> objectList = new ArrayList<>();
        Map<String, Integer> countMap = new HashMap<>();
        // 循环遍历得到topHits中的数据
        for (Terms.Bucket bucket : stringTerms.getBuckets()) {
            TopHits tophits = bucket.getAggregations().get(topName);// 获取高亮
            SearchHits tophitsHits = tophits.getHits();
            for (SearchHit searchHit : tophitsHits) {
                ClaimCaseObject claimCaseObject = JSON.parseObject(searchHit.getSourceAsString(), ClaimCaseObject.class);
                countMap.put(claimCaseObject.getAuditer(), (int) bucket.getDocCount());
            }
        }

        return countMap;
    }

    @Override
    public PageInfo<ClaimCaseObjectReq> claimCaseObjectList(Map<String, Object> paramMap, PageParam pp) {

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);
        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();

        Integer comeFrom = Integer.parseInt(paramMap.get("comeFrom").toString());

        List<String> redisCaseIdList = new ArrayList<>();

        // 处理岗（客服岗）
        if (paramMap.containsKey("insuranceCaseNo")) {
            String searchId = DigestUtils.md5DigestAsHex(JsonTool.genByFastJson(paramMap).getBytes(StandardCharsets.UTF_8));
            //赔付对象开始任务
            String redisKey = SearchPrefix.genFullObject(SearchPrefix.KD_CLAIM_CASE_OBJECT_START_AUDIT, searchId);

            if (!RedisTool3.exists(redisKey)) {
                int size = 1000;        // 滚动长度
                // 报案号
                if (paramMap.containsKey("claimCaseNo")) {
                    boolBuilder.must(QueryBuilders.wildcardQuery("claimCaseNo.keyword", "*" + paramMap.get("claimCaseNo") + "*"));
                }
                // 报案号
                if (paramMap.containsKey("insuranceCaseNo")) {
                    boolBuilder.must(QueryBuilders.wildcardQuery("insuranceCaseNo.keyword", "*" + paramMap.get("insuranceCaseNo") + "*"));
                }
                // 保司标识
                if (paramMap.containsKey("insCode")) {
                    boolBuilder.must(QueryBuilders.termQuery("insCode.keyword", paramMap.get("insCode")));
                }

                searchSourceBuilder.size(size);
                searchSourceBuilder.query(boolBuilder);
                SearchResponse searchResponse = ESHighLevelRESTClientTool.scrollSearchInit(ElasticIndexName.CLAIM_CASE, searchSourceBuilder);
                SearchHits searchHits = searchResponse.getHits();
                String _scroll_id = "";
                if (searchHits.getTotalHits().value == 0l) {
                } else {
                    _scroll_id = searchResponse.getScrollId();
                    SearchHit[] hits = searchHits.getHits();
                    for (SearchHit searchHit : hits) {
                        ClaimCase claimCase = JSON.parseObject(searchHit.getSourceAsString(), ClaimCase.class);
                        redisCaseIdList.add(claimCase.getId());
                    }
                }
                while (true) {
                    if (StringUtils.isNotBlank(_scroll_id)) {
                        searchResponse = ESHighLevelRESTClientTool.scrollSearchSyn(_scroll_id);
                        searchHits = searchResponse.getHits();
                        if (searchHits.getTotalHits().value == 0l || searchHits.getHits().length == 0) {
                            List<String> scrollIdList = new ArrayList<>();
                            scrollIdList.add(_scroll_id);
                            ESHighLevelRESTClientTool.clearScollSearchSyn(ESHighLevelRESTClientTool.genClearScollSearchReq(scrollIdList));
                            _scroll_id = null;
                        } else {
                            _scroll_id = searchResponse.getScrollId();
                            SearchHit[] hits = searchHits.getHits();
                            for (SearchHit searchHit : hits) {
                                ClaimCase claimCase = JSON.parseObject(searchHit.getSourceAsString(), ClaimCase.class);
                                redisCaseIdList.add(claimCase.getId());
                            }
                        }
                    } else {
                        break;
                    }
                }
                if (CollectionUtils.isNotEmpty(redisCaseIdList)) {
                    RedisTool3.rpush(redisKey, redisCaseIdList.toArray(new String[redisCaseIdList.size()]));
                    RedisTool3.expire(redisKey, 3 * 60);        // Redis存活3分钟
                }
                boolBuilder = new BoolQueryBuilder();
                searchSourceBuilder.clearRescorers();
            } else {
                Long totalHits = RedisTool3.llen(redisKey);
                redisCaseIdList = RedisTool3.lrange(redisKey, 0, totalHits);
            }
        }

        searchSourceBuilder.trackTotalHits(true);
        searchSourceBuilder.from(pp.getPageNum() != 0 ? (pp.getPageNum() - 1) * pp.getPageSize() : 0);
        searchSourceBuilder.size(pp.getPageSize());
        searchSourceBuilder.sort("createTime", SortOrder.DESC);

        // 客服岗 或 预处理岗
        if (comeFrom == 1 || comeFrom == 4) {
            boolBuilder.must(QueryBuilders.termQuery("auditer.keyword", paramMap.get("userId")));
        }

        if (paramMap.containsKey("status")) {

            boolBuilder.must(QueryBuilders.termQuery("isCaseClosed", 0));

            Integer status = Integer.parseInt(paramMap.get("status").toString());

            // 采集任务
            if (status == 0) {
                boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX21.getCode()));
            }
            // 理算任务
            if (status == 1) {
                boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX31.getCode()));
            }
            // 退回任务
            if (status == 2) {
                BoolQueryBuilder statusQueryBuilder = new BoolQueryBuilder();
                statusQueryBuilder.should(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX24.getCode()));
                statusQueryBuilder.should(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX34.getCode()));
                statusQueryBuilder.should(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX27.getCode()));
                statusQueryBuilder.should(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX37.getCode()));
                boolBuilder.must(statusQueryBuilder);
            }
            // 处理岗
            if (comeFrom == 2) {
                // 采集审核(个人池)
                if (status == 3) {
                    boolBuilder.must(QueryBuilders.termQuery("checkAuditer.keyword", paramMap.get("userId")));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX22.getCode()));
                }
                // 理算审核(个人池)
                if (status == 4) {
                    boolBuilder.must(QueryBuilders.termQuery("checkAuditer.keyword", paramMap.get("userId")));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX32.getCode()));
                }
                // 采集审核(公共池)
                if (status == 5) {
                    boolBuilder.mustNot(QueryBuilders.existsQuery("checkAuditer"));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX22.getCode()));
                }
                // 理算审核(公共池)
                if (status == 6) {
                    boolBuilder.mustNot(QueryBuilders.existsQuery("checkAuditer"));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX32.getCode()));
                }
            }
            // 保司岗
            if (comeFrom == 3) {
                // 采集审核(个人池)
                if (status == 3) {
                    boolBuilder.must(QueryBuilders.termQuery("insAuditer.keyword", paramMap.get("userId")));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX25.getCode()));
                }
                // 理算审核(个人池)
                if (status == 4) {
                    boolBuilder.must(QueryBuilders.termQuery("insAuditer.keyword", paramMap.get("userId")));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX35.getCode()));
                }
                // 采集审核(公共池)
                if (status == 5) {
                    boolBuilder.mustNot(QueryBuilders.existsQuery("insAuditer"));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX25.getCode()));
                }
                // 理算审核(公共池)
                if (status == 6) {
                    boolBuilder.mustNot(QueryBuilders.existsQuery("insAuditer"));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX35.getCode()));
                }
            }
            //新增签字任务
            if(status == 7){
                BoolQueryBuilder statusQueryBuilder = new BoolQueryBuilder();
                statusQueryBuilder.should(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX38.getCode()));
                statusQueryBuilder.should(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX39.getCode()));
                boolBuilder.must(statusQueryBuilder);
                boolBuilder.must(QueryBuilders.termQuery("auditer.keyword", paramMap.get("userId")));
            }
        }

        // 报案号
        if (paramMap.containsKey("claimCaseNo")) {
            boolBuilder.must(QueryBuilders.wildcardQuery("claimCaseNo.keyword", "*" + paramMap.get("claimCaseNo") + "*"));
        }

        // 立案号
        if (paramMap.containsKey("insuranceCaseNo")) {
            boolBuilder.must(QueryBuilders.termsQuery("claimCaseId.keyword", redisCaseIdList));     // 提取缓存数据查询
        }

        // 被保人姓名
        if (paramMap.containsKey("treatName")) {
            boolBuilder.must(QueryBuilders.wildcardQuery("treatName.keyword", "*" + paramMap.get("treatName") + "*"));
        }

        // 被保人证件号
        if (paramMap.containsKey("treatIdNum")) {
            boolBuilder.must(QueryBuilders.wildcardQuery("treatIdNum.keyword", "*" + paramMap.get("treatIdNum") + "*"));
        }

        // 类型
        if (paramMap.containsKey("type")) {
            boolBuilder.must(QueryBuilders.termQuery("type", paramMap.get("type")));
        }

        // 类型
        if (paramMap.containsKey("category")) {
            boolBuilder.must(QueryBuilders.termQuery("category", paramMap.get("category")));
        }

        // 三者姓名
        if (paramMap.containsKey("name")) {
            boolBuilder.must(QueryBuilders.wildcardQuery("name.keyword", "*" + paramMap.get("name") + "*"));
        }

        // 三者手机号
        if (paramMap.containsKey("mobile")) {
            boolBuilder.must(QueryBuilders.wildcardQuery("mobile.keyword", "*" + paramMap.get("mobile") + "*"));
        }

        // 车牌号
        if (paramMap.containsKey("carNumber")) {
            boolBuilder.must(QueryBuilders.wildcardQuery("carNumber.keyword", "*" + paramMap.get("carNumber") + "*"));
        }

        // 保司标识
        if (paramMap.containsKey("insCode")) {
            if (paramMap.get("insCode").toString().equals("DD")) {
                BoolQueryBuilder shouldQueryBuilder = new BoolQueryBuilder();
                shouldQueryBuilder.should(QueryBuilders.termQuery("insCode.keyword", paramMap.get("insCode")));
                shouldQueryBuilder.should(new BoolQueryBuilder().mustNot(QueryBuilders.existsQuery("insCode")));
                boolBuilder.must(shouldQueryBuilder);
            } else {
                boolBuilder.must(QueryBuilders.termQuery("insCode.keyword", paramMap.get("insCode")));
            }
        }

        searchSourceBuilder.query(boolBuilder);
        SearchSourceBuilder searchSourceBuilderModifyTime = new SearchSourceBuilder();
        searchSourceBuilderModifyTime.query(boolBuilder);
        searchSourceBuilderModifyTime.trackTotalHits(true);
        searchSourceBuilderModifyTime.from(pp.getPageNum() != 0 ? (pp.getPageNum() - 1) * pp.getPageSize() : 0);
        searchSourceBuilderModifyTime.size(pp.getPageSize());
        searchSourceBuilderModifyTime.sort("modifyTime", SortOrder.DESC);
        SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilderModifyTime));
        SearchHits searchHits = searchResponse.getHits();
        SearchHit[] hits = searchHits.getHits();
        List<ClaimCaseObjectReq> claimCaseObjectList = new ArrayList<>();
        List<String> claimCaseIdList = new ArrayList<>();
        for (SearchHit searchHit : hits) {

            ClaimCaseObjectReq claimCaseObjectReq = JSON.parseObject(searchHit.getSourceAsString(), ClaimCaseObjectReq.class);
//            claimCaseObject.setLabel(null);
            claimCaseIdList.add(claimCaseObjectReq.getClaimCaseId());

            //根据status判断三者车损是否要使用精友的新页面


            claimCaseObjectList.add(claimCaseObjectReq);
        }

        boolBuilder = QueryBuilders.boolQuery();
        boolBuilder.must(QueryBuilders.termsQuery("id.keyword", claimCaseIdList));
//        if (paramMap.containsKey("label")) {
//            boolBuilder.must(QueryBuilders.wildcardQuery("label.keyword", "*" + paramMap.get("label") + "*"));
//        }
        searchSourceBuilder.clearRescorers();
        searchSourceBuilder.from(0);
        searchSourceBuilder.size(pp.getPageSize());
        searchSourceBuilder.query(boolBuilder);
        searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE, searchSourceBuilder));
        SearchHits otherHits = searchResponse.getHits();
        hits = otherHits.getHits();
        Map<String, ClaimCase> claimCaseMap = new HashMap<>();
        for (SearchHit searchHit : hits) {
            ClaimCase claimCase = JSON.parseObject(searchHit.getSourceAsString(), ClaimCase.class);
            claimCaseMap.put(claimCase.getId(), claimCase);
        }
        String taggerCarTime = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_CONFIG_PREFIX.toString(), "TAGGER_CAR_TIME");
        if(StringUtils.isBlank(taggerCarTime)){
            taggerCarTime = "2025-08-01 00:00:00";
        }
        String finalTaggerCarTime = taggerCarTime;

        String allowUserId = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_CONFIG_PREFIX.toString(), "TAGGER_CAR_ALLOW_USER_ID");

        // 三者车损是否使用精友新页面状态
        List<String> isLossAssessmentNewStatus = new ArrayList<>();
        isLossAssessmentNewStatus.add(ClaimCaseObjectStatusEnum.BAX21.getCode());
        isLossAssessmentNewStatus.add(ClaimCaseObjectStatusEnum.BAX24.getCode());
        isLossAssessmentNewStatus.add(ClaimCaseObjectStatusEnum.BAX27.getCode());
        isLossAssessmentNewStatus.add(ClaimCaseObjectStatusEnum.BAX31.getCode());
        isLossAssessmentNewStatus.add(ClaimCaseObjectStatusEnum.BAX34.getCode());
        isLossAssessmentNewStatus.add(ClaimCaseObjectStatusEnum.BAX37.getCode());

        claimCaseObjectList.stream().forEach(claimCaseObjectReq -> {
            ClaimCase claimCase = claimCaseMap.get(claimCaseObjectReq.getClaimCaseId());
            if (claimCase != null) {
                claimCaseObjectReq.setInsuranceCaseNo(claimCase.getInsuranceCaseNo());
                claimCaseObjectReq.setStartDate(claimCase.getStartDate());
                claimCaseObjectReq.setTreatDate(claimCase.getTreatDate());
                claimCaseObjectReq.setLabel(claimCase.getLabel());
                claimCaseObjectReq.setCaseCreateTime(claimCase.getCreateTime());
                claimCaseObjectReq.setProvince(claimCase.getProvince());

                if (claimCaseObjectReq.getType() == 2
                        && claimCaseObjectReq.getCategory() == 3
                        && claimCaseObjectReq.getCreateTime().getTime() > DateUtils.parse(finalTaggerCarTime, DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS).getTime()) {
                    if (isLossAssessmentNewStatus.contains(claimCaseObjectReq.getStatus())) {
                        // 2025/07/29 17:09新增：只有特定人员才能使用精友三者车损
                        if(StringUtils.isBlank(allowUserId) || allowUserId.contains((String)paramMap.get("userId"))){
                            claimCaseObjectReq.setIsLossAssessmentNew(1);
                        }
                    }
                }
            }
        });

//        if (paramMap.containsKey("label")) {
//            claimCaseObjectList = claimCaseObjectList.stream().filter(s->StringUtils.isNotBlank(s.getLabel())).collect(Collectors.toList());
//        }

        // 预处理岗
        if (comeFrom == 4) {
            List<String> managerIdList = new ArrayList<>();
            String roleId = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "PRETREATMENT_ROLE", "ROLE_ID");
            Role role = roleDao.selectByPrimaryKey(roleId);
            if (role != null) {
                boolBuilder = QueryBuilders.boolQuery();
                managerIdList.addAll(trManagerRoleDao.findByRoleId(roleId));
                BoolQueryBuilder shouldQueryBuilder = new BoolQueryBuilder();
                for (String managerId : managerIdList) {
                    shouldQueryBuilder.should(QueryBuilders.wildcardQuery("creator.keyword", "*" + managerId));
                }
                boolBuilder.must(shouldQueryBuilder);
                boolBuilder.must(QueryBuilders.termsQuery("claimCaseId.keyword", claimCaseIdList));
                boolBuilder.must(QueryBuilders.termQuery("position.keyword", role.getName()));
                boolBuilder.must(QueryBuilders.termQuery("type", ClaimCaseLogTypeEnum.客服沟通备注记录.getCode()));
                String aggsName = "claimCaseLog";
                String topName = "top";
                searchSourceBuilder.clearRescorers();
                searchSourceBuilder.from(0);
                searchSourceBuilder.size(0);
                searchSourceBuilder.query(boolBuilder);
                searchSourceBuilder.aggregation(AggregationBuilders.terms(aggsName).field("claimCaseId.keyword").size(pp.getPageSize())
                        .subAggregation(AggregationBuilders.topHits(topName).sort("createTime", SortOrder.DESC).size(1)));
                searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_LOG, searchSourceBuilder));
                // 获取聚合结果aggregations
                Aggregations aggregations = searchResponse.getAggregations();

                Aggregation aggregation = aggregations.get(aggsName);   // claimCaseLog 部分
                ParsedStringTerms stringTerms = (ParsedStringTerms) aggregation;

                // 循环遍历得到topHits中的数据
                for (Terms.Bucket bucket : stringTerms.getBuckets()) {
                    TopHits tophits = bucket.getAggregations().get(topName);// 获取高亮
                    SearchHits tophitsHits = tophits.getHits();
                    for (SearchHit searchHit : tophitsHits) {
                        ClaimCaseLog claimCaseLog = JSON.parseObject(searchHit.getSourceAsString(), ClaimCaseLog.class);
                        for (ClaimCaseObjectReq claimCaseObjectReq : claimCaseObjectList) {
                            if (claimCaseObjectReq.getClaimCaseId().equals(claimCaseLog.getClaimCaseId())) {
                                claimCaseObjectReq.setProcessingTime(claimCaseLog.getCreateTime());
                                claimCaseObjectReq.setProcessingDescription(claimCaseLog.getDescription());
                            }
                        }
                    }
                }
            }
        }

        if(paramMap.containsKey("status")){
            Integer status = Integer.parseInt(paramMap.get("status").toString());

            //如果是签字状态的化
            if(status == 7){
                //查询已经发起的总数和完成数
                List<String> claimCaseObjectIdList = claimCaseObjectList.stream().map(ClaimCaseObjectReq::getId).collect(Collectors.toList());
                boolBuilder = QueryBuilders.boolQuery();
                boolBuilder.must(QueryBuilders.termsQuery("claimCaseObjectId.keyword",claimCaseObjectIdList));
                boolBuilder.mustNot(QueryBuilders.termQuery("status",-1));
                searchSourceBuilder.clearRescorers();
                searchSourceBuilder.size(1000);
                searchSourceBuilder.query(boolBuilder);

                searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.SIGN_TEMPLATE_PUSH_LOG, searchSourceBuilder));
                SearchHits searchHits1 = searchResponse.getHits();
                hits = searchHits1.getHits();
                Map<String,Integer> sumPush = new HashMap<>();
                Map<String,Integer> successPush = new HashMap<>();
                for (SearchHit searchHit : hits) {
                    SignTemplatePushLog signTemplatePushLog = JSON.parseObject(searchHit.getSourceAsString(), SignTemplatePushLog.class);
                    if(!sumPush.containsKey(signTemplatePushLog.getClaimCaseObjectId())){
                        sumPush.put(signTemplatePushLog.getClaimCaseObjectId(),0);
                    }
                    sumPush.put(signTemplatePushLog.getClaimCaseObjectId(),sumPush.get(signTemplatePushLog.getClaimCaseObjectId())+1);

                    if(signTemplatePushLog.getStatus() == 6){
                        if(!successPush.containsKey(signTemplatePushLog.getClaimCaseObjectId())){
                            successPush.put(signTemplatePushLog.getClaimCaseObjectId(),0);
                        }
                        successPush.put(signTemplatePushLog.getClaimCaseObjectId(),successPush.get(signTemplatePushLog.getClaimCaseObjectId())+1);
                    }
                }
                claimCaseObjectList.stream().forEach(claimCaseObjectReq -> {
                    if(sumPush.containsKey(claimCaseObjectReq.getId())){
                        claimCaseObjectReq.setSumSignPush(sumPush.get(claimCaseObjectReq.getId()));
                    }
                    if(successPush.containsKey(claimCaseObjectReq.getId())){
                        claimCaseObjectReq.setSuccessSignPush(successPush.get(claimCaseObjectReq.getId()));
                    }
                });
            }
        }

        PageInfo<ClaimCaseObjectReq> pageInfo = new PageInfo<>(claimCaseObjectList);
        Long totalHits = searchHits.getTotalHits().value;
//        Long totalHits = Long.valueOf(claimCaseObjectList.size());
        Integer total = totalHits.intValue();
        Integer pages = 0;

        //计算总页数
        if (total == -1) {
            pages = 1;
        }
        if (pp.getPageSize() > 0) {
            pages = (int) (total / pp.getPageSize() + ((total % pp.getPageSize() == 0) ? 0 : 1));
        }
        pageInfo.setPageNum(pp.getPageNum());
        pageInfo.setPageSize(pp.getPageSize());
        pageInfo.setTotal(total);
        pageInfo.setPages(pages);
        pageInfo.setSize(pp.getPageSize());
        pageInfo.setStartRow(pp.getPageNum() > 1 ? (pp.getPageNum() - 1) * pp.getPageSize() + 1 : 1);
        pageInfo.setEndRow(pp.getPageNum() > 1 ? pp.getPageNum() * pp.getPageSize() : pp.getPageSize());
        pageInfo.setList(claimCaseObjectList);
        pageInfo.setNavigatePages(8);

        // 计算导航页
        int[] navigatepageNums = null;
        navigatepageNums = ESTool.calcNavigatepageNums(pageInfo.getPages(), 8, navigatepageNums, pp.getPageNum());
        pageInfo.setNavigatepageNums(navigatepageNums);

        // 第一页
        int firstPage = 0;
        // 前一页
        int prePage = 0;
        // 下一页
        int nextPage = 0;
        // 最后一页
        int lastPage = 0;
        // 计算前后页，第一页，最后一页
        ESTool.calcPage(navigatepageNums, firstPage, lastPage, prePage, nextPage,
                pp.getPageNum(), pages, pageInfo
                       );

        // 是否为第一页
        boolean isFirstPage = false;
        // 是否为最后一页
        boolean isLastPage = false;
        // 是否有前一页
        boolean hasPreviousPage = false;
        // 是否有下一页
        boolean hasNextPage = false;
        // 判断页面边界
        ESTool.judgePageBoudary(isFirstPage, isLastPage, hasPreviousPage, hasNextPage,
                pp.getPageNum(), pages, pageInfo
                               );

        return pageInfo;
    }

    @Override
    public PageInfo<ClaimCaseObjectReq> claimCaseObjectList4BS(Map<String, Object> paramMap, PageParam pp) {

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);
        searchSourceBuilder.from(pp.getPageNum() != 0 ? (pp.getPageNum() - 1) * pp.getPageSize() : 0);
        searchSourceBuilder.size(pp.getPageSize());
        searchSourceBuilder.sort("createTime", SortOrder.DESC);
        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();

        Integer comeFrom = Integer.parseInt(paramMap.get("comeFrom").toString());

        if (paramMap.containsKey("status")) {

            boolBuilder.must(QueryBuilders.termQuery("isCaseClosed", 0));
            Integer status = Integer.parseInt(paramMap.get("status").toString());

            // 处理岗
            if (comeFrom == 2) {
                // 采集审核(个人池)
                if (status == 3) {
                    boolBuilder.must(QueryBuilders.termQuery("checkAuditer.keyword", paramMap.get("userId")));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX22.getCode()));
                }
                // 理算审核(个人池)
                if (status == 4) {
                    boolBuilder.must(QueryBuilders.termQuery("checkAuditer.keyword", paramMap.get("userId")));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX32.getCode()));
                }
                // 采集审核(公共池)
                if (status == 5) {
                    boolBuilder.mustNot(QueryBuilders.existsQuery("checkAuditer"));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX22.getCode()));
                }
                // 理算审核(公共池)
                if (status == 6) {
                    boolBuilder.mustNot(QueryBuilders.existsQuery("checkAuditer"));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX32.getCode()));
                }
            }
            if (comeFrom == 3) {
                // 采集审核(个人池)
                if (status == 3) {
                    boolBuilder.must(QueryBuilders.termQuery("insAuditer.keyword", paramMap.get("userId")));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX25.getCode()));
                }
                // 理算审核(个人池)
                if (status == 4) {
                    boolBuilder.must(QueryBuilders.termQuery("insAuditer.keyword", paramMap.get("userId")));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX35.getCode()));
                }
                // 采集审核(公共池)
                if (status == 5) {
                    boolBuilder.mustNot(QueryBuilders.existsQuery("insAuditer"));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX25.getCode()));
                }
                // 理算审核(公共池)
                if (status == 6) {
                    boolBuilder.mustNot(QueryBuilders.existsQuery("insAuditer"));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX35.getCode()));
                }
            }
        }

        // 案件号
        if (paramMap.containsKey("claimCaseNo")) {
            boolBuilder.must(QueryBuilders.wildcardQuery("claimCaseNo.keyword", "*" + paramMap.get("claimCaseNo") + "*"));
        }

        // 被保人姓名
        if (paramMap.containsKey("treatName")) {
            boolBuilder.must(QueryBuilders.wildcardQuery("treatName.keyword", "*" + paramMap.get("treatName") + "*"));
        }

        // 被保人证件号
        if (paramMap.containsKey("treatIdNum")) {
            boolBuilder.must(QueryBuilders.wildcardQuery("treatIdNum.keyword", "*" + paramMap.get("treatIdNum") + "*"));
        }

        // 类型
        if (paramMap.containsKey("type")) {
            boolBuilder.must(QueryBuilders.termQuery("type", paramMap.get("type")));
        }

        // 类型
        if (paramMap.containsKey("category")) {
            boolBuilder.must(QueryBuilders.termQuery("category", paramMap.get("category")));
        }

        // 保司标识
        if (paramMap.containsKey("insCode")) {
            if (paramMap.get("insCode").toString().equals("DD")) {
                BoolQueryBuilder shouldQueryBuilder = new BoolQueryBuilder();
                shouldQueryBuilder.should(QueryBuilders.termQuery("insCode.keyword", paramMap.get("insCode")));
                shouldQueryBuilder.should(new BoolQueryBuilder().mustNot(QueryBuilders.existsQuery("insCode")));
                boolBuilder.must(shouldQueryBuilder);
            } else {
                boolBuilder.must(QueryBuilders.termQuery("insCode.keyword", paramMap.get("insCode")));
            }
        }

        searchSourceBuilder.query(boolBuilder);
        SearchSourceBuilder searchSourceBuilderModifyTime = new SearchSourceBuilder();
        searchSourceBuilderModifyTime.query(boolBuilder);
        searchSourceBuilderModifyTime.trackTotalHits(true);
        searchSourceBuilderModifyTime.from(pp.getPageNum() != 0 ? (pp.getPageNum() - 1) * pp.getPageSize() : 0);
        searchSourceBuilderModifyTime.size(pp.getPageSize());
        searchSourceBuilderModifyTime.sort("modifyTime", SortOrder.DESC);
        SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilderModifyTime));
        SearchHits searchHits = searchResponse.getHits();
        SearchHit[] hits = searchHits.getHits();
        List<ClaimCaseObjectReq> claimCaseObjectList = new ArrayList<>();
        List<String> claimCaseIdList = new ArrayList<>();
        for (SearchHit searchHit : hits) {
            ClaimCaseObject claimCaseObject = JSON.parseObject(searchHit.getSourceAsString(), ClaimCaseObject.class);
            claimCaseIdList.add(claimCaseObject.getClaimCaseId());

            ClaimCaseObjectReq claimCaseObjectReq = new ClaimCaseObjectReq();
            BeanUtils.copyProperties(claimCaseObject, claimCaseObjectReq);
            claimCaseObjectList.add(claimCaseObjectReq);

        }
        BoolQueryBuilder boolBuilderRet = QueryBuilders.boolQuery();
        boolBuilderRet.must(QueryBuilders.termsQuery("id.keyword", claimCaseIdList));
        searchSourceBuilder.clearRescorers();
        searchSourceBuilder.from(0);
        searchSourceBuilder.size(pp.getPageSize());
        searchSourceBuilder.query(boolBuilderRet);
        searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE, searchSourceBuilder));
        SearchHits searchHitsRet = searchResponse.getHits();
        SearchHit[] hitsRet = searchHitsRet.getHits();
        for (SearchHit searchHit : hitsRet) {
            ClaimCase claimCase = JSON.parseObject(searchHit.getSourceAsString(), ClaimCase.class);
            for (ClaimCaseObjectReq claimCaseObjectReq : claimCaseObjectList) {
                if (claimCaseObjectReq.getClaimCaseId().equals(claimCase.getId())) {
                    claimCaseObjectReq.setStartDate(claimCase.getStartDate());
                    claimCaseObjectReq.setTreatDate(claimCase.getTreatDate());
                    claimCaseObjectReq.setLabel(claimCase.getLabel());
                    claimCaseObjectReq.setProvince(claimCase.getProvince());
                    claimCaseObjectReq.setCaseCreateTime(claimCase.getCreateTime());
                }
            }
        }
        PageInfo<ClaimCaseObjectReq> pageInfo = new PageInfo<>(claimCaseObjectList);
        Long totalHits = searchHits.getTotalHits().value;
        Integer total = totalHits.intValue();
        Integer pages = 0;

        //计算总页数
        if (total == -1) {
            pages = 1;
        }
        if (pp.getPageSize() > 0) {
            pages = (int) (total / pp.getPageSize() + ((total % pp.getPageSize() == 0) ? 0 : 1));
        }
        pageInfo.setPageNum(pp.getPageNum());
        pageInfo.setPageSize(pp.getPageSize());
        pageInfo.setTotal(total);
        pageInfo.setPages(pages);
        pageInfo.setSize(pp.getPageSize());
        pageInfo.setStartRow(pp.getPageNum() > 1 ? (pp.getPageNum() - 1) * pp.getPageSize() + 1 : 1);
        pageInfo.setEndRow(pp.getPageNum() > 1 ? pp.getPageNum() * pp.getPageSize() : pp.getPageSize());
        pageInfo.setList(claimCaseObjectList);
        pageInfo.setNavigatePages(8);

        // 计算导航页
        int[] navigatepageNums = null;
        navigatepageNums = ESTool.calcNavigatepageNums(pageInfo.getPages(), 8, navigatepageNums, pp.getPageNum());
        pageInfo.setNavigatepageNums(navigatepageNums);

        // 第一页
        int firstPage = 0;
        // 前一页
        int prePage = 0;
        // 下一页
        int nextPage = 0;
        // 最后一页
        int lastPage = 0;
        // 计算前后页，第一页，最后一页
        ESTool.calcPage(navigatepageNums, firstPage, lastPage, prePage, nextPage,
                pp.getPageNum(), pages, pageInfo
                       );

        // 是否为第一页
        boolean isFirstPage = false;
        // 是否为最后一页
        boolean isLastPage = false;
        // 是否有前一页
        boolean hasPreviousPage = false;
        // 是否有下一页
        boolean hasNextPage = false;
        // 判断页面边界
        ESTool.judgePageBoudary(isFirstPage, isLastPage, hasPreviousPage, hasNextPage,
                pp.getPageNum(), pages, pageInfo
                               );

        return pageInfo;
    }

    @Override
    public PageInfo<ClaimCaseObjectReq> claimCaseObjectList4BSV2(Map<String, Object> paramMap, PageParam pp) {

        // 是否使用Redis缓存数据
        boolean useRedis = true;
        if (paramMap.containsKey("stopFlag") && "true".equals(paramMap.get("stopFlag"))) {
            useRedis = false;
        }
        paramMap.remove("stopFlag");

        String searchId = DigestUtils.md5DigestAsHex(JsonTool.genByFastJson(paramMap).getBytes(StandardCharsets.UTF_8));
        //赔付对象保司审核
        String redisKey = SearchPrefix.genFullObject(SearchPrefix.KD_CLAIM_CASE_OBJECT_AUDIT_4BS, searchId);

        if (!useRedis) {
            RedisTool3.del(redisKey);
        }

        if (!RedisTool3.exists(redisKey)) {
            int size = 1000;

            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.trackTotalHits(true);

            BoolQueryBuilder boolBuilder = new BoolQueryBuilder();

            Integer comeFrom = Integer.parseInt(paramMap.get("comeFrom").toString());
            if (paramMap.containsKey("status")) {

                boolBuilder.must(QueryBuilders.termQuery("isCaseClosed", 0));
                Integer status = Integer.parseInt(paramMap.get("status").toString());
                if (comeFrom == 3) {
                    // 采集审核(个人池)
                    if (status == 3) {
                        boolBuilder.must(QueryBuilders.termQuery("insAuditer.keyword", paramMap.get("userId")));
                        boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX25.getCode()));
                    }
                    // 理算审核(个人池)
                    if (status == 4) {
                        boolBuilder.must(QueryBuilders.termQuery("insAuditer.keyword", paramMap.get("userId")));
                        boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX35.getCode()));
                    }
                    // 采集审核(公共池)
                    if (status == 5) {
                        boolBuilder.mustNot(QueryBuilders.existsQuery("insAuditer"));
                        boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX25.getCode()));
                    }
                    // 理算审核(公共池)
                    if (status == 6) {
                        boolBuilder.mustNot(QueryBuilders.existsQuery("insAuditer"));
                        boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX35.getCode()));
                    }
                }
            }

            // 被保人姓名
            if (paramMap.containsKey("treatName")) {
                boolBuilder.must(QueryBuilders.wildcardQuery("treatName.keyword", "*" + paramMap.get("treatName") + "*"));
            }

            // 被保人证件号
            if (paramMap.containsKey("treatIdNum")) {
                boolBuilder.must(QueryBuilders.wildcardQuery("treatIdNum.keyword", "*" + paramMap.get("treatIdNum") + "*"));
            }

            // 类型
            if (paramMap.containsKey("type")) {
                boolBuilder.must(QueryBuilders.termQuery("type", paramMap.get("type")));
            }

            // 类型
            if (paramMap.containsKey("category")) {
                boolBuilder.must(QueryBuilders.termQuery("category", paramMap.get("category")));
            }

            // 保司标识
            if (paramMap.containsKey("insCode")) {
                if (paramMap.get("insCode").toString().equals("DD")) {
                    BoolQueryBuilder shouldQueryBuilder = new BoolQueryBuilder();
                    shouldQueryBuilder.should(QueryBuilders.termQuery("insCode.keyword", paramMap.get("insCode")));
                    shouldQueryBuilder.should(new BoolQueryBuilder().mustNot(QueryBuilders.existsQuery("insCode")));
                    boolBuilder.must(shouldQueryBuilder);
                } else {
                    boolBuilder.must(QueryBuilders.termQuery("insCode.keyword", paramMap.get("insCode")));
                }
            }
            searchSourceBuilder.sort("modifyTime", SortOrder.DESC);
            searchSourceBuilder.size(size);
            searchSourceBuilder.fetchSource(new String[]{"id", "claimCaseId"}, null);
            searchSourceBuilder.query(boolBuilder);
            SearchResponse searchResponse = ESHighLevelRESTClientTool.scrollSearchInit(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder);
            SearchHits searchHits = searchResponse.getHits();
            List<ClaimCaseObject> claimCaseObjectList = new ArrayList<>();
            String _scroll_id = "";
            if (searchHits.getTotalHits().value == 0l) {
            } else {
                _scroll_id = searchResponse.getScrollId();
                SearchHit[] hits = searchHits.getHits();
                for (SearchHit searchHit : hits) {
                    ClaimCaseObject claimCaseObject = JSON.parseObject(searchHit.getSourceAsString(), ClaimCaseObject.class);
                    claimCaseObjectList.add(claimCaseObject);
                }
            }
            while (true) {
                if (StringUtils.isNotBlank(_scroll_id)) {
                    searchResponse = ESHighLevelRESTClientTool.scrollSearchSyn(_scroll_id);
                    searchHits = searchResponse.getHits();
                    if (searchHits.getTotalHits().value == 0l || searchHits.getHits().length == 0) {
                        List<String> scrollIdList = new ArrayList<>();
                        scrollIdList.add(_scroll_id);
                        ESHighLevelRESTClientTool.clearScollSearchSyn(ESHighLevelRESTClientTool.genClearScollSearchReq(scrollIdList));
                        _scroll_id = null;
                    } else {
                        _scroll_id = searchResponse.getScrollId();
                        SearchHit[] hits = searchHits.getHits();
                        for (SearchHit searchHit : hits) {
                            ClaimCaseObject claimCaseObject = JSON.parseObject(searchHit.getSourceAsString(), ClaimCaseObject.class);
                            claimCaseObjectList.add(claimCaseObject);
                        }
                    }
                } else {
                    break;
                }
            }

            searchSourceBuilder.clearRescorers();
            boolBuilder = new BoolQueryBuilder();
            boolBuilder.must(QueryBuilders.existsQuery("insuranceCaseNo"));     // 必须存在保司案件号
            // 立案号
            if (paramMap.containsKey("insuranceCaseNo")) {
                boolBuilder.must(QueryBuilders.wildcardQuery("insuranceCaseNo.keyword", "*" + paramMap.get("insuranceCaseNo") + "*"));
            }
            List<String> claimCaseIdList = claimCaseObjectList.stream().map(ClaimCaseObject::getClaimCaseId).distinct().collect(Collectors.toList());
            boolBuilder.must(QueryBuilders.termsQuery("id.keyword", claimCaseIdList));
            searchSourceBuilder.trackTotalHits(true);
            searchSourceBuilder.size(claimCaseIdList.size());
            searchSourceBuilder.fetchSource(new String[]{"id"}, null);
            searchSourceBuilder.query(boolBuilder);
            searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE, searchSourceBuilder));
            searchHits = searchResponse.getHits();
            SearchHit[] hits = searchHits.getHits();
            List<String> caseIdList = new ArrayList<>();
            for (SearchHit hit : hits) {
                caseIdList.add(JSON.parseObject(hit.getSourceAsString()).getString("id"));
            }

            claimCaseObjectList = claimCaseObjectList.stream().filter(claimCaseObject -> caseIdList.contains(claimCaseObject.getClaimCaseId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(claimCaseObjectList)) {
                List<String> collect = claimCaseObjectList.stream().map(ClaimCaseObject::getId).collect(Collectors.toList());
                RedisTool3.rpush(redisKey, collect.toArray(new String[collect.size()]));
                RedisTool3.expire(redisKey, 3 * 60);
            }
        }

        int from = pp.getPageNum() != 0 ? (pp.getPageNum() - 1) * pp.getPageSize() : 0;
        int size = pp.getPageSize();


        Long totalHits = RedisTool3.llen(redisKey);
        if (totalHits == null) {
            totalHits = 0L;
        }

        int end = size + from - 1;

        List<String> claimCaseObjectIdList = new ArrayList<>();
        if (end > 0) {
            claimCaseObjectIdList = RedisTool3.lrange(redisKey, from, end);
        }

        List<ClaimCaseObjectReq> claimCaseObjectList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(claimCaseObjectIdList)) {
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.sort("modifyTime", SortOrder.DESC);
            searchSourceBuilder.size(claimCaseObjectIdList.size());
            BoolQueryBuilder boolBuilder = new BoolQueryBuilder();
            boolBuilder.must(QueryBuilders.termsQuery("id.keyword", claimCaseObjectIdList));
            searchSourceBuilder.query(boolBuilder);
            SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
            SearchHits searchHits = searchResponse.getHits();
            if (searchHits.getTotalHits().value == 0) {
            } else {
                SearchHit[] hits = searchHits.getHits();
                for (SearchHit hit : hits) {
                    ClaimCaseObject claimCaseObject = JSON.parseObject(hit.getSourceAsString(), ClaimCaseObject.class);

                    ClaimCaseObjectReq claimCaseObjectReq = new ClaimCaseObjectReq();
                    BeanUtils.copyProperties(claimCaseObject, claimCaseObjectReq);
                    claimCaseObjectList.add(claimCaseObjectReq);
                }
            }
        }

        // 过滤出保司案件号
        if (CollectionUtils.isNotEmpty(claimCaseObjectList)) {
            List<String> claimCaseIdList = claimCaseObjectList.stream().map(ClaimCaseObjectReq::getClaimCaseId).distinct().collect(Collectors.toList());
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.size(claimCaseIdList.size());
            BoolQueryBuilder boolBuilder = new BoolQueryBuilder();
            boolBuilder.must(QueryBuilders.termsQuery("id.keyword", claimCaseIdList));
            searchSourceBuilder.query(boolBuilder);
            SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE, searchSourceBuilder));
            SearchHits searchHits = searchResponse.getHits();
            Map<String, ClaimCase> dataMap = new HashMap<>();
            if (searchHits.getTotalHits().value == 0) {
            } else {
                SearchHit[] hits = searchHits.getHits();
                for (SearchHit hit : hits) {
                    ClaimCase claimCase = JSON.parseObject(hit.getSourceAsString(), ClaimCase.class);
                    dataMap.put(claimCase.getId(), claimCase);
                }
            }
            claimCaseObjectList.stream().forEach(object -> {
                ClaimCase claimCase = dataMap.get(object.getClaimCaseId());
                if (claimCase != null) {
                    object.setInsuranceCaseNo(claimCase.getInsuranceCaseNo());
                    object.setStartDate(claimCase.getStartDate());
                    object.setTreatDate(claimCase.getTreatDate());
                    object.setCaseCreateTime(claimCase.getCreateTime());
                    object.setLabel(claimCase.getLabel());
                    object.setProvince(claimCase.getProvince());
                }
            });
        }

        PageInfo<ClaimCaseObjectReq> pageInfo = new PageInfo<ClaimCaseObjectReq>(claimCaseObjectList);

        Integer total = totalHits.intValue();
        Integer pages = 0;

        //计算总页数
        if (total == -1) {
            pages = 1;
        }
        if (pp.getPageSize() > 0) {
            pages = (int) (total / pp.getPageSize() + ((total % pp.getPageSize() == 0) ? 0 : 1));
        }
        pageInfo.setPageNum(pp.getPageNum());
        pageInfo.setPageSize(pp.getPageSize());
        pageInfo.setTotal(total);
        pageInfo.setPages(pages);
        pageInfo.setSize(pp.getPageSize());
        pageInfo.setStartRow(pp.getPageNum() > 1 ? (pp.getPageNum() - 1) * pp.getPageSize() + 1 : 1);
        pageInfo.setEndRow(pp.getPageNum() > 1 ? pp.getPageNum() * pp.getPageSize() : pp.getPageSize());
        pageInfo.setList(claimCaseObjectList);
        pageInfo.setNavigatePages(8);

        // 计算导航页
        int[] navigatepageNums = null;
        navigatepageNums = ESTool.calcNavigatepageNums(pageInfo.getPages(), 8, navigatepageNums, pp.getPageNum());
        pageInfo.setNavigatepageNums(navigatepageNums);

        // 第一页
        int firstPage = 0;
        // 前一页
        int prePage = 0;
        // 下一页
        int nextPage = 0;
        // 最后一页
        int lastPage = 0;
        // 计算前后页，第一页，最后一页
        ESTool.calcPage(navigatepageNums, firstPage, lastPage, prePage, nextPage,
                pp.getPageNum(), pages, pageInfo
        );

        // 是否为第一页
        boolean isFirstPage = false;
        // 是否为最后一页
        boolean isLastPage = false;
        // 是否有前一页
        boolean hasPreviousPage = false;
        // 是否有下一页
        boolean hasNextPage = false;
        // 判断页面边界
        ESTool.judgePageBoudary(isFirstPage, isLastPage, hasPreviousPage, hasNextPage,
                pp.getPageNum(), pages, pageInfo
        );

        return pageInfo;
    }
    @Override
    public PageInfo<ClaimCaseObjectReq> claimCaseSignatureList(Map<String, Object> paramMap, PageParam pp) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);
        searchSourceBuilder.from(pp.getPageNum() != 0 ? (pp.getPageNum() - 1) * pp.getPageSize() : 0);
        searchSourceBuilder.size(pp.getPageSize());
        searchSourceBuilder.sort("createTime", SortOrder.DESC);
        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();

        if (paramMap.containsKey("status")) {

            boolBuilder.must(QueryBuilders.termQuery("isCaseClosed", 0));
            Integer status = Integer.parseInt(paramMap.get("status").toString());

            //新增签章状态枚举
            if (status == 1) {
                boolBuilder.must(QueryBuilders.termQuery("checkAuditer.keyword", paramMap.get("userId")));
                boolBuilder.must(QueryBuilders.termsQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX33.getCode(),ClaimCaseObjectStatusEnum.BAX36.getCode(),ClaimCaseObjectStatusEnum.BAX40));
            }
            if (status == 0) {
                boolBuilder.mustNot(QueryBuilders.existsQuery("checkAuditer"));
                boolBuilder.must(QueryBuilders.termsQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX33.getCode(),ClaimCaseObjectStatusEnum.BAX36.getCode(),ClaimCaseObjectStatusEnum.BAX40));
            }
        }

        // 案件号
        if (paramMap.containsKey("claimCaseNo")) {
            boolBuilder.must(QueryBuilders.wildcardQuery("claimCaseNo.keyword", "*" + paramMap.get("claimCaseNo") + "*"));
        }

        // 被保人姓名
        if (paramMap.containsKey("treatName")) {
            boolBuilder.must(QueryBuilders.wildcardQuery("treatName.keyword", "*" + paramMap.get("treatName") + "*"));
        }

        // 被保人证件号
        if (paramMap.containsKey("treatIdNum")) {
            boolBuilder.must(QueryBuilders.wildcardQuery("treatIdNum.keyword", "*" + paramMap.get("treatIdNum") + "*"));
        }

        // 类型
        if (paramMap.containsKey("type")) {
            boolBuilder.must(QueryBuilders.termQuery("type", paramMap.get("type")));
        }

        // 类型
        if (paramMap.containsKey("category")) {
            boolBuilder.must(QueryBuilders.termQuery("category", paramMap.get("category")));
        }

        // 保司标识
        if (paramMap.containsKey("insCode")) {
            if (paramMap.get("insCode").toString().equals("DD")) {
                BoolQueryBuilder shouldQueryBuilder = new BoolQueryBuilder();
                shouldQueryBuilder.should(QueryBuilders.termQuery("insCode.keyword", paramMap.get("insCode")));
                shouldQueryBuilder.should(new BoolQueryBuilder().mustNot(QueryBuilders.existsQuery("insCode")));
                boolBuilder.must(shouldQueryBuilder);
            } else {
                boolBuilder.must(QueryBuilders.termQuery("insCode.keyword", paramMap.get("insCode")));
            }
        }

        if (paramMap.containsKey("auditer")) {
            boolBuilder.must(QueryBuilders.termQuery("auditer", paramMap.get("auditer")));
        }


        searchSourceBuilder.query(boolBuilder);
        SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
        SearchHits searchHits = searchResponse.getHits();
        SearchHit[] hits = searchHits.getHits();
        List<ClaimCaseObjectReq> claimCaseObjectList = new ArrayList<>();
        List<String> claimCaseIdList = new ArrayList<>();
        for (SearchHit searchHit : hits) {
            ClaimCaseObject claimCaseObject = JSON.parseObject(searchHit.getSourceAsString(), ClaimCaseObject.class);
            claimCaseIdList.add(claimCaseObject.getClaimCaseId());

            ClaimCaseObjectReq claimCaseObjectReq = new ClaimCaseObjectReq();
            BeanUtils.copyProperties(claimCaseObject, claimCaseObjectReq);
            claimCaseObjectList.add(claimCaseObjectReq);

        }
        BoolQueryBuilder boolBuilderRet = QueryBuilders.boolQuery();
        boolBuilderRet.must(QueryBuilders.termsQuery("id.keyword", claimCaseIdList));
        searchSourceBuilder.clearRescorers();
        searchSourceBuilder.from(0);
        searchSourceBuilder.size(pp.getPageSize());
        searchSourceBuilder.query(boolBuilderRet);
        searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE, searchSourceBuilder));
        SearchHits searchHitsRet = searchResponse.getHits();
        SearchHit[] hitsRet = searchHitsRet.getHits();
        for (SearchHit searchHit : hitsRet) {
            ClaimCase claimCase = JSON.parseObject(searchHit.getSourceAsString(), ClaimCase.class);
            for (ClaimCaseObjectReq claimCaseObjectReq : claimCaseObjectList) {
                if (claimCaseObjectReq.getClaimCaseId().equals(claimCase.getId())) {
                    claimCaseObjectReq.setStartDate(claimCase.getStartDate());
                    claimCaseObjectReq.setTreatDate(claimCase.getTreatDate());
                    claimCaseObjectReq.setLabel(claimCase.getLabel());
                    claimCaseObjectReq.setProvince(claimCase.getProvince());
                }
            }
        }
        PageInfo<ClaimCaseObjectReq> pageInfo = new PageInfo<>(claimCaseObjectList);
        Long totalHits = searchHits.getTotalHits().value;
        Integer total = totalHits.intValue();
        Integer pages = 0;

        //计算总页数
        if (total == -1) {
            pages = 1;
        }
        if (pp.getPageSize() > 0) {
            pages = (int) (total / pp.getPageSize() + ((total % pp.getPageSize() == 0) ? 0 : 1));
        }
        pageInfo.setPageNum(pp.getPageNum());
        pageInfo.setPageSize(pp.getPageSize());
        pageInfo.setTotal(total);
        pageInfo.setPages(pages);
        pageInfo.setSize(pp.getPageSize());
        pageInfo.setStartRow(pp.getPageNum() > 1 ? (pp.getPageNum() - 1) * pp.getPageSize() + 1 : 1);
        pageInfo.setEndRow(pp.getPageNum() > 1 ? pp.getPageNum() * pp.getPageSize() : pp.getPageSize());
        pageInfo.setList(claimCaseObjectList);
        pageInfo.setNavigatePages(8);

        // 计算导航页
        int[] navigatepageNums = null;
        navigatepageNums = ESTool.calcNavigatepageNums(pageInfo.getPages(), 8, navigatepageNums, pp.getPageNum());
        pageInfo.setNavigatepageNums(navigatepageNums);

        // 第一页
        int firstPage = 0;
        // 前一页
        int prePage = 0;
        // 下一页
        int nextPage = 0;
        // 最后一页
        int lastPage = 0;
        // 计算前后页，第一页，最后一页
        ESTool.calcPage(navigatepageNums, firstPage, lastPage, prePage, nextPage,
                pp.getPageNum(), pages, pageInfo
        );

        // 是否为第一页
        boolean isFirstPage = false;
        // 是否为最后一页
        boolean isLastPage = false;
        // 是否有前一页
        boolean hasPreviousPage = false;
        // 是否有下一页
        boolean hasNextPage = false;
        // 判断页面边界
        ESTool.judgePageBoudary(isFirstPage, isLastPage, hasPreviousPage, hasNextPage,
                pp.getPageNum(), pages, pageInfo
        );

        return pageInfo;
    }

    @Override
    public Map<String, List<ClaimCaseObject>> findClaimCaseObjectStatistics() {

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);

        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
        boolBuilder.must(QueryBuilders.existsQuery("auditer"));

        searchSourceBuilder.query(boolBuilder);

        SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));

        SearchHits searchHits = searchResponse.getHits();

        if (searchHits.getTotalHits().value == 0l) {
            return null;
        }

        Long totalHits = searchHits.getTotalHits().value;
        Integer total = totalHits.intValue();

        searchSourceBuilder.size(total);

        searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
        searchHits = searchResponse.getHits();

        SearchHit[] hits = searchHits.getHits();

        List<ClaimCaseObject> claimCaseObjectList = new ArrayList<>();

        for (SearchHit hit : hits) {
            ClaimCaseObject claimCaseObject = JSON.parseObject(hit.getSourceAsString(), ClaimCaseObject.class);
            claimCaseObjectList.add(claimCaseObject);
        }

        Map<String, List<ClaimCaseObject>> collect = claimCaseObjectList.stream().collect(groupingBy(a -> a.getAuditer()));

        return collect;
    }

    @Override
    public Map<String, List<ClaimCaseObject>> findClaimCaseObjectStatisticsByMonth(Date date) {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");

        String date1=sdf.format(date);

        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.MONTH, 1);

        String date2=sdf.format(c.getTime());

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);

        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
        boolBuilder.must(QueryBuilders.existsQuery("auditer"));

        RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("createTime");
        rangeQueryBuilder.gte(kd.common.tool.DateUtils.parse(date1 + "-01 00:00:00", "yyyy-MM-dd HH:mm:ss").getTime());
        rangeQueryBuilder.lt(kd.common.tool.DateUtils.parse(date2 + "-01 00:00:00", "yyyy-MM-dd HH:mm:ss").getTime());

        boolBuilder.must(rangeQueryBuilder);

        searchSourceBuilder.query(boolBuilder);

        SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));

        SearchHits searchHits = searchResponse.getHits();

        if (searchHits.getTotalHits().value == 0l) {
            return null;
        }

        Long totalHits = searchHits.getTotalHits().value;
        Integer total = totalHits.intValue();

        searchSourceBuilder.size(total);

        searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
        searchHits = searchResponse.getHits();

        SearchHit[] hits = searchHits.getHits();

        List<ClaimCaseObject> claimCaseObjectList = new ArrayList<>();

        for (SearchHit hit : hits) {
            ClaimCaseObject claimCaseObject = JSON.parseObject(hit.getSourceAsString(), ClaimCaseObject.class);
            claimCaseObjectList.add(claimCaseObject);
        }

        Map<String, List<ClaimCaseObject>> collect = claimCaseObjectList.stream().collect(groupingBy(a -> a.getAuditer()));

        return collect;
    }

    @Override
    public PageInfo<ClaimCaseObject> findByStatisticsParam(Map<String, Object> paramMap, PageParam pp) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);
        searchSourceBuilder.from(pp.getPageNum() != 0 ? (pp.getPageNum() - 1) * pp.getPageSize() : 0);
        searchSourceBuilder.size(pp.getPageSize());
        searchSourceBuilder.sort("createTime", SortOrder.DESC);
        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();

        if (paramMap.containsKey("auditer")) {
            boolBuilder.must(QueryBuilders.termsQuery("auditer.keyword", paramMap.get("auditer")));
        }

        if (paramMap.containsKey("statisticalType")) {
            boolBuilder.must(QueryBuilders.existsQuery("auditer"));
            Integer statisticalType = Integer.parseInt(paramMap.get("statisticalType").toString());
            // 处理中任务数
            if (statisticalType == 1) {
                BoolQueryBuilder wildcardQueryBuilder = new BoolQueryBuilder();
                wildcardQueryBuilder.should(QueryBuilders.wildcardQuery("status.keyword", "BAX2*"));
                wildcardQueryBuilder.should(QueryBuilders.wildcardQuery("status.keyword", "BAX3*"));
                boolBuilder.must(wildcardQueryBuilder);
                boolBuilder.must(QueryBuilders.termQuery("isCaseClosed", 0));
            }
            // 完成任务数
            if (statisticalType == 2) {
                boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX99.getCode()));
                boolBuilder.must(QueryBuilders.termQuery("isCaseClosed", 0));
            }
            // 关闭任务数
            if (statisticalType == 3) {
                boolBuilder.must(QueryBuilders.termQuery("isCaseClosed", 1));
            }
            // 当日接收
            if (statisticalType == 4) {
                Date date = new Date();
                RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("auditerShareTime");
                rangeQueryBuilder.gte(DateUtils.parse(DateUtils.format(date), DateUtils.FORMAT_DATE_YYYY_MM_DD).getTime());
                rangeQueryBuilder.lte(DateUtils.parse(DateUtils.format(date) + " 23:59:59", DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS).getTime());
                boolBuilder.must(rangeQueryBuilder);
                boolBuilder.must(QueryBuilders.termQuery("isCaseClosed", 0));
            }
            // 当日完成
            if (statisticalType == 5) {
                Date date = new Date();
                RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("checkEndTime");
                rangeQueryBuilder.gte(DateUtils.parse(DateUtils.format(date), DateUtils.FORMAT_DATE_YYYY_MM_DD).getTime());
                rangeQueryBuilder.lte(DateUtils.parse(DateUtils.format(date) + " 23:59:59", DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS).getTime());
                boolBuilder.must(rangeQueryBuilder);
                boolBuilder.must(QueryBuilders.termQuery("isCaseClosed", 0));
            }
        }

        // 案件号
        if (paramMap.containsKey("claimCaseNo")) {
            boolBuilder.must(QueryBuilders.wildcardQuery("claimCaseNo.keyword", "*" + paramMap.get("claimCaseNo") + "*"));
        }

        // 被保人姓名
        if (paramMap.containsKey("treatName")) {
            boolBuilder.must(QueryBuilders.wildcardQuery("treatName.keyword", "*" + paramMap.get("treatName") + "*"));
        }

        // 被保人证件号
        if (paramMap.containsKey("treatIdNum")) {
            boolBuilder.must(QueryBuilders.wildcardQuery("treatIdNum.keyword", "*" + paramMap.get("treatIdNum") + "*"));
        }

        // 类型
        if (paramMap.containsKey("type")) {
            boolBuilder.must(QueryBuilders.termQuery("type", paramMap.get("type")));
        }

        // 类型
        if (paramMap.containsKey("category")) {
            boolBuilder.must(QueryBuilders.termQuery("category", paramMap.get("category")));
        }

        searchSourceBuilder.query(boolBuilder);
        SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
        SearchHits searchHits = searchResponse.getHits();
        SearchHit[] hits = searchHits.getHits();
        List<ClaimCaseObject> claimCaseObjectList = new ArrayList<>();
        for (SearchHit searchHit : hits) {
            ClaimCaseObject claimCaseObject = JSON.parseObject(searchHit.getSourceAsString(), ClaimCaseObject.class);
            claimCaseObjectList.add(claimCaseObject);
        }
        PageInfo<ClaimCaseObject> pageInfo = new PageInfo<>(claimCaseObjectList);
        Long totalHits = searchHits.getTotalHits().value;
        Integer total = totalHits.intValue();
        Integer pages = 0;

        //计算总页数
        if (total == -1) {
            pages = 1;
        }
        if (pp.getPageSize() > 0) {
            pages = (int) (total / pp.getPageSize() + ((total % pp.getPageSize() == 0) ? 0 : 1));
        }
        pageInfo.setPageNum(pp.getPageNum());
        pageInfo.setPageSize(pp.getPageSize());
        pageInfo.setTotal(total);
        pageInfo.setPages(pages);
        pageInfo.setSize(pp.getPageSize());
        pageInfo.setStartRow(pp.getPageNum() > 1 ? (pp.getPageNum() - 1) * pp.getPageSize() + 1 : 1);
        pageInfo.setEndRow(pp.getPageNum() > 1 ? pp.getPageNum() * pp.getPageSize() : pp.getPageSize());
        pageInfo.setList(claimCaseObjectList);
        pageInfo.setNavigatePages(8);

        // 计算导航页
        int[] navigatepageNums = null;
        navigatepageNums = ESTool.calcNavigatepageNums(pageInfo.getPages(), 8, navigatepageNums, pp.getPageNum());
        pageInfo.setNavigatepageNums(navigatepageNums);

        // 第一页
        int firstPage = 0;
        // 前一页
        int prePage = 0;
        // 下一页
        int nextPage = 0;
        // 最后一页
        int lastPage = 0;
        // 计算前后页，第一页，最后一页
        ESTool.calcPage(navigatepageNums, firstPage, lastPage, prePage, nextPage,
                pp.getPageNum(), pages, pageInfo
                       );

        // 是否为第一页
        boolean isFirstPage = false;
        // 是否为最后一页
        boolean isLastPage = false;
        // 是否有前一页
        boolean hasPreviousPage = false;
        // 是否有下一页
        boolean hasNextPage = false;
        // 判断页面边界
        ESTool.judgePageBoudary(isFirstPage, isLastPage, hasPreviousPage, hasNextPage,
                pp.getPageNum(), pages, pageInfo
                               );

        return pageInfo;
    }

    @Override
    public int clearCheckAudit(ClaimCaseObject claimCaseObject) {
        return claimCaseObjectDao.clearCheckAudit(claimCaseObject);
    }

    @Override
    public Map<String, Map<String, String>> findCount4StatusAndInsCode(Map<String, Object> paramMap) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);
        searchSourceBuilder.size(0);
        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();

        Map<String,String> insStatusMap = (Map<String, String>) paramMap.get("status");
        boolBuilder.must(QueryBuilders.termsQuery("status.keyword",insStatusMap.keySet()));
        searchSourceBuilder.query(boolBuilder);

        String insStatusName = "insStatusName";
        String insCodeName = "insCodeName";

        Map<String,String> insCodeMap = (Map<String, String>) paramMap.get("insCode");

        searchSourceBuilder.aggregation(AggregationBuilders.terms(insStatusName).field("status.keyword").size(insStatusMap.size())
        .subAggregation(AggregationBuilders.terms(insCodeName).field("insCode.keyword").size(insCodeMap.size()+10)));

        SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
        Aggregations aggregations = searchResponse.getAggregations();
        ParsedStringTerms insStatusTerms = (ParsedStringTerms)aggregations.get(insStatusName);

        Map<String,Map<String,String>> resultMap = new LinkedHashMap<>();
        for(Terms.Bucket insStatusBucket : insStatusTerms.getBuckets()){
            ParsedStringTerms insCodeTerms = (ParsedStringTerms)insStatusBucket.getAggregations().get(insCodeName);
            long docCount4InsStatus = insStatusBucket.getDocCount();
            String keyAsStringInsStatus = insStatusBucket.getKeyAsString();

            Map<String,String> insCodeResultMap = new HashMap<>();
            insCodeResultMap.put("总计", String.valueOf(docCount4InsStatus));
            resultMap.put(keyAsStringInsStatus,insCodeResultMap);
            long itemSum = 0L;
            for(Terms.Bucket insCodeBucket : insCodeTerms.getBuckets()){
                long docCount4InsCode = insCodeBucket.getDocCount();
                String keyAsStringInsCode = insCodeBucket.getKeyAsString();
                if(insCodeMap.containsKey(keyAsStringInsCode)){
                    itemSum +=docCount4InsCode;
                    insCodeResultMap.put(keyAsStringInsCode,String.valueOf(docCount4InsCode));
                }
            }
            long subNum = docCount4InsStatus-itemSum;
            if(subNum>0L){
                insCodeResultMap.put("其他",String.valueOf(subNum));
            }
        }


        return resultMap;
    }

    @Override
    public Map<String, List<ClaimCaseObjectV1>> findClaimCaseObjectMonthStatistics(Map<String, Object> paramMap) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);

        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
        boolBuilder.must(QueryBuilders.existsQuery("auditer"))
                .must(QueryBuilders.existsQuery("claimCaseNo"));

        if(paramMap.containsKey("auditer")){
            boolBuilder.must(QueryBuilders.matchQuery("auditer.keyword", paramMap.get("auditer")));
        }

        // 获取当前时间
        Calendar calendar = Calendar.getInstance();

        calendar.setTime(DateUtils.parse(paramMap.get("createTime") + "-01"));

        // 设置为当月第一天
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date firstDay = calendar.getTime();

        // 设置为下个月的第一天
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        // 前一天即为当月的最后一天
        calendar.add(Calendar.DAY_OF_YEAR, -1);
        Date lastDay = calendar.getTime();


        RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("createTime");
        if(paramMap.get("startTime") != null || paramMap.get("endTime") != null){
            if(paramMap.get("startTime") != null){
                rangeQueryBuilder.gte(DateUtils.parse(paramMap.get("startTime") + " 00:00:00", DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS).getTime());
            }
            if(paramMap.get("endTime") != null){
                rangeQueryBuilder.lte(DateUtils.parse(paramMap.get("endTime") + " 23:59:59", DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS).getTime());
            }
            boolBuilder.must(rangeQueryBuilder);
        }else{
            rangeQueryBuilder.gte(DateUtils.parse(DateUtils.format(firstDay, DateUtils.FORMAT_DATE_YYYY_MM_DD) + " 00:00:00", DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS).getTime());
            rangeQueryBuilder.lte(DateUtils.parse(DateUtils.format(lastDay, DateUtils.FORMAT_DATE_YYYY_MM_DD) + " 23:59:59", DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS).getTime());
            boolBuilder.must(rangeQueryBuilder);
        }
        boolBuilder.must(rangeQueryBuilder);

        searchSourceBuilder.query(boolBuilder);

        SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));

        SearchHits searchHits = searchResponse.getHits();

        if (searchHits.getTotalHits().value == 0L) {
            return null;
        }
        Long totalHits = searchHits.getTotalHits().value;
        Integer total = totalHits.intValue();

        searchSourceBuilder.size(total);

        searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
        searchHits = searchResponse.getHits();

        SearchHit[] hits = searchHits.getHits();

        List<ClaimCaseObjectV1> claimCaseObjectList = new ArrayList<>();

        HashSet<String> claimCaseNoSet = new HashSet<>();
        for (SearchHit hit : hits) {
            ClaimCaseObject claimCaseObject = JSON.parseObject(hit.getSourceAsString(), ClaimCaseObject.class);
            claimCaseNoSet.add(claimCaseObject.getClaimCaseNo());

            ClaimCaseObjectV1 claimCaseObjectV1 = new ClaimCaseObjectV1();
            BeanUtils.copyProperties(claimCaseObject, claimCaseObjectV1);
            claimCaseObjectList.add(claimCaseObjectV1);
        }

        //从ClaimCase索引中获取当月匹配的案件号
        List<String> setList = new ArrayList<>(claimCaseNoSet);

        SearchSourceBuilder ClaimCaseBuilder = new SearchSourceBuilder();
        ClaimCaseBuilder.trackTotalHits(true);

        BoolQueryBuilder claimCaseBoolBuilder = new BoolQueryBuilder();
        claimCaseBoolBuilder.must(QueryBuilders.termsQuery("claimCaseNo.keyword", setList));
        ClaimCaseBuilder.query(claimCaseBoolBuilder);
        SearchResponse claimCaseResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE, ClaimCaseBuilder));

        SearchHits claimCaseHits = claimCaseResponse.getHits();

        if (claimCaseHits.getTotalHits().value == 0L) {
            return null;
        }
        Long ClaimCaseTotalHits = claimCaseHits.getTotalHits().value;
        Integer ClaimCaseTotal = ClaimCaseTotalHits.intValue();

        ClaimCaseBuilder.size(ClaimCaseTotal);

        claimCaseResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE, ClaimCaseBuilder));
        claimCaseHits = claimCaseResponse.getHits();

        SearchHit[] claimCaseSearchHits = claimCaseHits.getHits();


        HashMap<String, String> hashMap = new HashMap<>();
        for (SearchHit hit : claimCaseSearchHits) {
            ClaimCase claimCase = JSON.parseObject(hit.getSourceAsString(), ClaimCase.class);
            if(claimCase != null && StringUtils.isNotBlank(claimCase.getStatus())){
                hashMap.put(claimCase.getClaimCaseNo(), claimCase.getStatus());
            }
        }

        for(ClaimCaseObjectV1 claimCaseObject :claimCaseObjectList){
            if(hashMap.containsKey(claimCaseObject.getClaimCaseNo())){
                claimCaseObject.setClaimCaseStatus(hashMap.get(claimCaseObject.getClaimCaseNo()));
            }
        }


        Map<String, List<ClaimCaseObjectV1>> collect = claimCaseObjectList.stream().collect(groupingBy(a -> a.getAuditer()));

        return collect;
    }

    @Override
    public Map<String, List<ClaimCaseObjectV1>> findClaimCaseObjectMonthPassCount(Map<String, Object> paramMap){
        //查询ClaimCaseObject中状态为理算初审通过、理算复审审核通过、完成且modifyTime在范围内的
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);

        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.existsQuery("auditer"))
                .must(QueryBuilders.existsQuery("claimCaseNo"));

        if(paramMap.containsKey("auditer")){
            boolQueryBuilder.must(QueryBuilders.matchQuery("auditer.keyword", paramMap.get("auditer")));
        }

        Calendar calendar = Calendar.getInstance();

        calendar.setTime(DateUtils.parse(paramMap.get("createTime") + "-01"));

        // 设置为当月第一天
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date firstDay = calendar.getTime();

        // 设置为下个月的第一天
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        // 前一天即为当月的最后一天
        calendar.add(Calendar.DAY_OF_YEAR, -1);
        Date lastDay = calendar.getTime();


        RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("modifyTime");
        if (paramMap.get("startTime") != null || paramMap.get("endTime") != null){
            if (paramMap.get("startTime") != null){
                rangeQueryBuilder.gte(DateUtils.parse(paramMap.get("startTime") + " 00:00:00", DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS).getTime());
            }
            if (paramMap.get("endTime") != null){
                rangeQueryBuilder.lte(DateUtils.parse(paramMap.get("endTime") + " 23:59:59", DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS).getTime());
            }
            boolQueryBuilder.must(rangeQueryBuilder);
        } else{
            rangeQueryBuilder.gte(DateUtils.parse(DateUtils.format(firstDay, DateUtils.FORMAT_DATE_YYYY_MM_DD) + " 00:00:00", DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS).getTime());
            rangeQueryBuilder.lte(DateUtils.parse(DateUtils.format(lastDay, DateUtils.FORMAT_DATE_YYYY_MM_DD) + " 23:59:59", DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS).getTime());
            boolQueryBuilder.must(rangeQueryBuilder);
        }
        boolQueryBuilder.must(rangeQueryBuilder);

        BoolQueryBuilder shouldQueryBuilder = new BoolQueryBuilder();
        shouldQueryBuilder.should(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX33.getCode()));
        shouldQueryBuilder.should(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX36.getCode()));
        shouldQueryBuilder.should(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX40.getCode()));
        shouldQueryBuilder.should(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX99.getCode()));
        boolQueryBuilder.must(shouldQueryBuilder);

        searchSourceBuilder.query(boolQueryBuilder);

        SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));

        SearchHits searchHits = searchResponse.getHits();

        if (searchHits.getTotalHits().value == 0L) {
            return null;
        }
        Long totalHits = searchHits.getTotalHits().value;
        Integer total = totalHits.intValue();

        searchSourceBuilder.size(total);

        searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
        searchHits = searchResponse.getHits();

        SearchHit[] hits = searchHits.getHits();

        List<ClaimCaseObjectV1> claimCaseObjectList = new ArrayList<>();

        for (SearchHit hit : hits) {
            ClaimCaseObject claimCaseObject = JSON.parseObject(hit.getSourceAsString(), ClaimCaseObject.class);

            ClaimCaseObjectV1 claimCaseObjectV1 = new ClaimCaseObjectV1();
            BeanUtils.copyProperties(claimCaseObject, claimCaseObjectV1);
            claimCaseObjectList.add(claimCaseObjectV1);
        }

        Map<String, List<ClaimCaseObjectV1>> collect = claimCaseObjectList.stream().collect(groupingBy(a -> a.getAuditer()));
        return collect;
    }

    @Override
    public Map<String, List<ClaimCaseObjectV1>> findClaimCaseObjectMonthStatisticsFromStart(Map<String, Object> paramMap){
        //查询claimCase中平台开通至今，当月所有关闭数
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);

        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
        boolBuilder.must(QueryBuilders.existsQuery("claimCaseNo"));

        // 获取当前时间
        Calendar calendar = Calendar.getInstance();

        calendar.setTime(DateUtils.parse(paramMap.get("createTime") + "-01"));

        // 设置为当月第一天
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date firstDay = calendar.getTime();

        // 设置为下个月的第一天
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        // 前一天即为当月的最后一天
        calendar.add(Calendar.DAY_OF_YEAR, -1);
        Date lastDay = calendar.getTime();


        RangeQueryBuilder cancelDateRange = QueryBuilders.rangeQuery("cancelDate");
        if (paramMap.get("startTime") != null || paramMap.get("endTime") != null){
            if (paramMap.get("startTime") != null){
                cancelDateRange.gte(DateUtils.parse(paramMap.get("startTime") + " 00:00:00", DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS).getTime());
            }
            if (paramMap.get("endTime") != null){
                cancelDateRange.lte(DateUtils.parse(paramMap.get("endTime") + " 23:59:59", DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS).getTime());
            }
            boolBuilder.must(cancelDateRange);
        } else{
            cancelDateRange.gte(DateUtils.parse(DateUtils.format(firstDay, DateUtils.FORMAT_DATE_YYYY_MM_DD) + " 00:00:00", DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS).getTime());
            cancelDateRange.lte(DateUtils.parse(DateUtils.format(lastDay, DateUtils.FORMAT_DATE_YYYY_MM_DD) + " 23:59:59", DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS).getTime());
            boolBuilder.must(cancelDateRange);
        }
        boolBuilder.must(cancelDateRange);

        searchSourceBuilder.query(boolBuilder);

        SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE, searchSourceBuilder));

        SearchHits searchHits = searchResponse.getHits();

        HashMap<String, String> hashMap = new HashMap<>();

        if (searchHits.getTotalHits().value != 0L) {
            Long totalHits = searchHits.getTotalHits().value;
            Integer total = totalHits.intValue();

            searchSourceBuilder.size(total);

            searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE, searchSourceBuilder));
            searchHits = searchResponse.getHits();

            SearchHit[] hits = searchHits.getHits();

            for (SearchHit hit : hits) {
                ClaimCase claimCase = JSON.parseObject(hit.getSourceAsString(), ClaimCase.class);
                if(claimCase != null && StringUtils.isNotBlank(claimCase.getStatus())){
                    hashMap.put(claimCase.getClaimCaseNo(), claimCase.getStatus());
                }
            }
        }

        //查询平台开通至今，当月所有结案数量
        searchSourceBuilder.clearRescorers();
        searchSourceBuilder.trackTotalHits(true);

        boolBuilder = QueryBuilders.boolQuery();
        boolBuilder.must(QueryBuilders.existsQuery("claimCaseNo"));

        RangeQueryBuilder checkEndTimeRange = QueryBuilders.rangeQuery("checkEndTime");
        if (paramMap.get("startTime") != null || paramMap.get("endTime") != null){
            if (paramMap.get("startTime") != null){
                checkEndTimeRange.gte(DateUtils.parse(paramMap.get("startTime") + " 00:00:00", DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS).getTime());
            }
            if (paramMap.get("endTime") != null){
                checkEndTimeRange.lte(DateUtils.parse(paramMap.get("endTime") + " 23:59:59", DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS).getTime());
            }
            boolBuilder.must(checkEndTimeRange);
        } else{
            checkEndTimeRange.gte(DateUtils.parse(DateUtils.format(firstDay, DateUtils.FORMAT_DATE_YYYY_MM_DD) + " 00:00:00", DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS).getTime());
            checkEndTimeRange.lte(DateUtils.parse(DateUtils.format(lastDay, DateUtils.FORMAT_DATE_YYYY_MM_DD) + " 23:59:59", DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS).getTime());
            boolBuilder.must(checkEndTimeRange);
        }boolBuilder.must(checkEndTimeRange);

        searchSourceBuilder.query(boolBuilder);

       searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE, searchSourceBuilder));

        searchHits = searchResponse.getHits();

        if (searchHits.getTotalHits().value != 0L) {
            Long totalHits = searchHits.getTotalHits().value;
            Integer total = totalHits.intValue();

            searchSourceBuilder.size(total);

            searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE, searchSourceBuilder));
            searchHits = searchResponse.getHits();

            SearchHit[] hits = searchHits.getHits();

            for (SearchHit hit : hits) {
                ClaimCase claimCase = JSON.parseObject(hit.getSourceAsString(), ClaimCase.class);
                if(claimCase != null && StringUtils.isNotBlank(claimCase.getStatus())){
                    hashMap.put(claimCase.getClaimCaseNo(), claimCase.getStatus());
                }
            }
        }

        //从ClaimCase索引中获取当月匹配的案件号
        List<String> setList = new ArrayList<>(hashMap.keySet());

        SearchSourceBuilder ClaimCaseObjectBuilder = new SearchSourceBuilder();
        ClaimCaseObjectBuilder.trackTotalHits(true);

        BoolQueryBuilder claimCaseObjectBoolBuilder = new BoolQueryBuilder();
        claimCaseObjectBoolBuilder.must(QueryBuilders.existsQuery("auditer"));

        if(paramMap.containsKey("auditer")){
            claimCaseObjectBoolBuilder.must(QueryBuilders.matchQuery("auditer.keyword", paramMap.get("auditer")));
        }
        claimCaseObjectBoolBuilder.must(QueryBuilders.termsQuery("claimCaseNo.keyword", setList));
        ClaimCaseObjectBuilder.query(claimCaseObjectBoolBuilder);
        SearchResponse claimCaseObjectResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, ClaimCaseObjectBuilder));

        SearchHits claimCaseObjectHits = claimCaseObjectResponse.getHits();

        if (claimCaseObjectHits.getTotalHits().value == 0L) {
            return null;
        }
        Long ClaimCaseTotalHits = claimCaseObjectHits.getTotalHits().value;
        Integer ClaimCaseTotal = ClaimCaseTotalHits.intValue();

        ClaimCaseObjectBuilder.size(ClaimCaseTotal);

        claimCaseObjectResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, ClaimCaseObjectBuilder));
        claimCaseObjectHits = claimCaseObjectResponse.getHits();

        SearchHit[] claimCaseObjectSearchHits = claimCaseObjectHits.getHits();

        List<ClaimCaseObjectV1> claimCaseObjectList = new ArrayList<>();
        for (SearchHit hit : claimCaseObjectSearchHits) {
            ClaimCaseObject claimCaseObject = JSON.parseObject(hit.getSourceAsString(), ClaimCaseObject.class);

            ClaimCaseObjectV1 claimCaseObjectV1 = new ClaimCaseObjectV1();
            BeanUtils.copyProperties(claimCaseObject, claimCaseObjectV1);

            if(hashMap.containsKey(claimCaseObjectV1.getClaimCaseNo())){
                claimCaseObjectV1.setClaimCaseStatus(hashMap.get(claimCaseObjectV1.getClaimCaseNo()));
            }
            claimCaseObjectList.add(claimCaseObjectV1);
        }

        Map<String, List<ClaimCaseObjectV1>> collect = claimCaseObjectList.stream().collect(groupingBy(a -> a.getAuditer()));

        return collect;
    }

    @Override
    public List<ClaimCaseObject> findByRiskCodeParam(Map<String, Object> paramMap) {

        if (paramMap.keySet().size() == 0) {
            return null;
        }

        List<ClaimCaseObject> claimCaseObjectList = new ArrayList<>();

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);
        searchSourceBuilder.sort("createTime", SortOrder.DESC);
        BoolQueryBuilder boolBuilder = new BoolQueryBuilder();

        if (paramMap.containsKey("claimCaseId")) {
            boolBuilder.must(QueryBuilders.termQuery("claimCaseId.keyword", paramMap.get("claimCaseId")));
        }

        if (paramMap.containsKey("caseType")) {
            String caseType = (String) paramMap.get("caseType");
            // 众包案件
            if ("YW".equals(caseType) && paramMap.containsKey("riskCode")) {
                String riskCode = (String) paramMap.get("riskCode");    // 险种，财ZHA；意5XA/5XB
                if ("ZHA".equals(riskCode)) {
                    boolBuilder.must(QueryBuilders.termQuery("type", 2));
                } else {
                    boolBuilder.must(QueryBuilders.termQuery("type", 1));
                }
            }
        }

        searchSourceBuilder.query(boolBuilder);

        SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
        SearchHits searchHits = searchResponse.getHits();
        Long searchHitsValue = searchHits.getTotalHits().value;
        int total = searchHitsValue.intValue();
        if (total == 0) {
            return null;
        }
        searchSourceBuilder.size(total);

        searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
        searchHits = searchResponse.getHits();
        SearchHit[] hits = searchHits.getHits();

        for (SearchHit searchHit : hits) {
            ClaimCaseObject claimCaseObject = JSON.parseObject(searchHit.getSourceAsString(), ClaimCaseObject.class);
            claimCaseObjectList.add(claimCaseObject);
        }

        return claimCaseObjectList;
    }

    @Override
    public Map<String, BigDecimal> findAggByParam(Set<String> claimCaseIdSet) {
        if (CollectionUtils.isEmpty(claimCaseIdSet)) {
            return null;
        }

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(0);
        BoolQueryBuilder boolBuilder = new BoolQueryBuilder();

        boolBuilder.must(QueryBuilders.termsQuery("claimCaseId.keyword", claimCaseIdSet));


        String aggsName = "claimCase";
        String topName = "sumAmount";

        searchSourceBuilder.aggregation(AggregationBuilders.terms(aggsName).field("claimCaseId.keyword").size(5000)
                .subAggregation(AggregationBuilders.sum(topName).field("estimatedApprovedMoney")));

        searchSourceBuilder.query(boolBuilder);

        SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
        Aggregations aggregations = searchResponse.getAggregations();

        Aggregation claimCaseAggregation = aggregations.get(aggsName);
        ParsedStringTerms claimCaseParsedStringTerms = (ParsedStringTerms) claimCaseAggregation;

        Map<String, BigDecimal> map = new HashMap<>();
        for (Terms.Bucket bucket : claimCaseParsedStringTerms.getBuckets()) {
            String claimCaseId = bucket.getKeyAsString();
            ParsedSum parsedSum = (ParsedSum) bucket.getAggregations().get(topName);
            BigDecimal sumAppraisalAmountDecimal = new BigDecimal(parsedSum.getValueAsString()).setScale(2, BigDecimal.ROUND_HALF_UP);
            map.put(claimCaseId, sumAppraisalAmountDecimal);
        }
        return map;
    }


    @Override
    public void genCaseObjectImageInfo(String id) {
        ClaimCaseObject caseObject = selectByPrimaryKey(id);
        if (caseObject == null) {
            return;
        }
        try (FileInputStream inputStream = new FileInputStream(getResourceUri("/files/assessCarTemplate.xlsx"));
             Workbook wbCWS = WorkbookFactory.create(inputStream)) {
            Sheet sheetCWS = wbCWS.getSheetAt(0);
            List<ClaimCaseObjectAssessment> caseObjectAssessments = claimCaseObjectAssessmentService.findByClaimCaseObjectId(id);
            AssessmentFatherVo assessmentFather = new AssessmentFatherVo();
            assessmentFather.setIs4S(StringUtils.isNotBlank(caseObject.getIsServiceShop())?caseObject.getIsServiceShop():"");
            BeanUtils.copyProperties(caseObject, assessmentFather);
            List<Assessment> assessments = new ArrayList<>();
            caseObjectAssessments.forEach(c -> {
                Assessment assessment = new Assessment();
                BeanUtils.copyProperties(c, assessment);
                assessment.setRepairName(c.getName());
                assessments.add(assessment);
            });
            String[] checkAuditors = getCheckAuditor(caseObject);
            assessmentFather.setFirstAuditor(checkAuditors[0]);
            assessmentFather.setCheckAuditor(checkAuditors[1]);
            assessmentFather.setAssessmentReport(assessments);
            assessmentFather.setCarOwner(caseObject.getTreatName());
            reportExcelContentCWS(sheetCWS, assessmentFather); //替换字段值
            BufferedImage bufferedImage = Excel2ImageUtil.workRun(wbCWS); //excel转影像
            exportImage(bufferedImage,caseObject); //影像入库
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private String getResourceUri(String resource) {
        return DownLoadCenterController.class.getResource(resource).getPath();
    }

    private void reportExcelContentCWS(Sheet sheet, AssessmentFatherVo assessmentFather) {
        Map<String, Object> estimateInventoryMap = EstimateInventoryEnum.getEnumMapByParentCode("2");
        Map<String, List<Assessment>> assessmentMap = assessmentFather.getAssessmentReport().stream().collect(Collectors.groupingBy(Assessment::getCode));

        int maxLength = 8;
        for (String code : assessmentMap.keySet()) {
            if (maxLength < assessmentMap.get(code).size()) {
                maxLength = assessmentMap.get(code).size();
            }
        }
        excelInsertRow(sheet, 8, maxLength - 1);


        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("carNumber", StringUtils.isNotBlank(assessmentFather.getCarNumber()) ? assessmentFather.getCarNumber() : "");
        dataMap.put("carModel", StringUtils.isNotBlank(assessmentFather.getCarModel()) ? assessmentFather.getCarModel() : "");
        dataMap.put("carEncoding", StringUtils.isNotBlank(assessmentFather.getCarEncoding()) ? assessmentFather.getCarEncoding() : "");
        dataMap.put("firstRegistrationTimeStr", assessmentFather.getFirstRegistrationTime() != null ? kd.common.tool.DateUtils.format(assessmentFather.getFirstRegistrationTime(), kd.common.tool.DateUtils.FORMAT_DATE_PATTERN_1) : "");
        dataMap.put("carOwner", StringUtils.isNotBlank(assessmentFather.getCarOwner()) ? assessmentFather.getCarOwner() : "");
        dataMap.put("lossAssessmentTimeStr", assessmentFather.getLossAssessmentTime() != null ? kd.common.tool.DateUtils.format(assessmentFather.getLossAssessmentTime(), kd.common.tool.DateUtils.FORMAT_DATE_PATTERN_1) : "");
        dataMap.put("repairFactory", StringUtils.isNotBlank(assessmentFather.getRepairFactory()) ? assessmentFather.getRepairFactory() : "");
        dataMap.put("is4S", StringUtils.isNotBlank(assessmentFather.getIs4S()) ? assessmentFather.getIs4S() : "");
        dataMap.put("residualValue", String.valueOf(assessmentFather.getResidualNuclearLossValue() != null ? assessmentFather.getResidualNuclearLossValue() : BigDecimal.ZERO));
        DecimalFormat df2 = new DecimalFormat("#.00");
        dataMap.put("dutyRate", assessmentFather.getDutyRate() != null ? df2.format(assessmentFather.getDutyRate()) + "%" : "");
        dataMap.put("deductFee", String.valueOf(assessmentFather.getDeductFee() != null ? assessmentFather.getDeductFee() : ""));
        dataMap.put("verifyAmout", String.valueOf(assessmentFather.getVerifyAmout() != null ? assessmentFather.getVerifyAmout() : ""));
        dataMap.put("verifyDetail", StringUtils.isNotBlank(assessmentFather.getVerifyDetail()) ? assessmentFather.getVerifyDetail() : "");
        dataMap.put("firstAuditor", StringUtils.isNotBlank(assessmentFather.getFirstAuditor()) ? assessmentFather.getFirstAuditor() : "");
        dataMap.put("checkAuditor", StringUtils.isNotBlank(assessmentFather.getCheckAuditor()) ? assessmentFather.getCheckAuditor() : "");


        Map<String, BigDecimal> totalBigDecimalMap = new HashMap<>();
        totalBigDecimalMap.put("lossAssessment-2-1-total", BigDecimal.ZERO);
        totalBigDecimalMap.put("approvedAmount-2-1-total", BigDecimal.ZERO);

        totalBigDecimalMap.put("lossAssessment-2-2-total", BigDecimal.ZERO);
        totalBigDecimalMap.put("approvedAmount-2-2-total", BigDecimal.ZERO);

        totalBigDecimalMap.put("lossAssessment-total", BigDecimal.ZERO);
        totalBigDecimalMap.put("approvedAmount-total", BigDecimal.ZERO);

        totalBigDecimalMap.put("totalAmount", BigDecimal.ZERO);

        //配件 人工 字段信息内容
        Map<String, List<Map<String, Object>>> detailMap = new HashMap<>();
        //配件 人工 字段信息表 对应列号
        Map<String, Integer> code2Col = new HashMap<>();
        for (String code : estimateInventoryMap.keySet()) {
            List<Map<String, Object>> detailInfoList = new ArrayList<>();
            if (assessmentMap.containsKey(code)) {
                List<Assessment> assessmentList = assessmentMap.get(code);

                for (Assessment assessment : assessmentList) {
                    Map<String, Object> dataInfoMap = new HashMap<>();
                    if (StringUtils.isNotBlank(assessment.getRepairName())) {
                        dataInfoMap.put("repairName-" + code, assessment.getRepairName());
                    } else {
                        dataInfoMap.put("repairName-" + code, "");
                    }
                    if (assessment.getLossAssessment() != null) {
                        dataInfoMap.put("lossAssessment-" + code, assessment.getLossAssessment());
                        String totalKey = "lossAssessment-" + code + "-total";
                        totalBigDecimalMap.put(totalKey, totalBigDecimalMap.get(totalKey).add(assessment.getLossAssessment()));
                        totalBigDecimalMap.put("lossAssessment-total", totalBigDecimalMap.get("lossAssessment-total").add(assessment.getLossAssessment()));
                    } else {
                        dataInfoMap.put("lossAssessment-" + code, "");
                    }
                    if (assessment.getApprovedAmount() != null) {
                        dataInfoMap.put("approvedAmount-" + code, assessment.getApprovedAmount());
                        String totalKey = "approvedAmount-" + code + "-total";
                        totalBigDecimalMap.put(totalKey, totalBigDecimalMap.get(totalKey).add(assessment.getApprovedAmount()));
                        totalBigDecimalMap.put("approvedAmount-total", totalBigDecimalMap.get("approvedAmount-total").add(assessment.getApprovedAmount()));
                    } else {
                        dataInfoMap.put("approvedAmount-" + code, "");
                    }

                    detailInfoList.add(dataInfoMap);
                }


            } else {
                Map<String, Object> dataInfoMap = new HashMap<>();
                dataInfoMap.put("repairName-" + code, "");
                dataInfoMap.put("lossAssessment-" + code, "");
                dataInfoMap.put("approvedAmount-" + code, "");
                detailInfoList.add(dataInfoMap);
            }
            detailMap.put(code, detailInfoList);
            code2Col.put("repairName-" + code, 0);
            code2Col.put("lossAssessment-" + code, 0);
            code2Col.put("approvedAmount-" + code, 0);
        }

        dataMap.put("lossAssessment-total-chinese", ConvertUpMoneyTool.toChinese(String.valueOf(totalBigDecimalMap.get("lossAssessment-total"))));
        dataMap.put("approvedAmount-total-chinese", ConvertUpMoneyTool.toChinese(String.valueOf(totalBigDecimalMap.get("approvedAmount-total"))));

        BigDecimal totalAmount = totalBigDecimalMap.get("approvedAmount-total").subtract(assessmentFather.getResidualValue() != null ? assessmentFather.getResidualValue() : BigDecimal.ZERO);
        totalBigDecimalMap.put("totalAmount", totalAmount);
        dataMap.put("totalAmount-chinese", ConvertUpMoneyTool.toChinese(String.valueOf(totalBigDecimalMap.get("totalAmount"))));

        int minRow = 0;

        for (int i = 0; i <= sheet.getLastRowNum(); i++) {
            Row dataRow = sheet.getRow(i);
            for (int ii = 0; ii < dataRow.getLastCellNum(); ii++) {
                Cell cell = dataRow.getCell(ii);
                if (cell != null) {
                    String data = cell.getStringCellValue();
                    if (dataMap.containsKey(data)) {
                        String value = dataMap.get(data).toString();
                        cell.setCellValue(value);
                    }
                    if (totalBigDecimalMap.containsKey(data)) {
                        String value = totalBigDecimalMap.get(data).toString();
                        cell.setCellValue(value);
                    }
                    if (code2Col.containsKey(data)) {
                        code2Col.put(data, ii);
                        minRow = i;
                    }
                }
            }
        }

        for (String code : estimateInventoryMap.keySet()) {
            List<Map<String, Object>> detailInfoList = detailMap.get(code);
            for (int i = 0; i < maxLength; i++) {
                Row row = sheet.getRow(minRow + i);
                if (row == null) {
                    row = sheet.createRow(minRow + i);
                    Cell sourceCell = null;
                    Cell cell = null;
                    for (int ii = 0; ii < sheet.getRow(minRow).getLastCellNum(); ii++) {
                        cell = row.createCell(ii);
                        sourceCell = sheet.getRow(minRow).getCell(ii);
                        cell.setCellStyle(sourceCell.getCellStyle());
                    }
                }
                for (String key : code2Col.keySet()) {
                    if (key.endsWith(code)) {
                        int colNum = code2Col.get(key);
                        row.getCell(colNum).setCellValue(detailInfoList.size() > i ? (detailInfoList.get(i).get(key).toString()) : "");
                    }
                }
            }
        }
    }

    //导出影像信息入库
    private void exportImage(BufferedImage bufferedImage,ClaimCaseObject object) throws IOException {
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        /*
         * png 改为 jpeg格式：
         * 推送海峡影像时影像类型需为 image/png,image/jpeg...
         * png格式无法推送海峡影像
         * 测试发现修改为png 和 image/png时影像无法正常显示
         * 故改为 jpeg 和 image/jpeg
         */
        ImageIO.write(bufferedImage, "JPEG", os);
        InputStream inputStream = new ByteArrayInputStream(os.toByteArray());
        try {
            String claimCaseAttachId = Tool.uuid();
            String objectId = kd.beijingRoastDuck.util.AliOssBucketPrefix.genFullObject(KD_DUCK_INTERNAL, object.getClaimCaseNo(), claimCaseAttachId);
            AliOssToolV3.uploadByStreamOuter(AliOssToolV3.PRIVATE_BUCKET_ID, objectId, inputStream, "image/jpeg");
            ClaimCaseAttach claimCaseAttach = new ClaimCaseAttach();
            claimCaseAttach.setId(claimCaseAttachId);
            claimCaseAttach.setClaimCaseId(object.getClaimCaseId());
            claimCaseAttach.setClaimCaseNo(object.getClaimCaseNo());
            claimCaseAttach.setContentType("三者定损单");
            claimCaseAttach.setFileName(claimCaseAttachId);
            claimCaseAttach.setFileObjectId(objectId);
            claimCaseAttach.setFileContentType("image/jpeg");
            claimCaseAttach.setIsClear(1);
            claimCaseAttach.setStatus(1);
            Date date = new Date();
            claimCaseAttach.setCreateTime(date);
            claimCaseAttach.setAttachFrom(4);
            claimCaseAttach.setReplenishNub(-1);
            claimCaseAttach.setCreator("系统");
            int resCount = claimCaseAttachDao.insertSelective(claimCaseAttach);
            ClaimCaseLog claimCaseLog = new ClaimCaseLog();
            claimCaseLog.setId(Tool.uuid());
            claimCaseLog.setClaimCaseId(object.getClaimCaseId());
            claimCaseLog.setLevel(1);
            claimCaseLog.setSecurityClassification(100);
            claimCaseLog.setType(1);
            claimCaseLog.setStatus(String.valueOf(resCount));
            claimCaseLog.setPosition("系统");
            claimCaseLog.setDescription("案件估损初审通过-》生成车物定损单");
            claimCaseLog.setCreator("系统");
            claimCaseLog.setCreateTime(date);
            claimCaseLogDao.insertSelective(claimCaseLog);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }



    private static String[] getCheckAuditor(ClaimCaseObject object) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);
        searchSourceBuilder.size(1000);
        searchSourceBuilder.sort("createTime", SortOrder.DESC);
        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();

        boolBuilder.must(QueryBuilders.termQuery("claimCaseObjectId.keyword", object.getId()));
        boolBuilder.must(QueryBuilders.existsQuery("creator"));
        searchSourceBuilder.query(boolBuilder);
        SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT_LOG, searchSourceBuilder));
        SearchHits searchHits = searchResponse.getHits();
        SearchHit[] hits = searchHits.getHits();
        String checkAuditor = "";
        String checkAuditorAgain = "";
        for (SearchHit hit : hits) {
            ClaimCaseObjectLog claimCaseObjectLog = JSON.parseObject(hit.getSourceAsString(), ClaimCaseObjectLog.class);
            String description = claimCaseObjectLog.getDescription();
                if (StringUtils.isBlank(checkAuditor) && StringUtils.isNotBlank(description) && description.startsWith("估损复核通过了此赔付对象信息")) {
                    checkAuditor = claimCaseObjectLog.getCreator().split("-")[0];
                }
                if (StringUtils.isBlank(checkAuditorAgain) && StringUtils.isNotBlank(description) && description.startsWith("估损保司审核通过了此赔付对象信息")) {
                    checkAuditorAgain = claimCaseObjectLog.getCreator().split("-")[0];
                }
        }
        return new String[]{checkAuditor,checkAuditorAgain};
    }

    /**
     * @param sheet   要操作的sheet
     * @param starRow 在第几行下
     * @param rows    插入几行
     */
    private void excelInsertRow(Sheet sheet, int starRow, int rows) {
        sheet.shiftRows(starRow + 1, sheet.getLastRowNum(), rows, true, false);
        starRow = starRow - 1;
        for (int i = 0; i < rows; i++) {
            Row sourceRow = null;
            Row targetRow = null;
            Cell sourceCell = null;
            Cell targetCell = null;
            short m;
            starRow = starRow + 1;
            sourceRow = sheet.getRow(starRow);
            targetRow = sheet.createRow(starRow + 1);
            targetRow.setHeight(sourceRow.getHeight());
            for (m = sourceRow.getFirstCellNum(); m < sourceRow.getLastCellNum(); m++) {
                sourceCell = sourceRow.getCell(m);
                targetCell = targetRow.createCell(m);
                targetCell.setCellStyle(sourceCell.getCellStyle());
                targetCell.setCellType(sourceCell.getCellType());
            }
        }
    }

    @Override
    public List<ClaimCaseObject> findListByClaimCaseNo(String claimCaseNo) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);
        searchSourceBuilder.sort("createTime", SortOrder.DESC);
        BoolQueryBuilder boolBuilder = new BoolQueryBuilder();
        boolBuilder.must(QueryBuilders.termQuery("claimCaseNo.keyword",claimCaseNo));
        searchSourceBuilder.query(boolBuilder);

        SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
        SearchHits searchHits = searchResponse.getHits();
        Long searchHitsValue = searchHits.getTotalHits().value;
        int total = searchHitsValue.intValue();
        if (total == 0) {
            return null;
        }
        searchSourceBuilder.size(total);

        searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
        searchHits = searchResponse.getHits();
        SearchHit[] hits = searchHits.getHits();

        List<ClaimCaseObject> claimCaseObjectList = new ArrayList<>();
        for (SearchHit searchHit : hits) {
            ClaimCaseObject claimCaseObject = JSON.parseObject(searchHit.getSourceAsString(), ClaimCaseObject.class);
            claimCaseObjectList.add(claimCaseObject);
        }
        return claimCaseObjectList;
    }

    @Override
    public List<ClaimCaseObject> findListByClaimCaseIdSet(Set<String> claimCaseIdSet) {
        if (CollectionUtils.isEmpty(claimCaseIdSet)) {
            return null;
        }
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);
        searchSourceBuilder.size(0);
        BoolQueryBuilder boolBuilder = new BoolQueryBuilder();
        boolBuilder.must(QueryBuilders.termsQuery("claimCaseId.keyword",claimCaseIdSet));
        searchSourceBuilder.query(boolBuilder);
        SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
        long total = searchResponse.getHits().getTotalHits().value;
        if (total == 0) {
            return null;
        }
        searchSourceBuilder.size((int)total);
        searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
        List<ClaimCaseObject> caseObjectList = new ArrayList<>();
        for (SearchHit hit : searchResponse.getHits().getHits()) {
            ClaimCaseObject caseObject = JSON.parseObject(hit.getSourceAsString(),ClaimCaseObject.class);
            caseObjectList.add(caseObject);
        }
        return caseObjectList;
    }

    /**
     * @author: hoomik
     * @description: 修改案件赔付对象状态并添加日志
     * @date: 2024/11/07 14:32
     * @param
     * @return
     */
    @Override
    public int nextSignTask(Map<String, Object> modifyMap) {
        if(modifyMap.containsKey("claimCaseObjectUpdate")){
            claimCaseObjectDao.updateByPrimaryKeySelective((ClaimCaseObject) modifyMap.get("claimCaseObjectUpdate"));
        }

        if(modifyMap.containsKey("claimCaseLog")){
            claimCaseLogDao.insertSelective((ClaimCaseLog) modifyMap.get("claimCaseLog"));
        }
        return 1;
    }

    @Override
    public ClaimCaseObjectScrollVo findListByScroll() {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(3000);
        BoolQueryBuilder boolBuilder = new BoolQueryBuilder();
        boolBuilder.must(QueryBuilders.existsQuery("auditer"));
        searchSourceBuilder.query(boolBuilder);
        SearchResponse searchResponse = ESHighLevelRESTClientTool.scrollSearchInit(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder);
        long totalVal = searchResponse.getHits().getTotalHits().value;
        if (totalVal == 0) {
            return null;
        }
        String scrollId = searchResponse.getScrollId();
        SearchHits searchHits = searchResponse.getHits();
        List<ClaimCaseObject> claimCaseObjectList = new ArrayList<>();
        for (SearchHit hit : searchHits.getHits()) {
            ClaimCaseObject claimCaseObject = JSON.parseObject(hit.getSourceAsString(), ClaimCaseObject.class);
            claimCaseObjectList.add(claimCaseObject);
        }
        ClaimCaseObjectScrollVo claimCaseObjectScrollVo = new ClaimCaseObjectScrollVo();
        claimCaseObjectScrollVo.setScrollId(scrollId);
        claimCaseObjectScrollVo.setClaimCaseObjectList(claimCaseObjectList);
        return claimCaseObjectScrollVo;
    }

    @Override
    public ClaimCaseObjectScrollVo findListByScrollId(String scrollId) {
        SearchResponse searchResponse = ESHighLevelRESTClientTool.scrollSearchSyn(scrollId);
        SearchHits searchHits = searchResponse.getHits();
        List<ClaimCaseObject> claimCaseObjectList = new ArrayList<>();
        String _scroll_id = "";
        if (searchHits.getTotalHits().value == 0l) {
        } else {
            _scroll_id = searchResponse.getScrollId();
            SearchHit[] hitArray = searchHits.getHits();
            for (SearchHit searchHit : hitArray) {
                claimCaseObjectList.add(JSON.parseObject(searchHit.getSourceAsString(), ClaimCaseObject.class));
            }
            if (claimCaseObjectList.size() == 0) {
                ESHighLevelRESTClientTool.clearScollSearchSyn(ESHighLevelRESTClientTool.genClearScollSearchReq(new ArrayList<String>() {{
                    this.add(scrollId);
                }}));
            }

        }
        ClaimCaseObjectScrollVo claimCaseObjectScrollVo = new ClaimCaseObjectScrollVo();
        claimCaseObjectScrollVo.setScrollId(_scroll_id);
        claimCaseObjectScrollVo.setClaimCaseObjectList(claimCaseObjectList);
        return claimCaseObjectScrollVo;
    }

    @Override
    public PageInfo<ClaimCaseObjectReq> claimCaseObjectListJD(Map<String, Object> paramMap, PageParam pp) {

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);
        searchSourceBuilder.from(pp.getPageNum() != 0 ? (pp.getPageNum() - 1) * pp.getPageSize() : 0);
        searchSourceBuilder.size(pp.getPageSize());
        searchSourceBuilder.sort("createTime", SortOrder.DESC);
        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();

        Integer comeFrom = Integer.parseInt(paramMap.get("comeFrom").toString());

        // 客服岗 或 预处理岗
        if (comeFrom == 1 || comeFrom == 4) {
            boolBuilder.must(QueryBuilders.termQuery("auditer.keyword", paramMap.get("userId")));
        }


        if (paramMap.containsKey("status")) {

            boolBuilder.must(QueryBuilders.termQuery("isCaseClosed", 0));

            Integer status = Integer.parseInt(paramMap.get("status").toString());

            // 采集任务
            if (status == 0) {
                boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX21.getCode()));
            }
            // 理算任务
            if (status == 1) {
                boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX31.getCode()));
            }
            // 退回任务
            if (status == 2) {
                BoolQueryBuilder statusQueryBuilder = new BoolQueryBuilder();
                statusQueryBuilder.should(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX24.getCode()));
                statusQueryBuilder.should(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX34.getCode()));
                statusQueryBuilder.should(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX27.getCode()));
                statusQueryBuilder.should(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX37.getCode()));
                boolBuilder.must(statusQueryBuilder);
            }
            // 处理岗
            if (comeFrom == 2) {
                // 采集审核(个人池)
                if (status == 3) {
                    boolBuilder.must(QueryBuilders.termQuery("checkAuditer.keyword", paramMap.get("userId")));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX22.getCode()));
                }
                // 理算审核(个人池)
                if (status == 4) {
                    boolBuilder.must(QueryBuilders.termQuery("checkAuditer.keyword", paramMap.get("userId")));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX32.getCode()));
                }
                // 采集审核(公共池)
                if (status == 5) {
                    boolBuilder.mustNot(QueryBuilders.existsQuery("checkAuditer"));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX22.getCode()));
                }
                // 理算审核(公共池)
                if (status == 6) {
                    boolBuilder.mustNot(QueryBuilders.existsQuery("checkAuditer"));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX32.getCode()));
                }
            }
            // 保司岗
            if (comeFrom == 3) {
                // 采集审核(个人池)
                if (status == 3) {
                    boolBuilder.must(QueryBuilders.termQuery("insAuditer.keyword", paramMap.get("userId")));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX25.getCode()));
                }
                // 理算审核(个人池)
                if (status == 4) {
                    boolBuilder.must(QueryBuilders.termQuery("insAuditer.keyword", paramMap.get("userId")));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX35.getCode()));
                }
                // 采集审核(公共池)
                if (status == 5) {
                    boolBuilder.mustNot(QueryBuilders.existsQuery("insAuditer"));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX25.getCode()));
                }
                // 理算审核(公共池)
                if (status == 6) {
                    boolBuilder.mustNot(QueryBuilders.existsQuery("insAuditer"));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX35.getCode()));
                }
            }
            //新增签字任务
            if(status == 7){
                BoolQueryBuilder statusQueryBuilder = new BoolQueryBuilder();
                statusQueryBuilder.should(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX38.getCode()));
                statusQueryBuilder.should(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX39.getCode()));
                boolBuilder.must(statusQueryBuilder);
                boolBuilder.must(QueryBuilders.termQuery("auditer.keyword", paramMap.get("userId")));
            }
        }

        // 案件号
        if (paramMap.containsKey("claimCaseNo")) {
            boolBuilder.must(QueryBuilders.wildcardQuery("claimCaseNo.keyword", "*" + paramMap.get("claimCaseNo") + "*"));
        }

        // 被保人姓名和证件号
        if (paramMap.containsKey("treatName") || paramMap.containsKey("treatIdNum")) {
            Map<String, Object> treatParam = new HashMap<>();
            if (paramMap.containsKey("treatName")) {
                treatParam.put("treatName", paramMap.get("treatName"));
            }
            if (paramMap.containsKey("treatIdNum")) {
                treatParam.put("treatIdNum", paramMap.get("treatIdNum"));
            }
            List<ClaimCase> byTreatParam = claimCaseService.findByTreatParam(treatParam);
            if (CollectionUtils.isNotEmpty(byTreatParam)) {
                boolBuilder.must(QueryBuilders.termsQuery("claimCaseNo.keyword", byTreatParam.stream().map(ClaimCase::getClaimCaseNo).collect(Collectors.toList())));
            }else {
                boolBuilder.must(QueryBuilders.termsQuery("claimCaseNo.keyword", ""));
            }
        }

        // 类型
        if (paramMap.containsKey("type")) {
            boolBuilder.must(QueryBuilders.termQuery("type", paramMap.get("type")));
        }

        // 类型
        if (paramMap.containsKey("category")) {
            boolBuilder.must(QueryBuilders.termQuery("category", paramMap.get("category")));
        }

        // 保司标识
        if (paramMap.containsKey("insCode")) {
            if (paramMap.get("insCode").toString().equals("DD")) {
                BoolQueryBuilder shouldQueryBuilder = new BoolQueryBuilder();
                shouldQueryBuilder.should(QueryBuilders.termQuery("insCode.keyword", paramMap.get("insCode")));
                shouldQueryBuilder.should(new BoolQueryBuilder().mustNot(QueryBuilders.existsQuery("insCode")));
                boolBuilder.must(shouldQueryBuilder);
            } else {
                boolBuilder.must(QueryBuilders.termQuery("insCode.keyword", paramMap.get("insCode")));
            }
        }

        searchSourceBuilder.query(boolBuilder);
        SearchSourceBuilder searchSourceBuilderModifyTime = new SearchSourceBuilder();
        searchSourceBuilderModifyTime.query(boolBuilder);
        searchSourceBuilderModifyTime.trackTotalHits(true);
        searchSourceBuilderModifyTime.from(pp.getPageNum() != 0 ? (pp.getPageNum() - 1) * pp.getPageSize() : 0);
        searchSourceBuilderModifyTime.size(pp.getPageSize());
        searchSourceBuilderModifyTime.sort("modifyTime", SortOrder.DESC);
        SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilderModifyTime));
        SearchHits searchHits = searchResponse.getHits();
        SearchHit[] hits = searchHits.getHits();
        List<ClaimCaseObjectReq> claimCaseObjectList = new ArrayList<>();
        List<String> claimCaseIdList = new ArrayList<>();
        for (SearchHit searchHit : hits) {

            ClaimCaseObject claimCaseObject = JSON.parseObject(searchHit.getSourceAsString(), ClaimCaseObject.class);
//            claimCaseObject.setLabel(null);
            claimCaseIdList.add(claimCaseObject.getClaimCaseId());

            ClaimCaseObjectReq claimCaseObjectReq = new ClaimCaseObjectReq();
            BeanUtils.copyProperties(claimCaseObject, claimCaseObjectReq);
            claimCaseObjectList.add(claimCaseObjectReq);
        }

        boolBuilder = QueryBuilders.boolQuery();
        boolBuilder.must(QueryBuilders.termsQuery("id.keyword", claimCaseIdList));
//        if (paramMap.containsKey("label")) {
//            boolBuilder.must(QueryBuilders.wildcardQuery("label.keyword", "*" + paramMap.get("label") + "*"));
//        }
        searchSourceBuilder.clearRescorers();
        searchSourceBuilder.from(0);
        searchSourceBuilder.size(pp.getPageSize());
        searchSourceBuilder.query(boolBuilder);
        searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE, searchSourceBuilder));
        SearchHits otherHits = searchResponse.getHits();
        hits = otherHits.getHits();
        for (SearchHit searchHit : hits) {
            ClaimCase claimCase = JSON.parseObject(searchHit.getSourceAsString(), ClaimCase.class);
            for (ClaimCaseObjectReq claimCaseObjectReq : claimCaseObjectList) {
                if (claimCaseObjectReq.getClaimCaseId().equals(claimCase.getId())) {
                    claimCaseObjectReq.setStartDate(claimCase.getStartDate());
                    claimCaseObjectReq.setTreatDate(claimCase.getTreatDate());
                    claimCaseObjectReq.setLabel(claimCase.getLabel());
                    claimCaseObjectReq.setProvince(claimCase.getProvince());
                }
            }
        }

//        if (paramMap.containsKey("label")) {
//            claimCaseObjectList = claimCaseObjectList.stream().filter(s->StringUtils.isNotBlank(s.getLabel())).collect(Collectors.toList());
//        }

        // 预处理岗
        if (comeFrom == 4) {
            List<String> managerIdList = new ArrayList<>();
            String roleId = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "PRETREATMENT_ROLE", "ROLE_ID");
            Role role = roleDao.selectByPrimaryKey(roleId);
            if (role != null) {
                boolBuilder = QueryBuilders.boolQuery();
                managerIdList.addAll(trManagerRoleDao.findByRoleId(roleId));
                BoolQueryBuilder shouldQueryBuilder = new BoolQueryBuilder();
                for (String managerId : managerIdList) {
                    shouldQueryBuilder.should(QueryBuilders.wildcardQuery("creator.keyword", "*" + managerId));
                }
                boolBuilder.must(shouldQueryBuilder);
                boolBuilder.must(QueryBuilders.termsQuery("claimCaseId.keyword", claimCaseIdList));
                boolBuilder.must(QueryBuilders.termQuery("position.keyword", role.getName()));
                boolBuilder.must(QueryBuilders.termQuery("type", ClaimCaseLogTypeEnum.客服沟通备注记录.getCode()));
                String aggsName = "claimCaseLog";
                String topName = "top";
                searchSourceBuilder.clearRescorers();
                searchSourceBuilder.from(0);
                searchSourceBuilder.size(0);
                searchSourceBuilder.query(boolBuilder);
                searchSourceBuilder.aggregation(AggregationBuilders.terms(aggsName).field("claimCaseId.keyword").size(pp.getPageSize())
                        .subAggregation(AggregationBuilders.topHits(topName).sort("createTime", SortOrder.DESC).size(1)));
                searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_LOG, searchSourceBuilder));
                // 获取聚合结果aggregations
                Aggregations aggregations = searchResponse.getAggregations();

                Aggregation aggregation = aggregations.get(aggsName);   // claimCaseLog 部分
                ParsedStringTerms stringTerms = (ParsedStringTerms) aggregation;

                // 循环遍历得到topHits中的数据
                for (Terms.Bucket bucket : stringTerms.getBuckets()) {
                    TopHits tophits = bucket.getAggregations().get(topName);// 获取高亮
                    SearchHits tophitsHits = tophits.getHits();
                    for (SearchHit searchHit : tophitsHits) {
                        ClaimCaseLog claimCaseLog = JSON.parseObject(searchHit.getSourceAsString(), ClaimCaseLog.class);
                        for (ClaimCaseObjectReq claimCaseObjectReq : claimCaseObjectList) {
                            if (claimCaseObjectReq.getClaimCaseId().equals(claimCaseLog.getClaimCaseId())) {
                                claimCaseObjectReq.setProcessingTime(claimCaseLog.getCreateTime());
                                claimCaseObjectReq.setProcessingDescription(claimCaseLog.getDescription());
                            }
                        }
                    }
                }
            }
        }

        if(paramMap.containsKey("status")){
            Integer status = Integer.parseInt(paramMap.get("status").toString());

            //如果是签字状态的化
            if(status == 7){
                //查询已经发起的总数和完成数
                List<String> claimCaseObjectIdList = claimCaseObjectList.stream().map(ClaimCaseObjectReq::getId).collect(Collectors.toList());
                boolBuilder = QueryBuilders.boolQuery();
                boolBuilder.must(QueryBuilders.termsQuery("claimCaseObjectId.keyword",claimCaseObjectIdList));
                boolBuilder.mustNot(QueryBuilders.termQuery("status",-1));
                searchSourceBuilder.clearRescorers();
                searchSourceBuilder.size(1000);
                searchSourceBuilder.query(boolBuilder);

                searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.SIGN_TEMPLATE_PUSH_LOG, searchSourceBuilder));
                SearchHits searchHits1 = searchResponse.getHits();
                hits = searchHits1.getHits();
                Map<String,Integer> sumPush = new HashMap<>();
                Map<String,Integer> successPush = new HashMap<>();
                for (SearchHit searchHit : hits) {
                    SignTemplatePushLog signTemplatePushLog = JSON.parseObject(searchHit.getSourceAsString(), SignTemplatePushLog.class);
                    if(!sumPush.containsKey(signTemplatePushLog.getClaimCaseObjectId())){
                        sumPush.put(signTemplatePushLog.getClaimCaseObjectId(),0);
                    }
                    sumPush.put(signTemplatePushLog.getClaimCaseObjectId(),sumPush.get(signTemplatePushLog.getClaimCaseObjectId())+1);

                    if(signTemplatePushLog.getStatus() == 6){
                        if(!successPush.containsKey(signTemplatePushLog.getClaimCaseObjectId())){
                            successPush.put(signTemplatePushLog.getClaimCaseObjectId(),0);
                        }
                        successPush.put(signTemplatePushLog.getClaimCaseObjectId(),successPush.get(signTemplatePushLog.getClaimCaseObjectId())+1);
                    }
                }
                claimCaseObjectList.stream().forEach(claimCaseObjectReq -> {
                    if(sumPush.containsKey(claimCaseObjectReq.getId())){
                        claimCaseObjectReq.setSumSignPush(sumPush.get(claimCaseObjectReq.getId()));
                    }
                    if(successPush.containsKey(claimCaseObjectReq.getId())){
                        claimCaseObjectReq.setSuccessSignPush(successPush.get(claimCaseObjectReq.getId()));
                    }
                });
            }
        }

        PageInfo<ClaimCaseObjectReq> pageInfo = new PageInfo<>(claimCaseObjectList);
        Long totalHits = searchHits.getTotalHits().value;
//        Long totalHits = Long.valueOf(claimCaseObjectList.size());
        Integer total = totalHits.intValue();
        Integer pages = 0;

        //计算总页数
        if (total == -1) {
            pages = 1;
        }
        if (pp.getPageSize() > 0) {
            pages = (int) (total / pp.getPageSize() + ((total % pp.getPageSize() == 0) ? 0 : 1));
        }
        pageInfo.setPageNum(pp.getPageNum());
        pageInfo.setPageSize(pp.getPageSize());
        pageInfo.setTotal(total);
        pageInfo.setPages(pages);
        pageInfo.setSize(pp.getPageSize());
        pageInfo.setStartRow(pp.getPageNum() > 1 ? (pp.getPageNum() - 1) * pp.getPageSize() + 1 : 1);
        pageInfo.setEndRow(pp.getPageNum() > 1 ? pp.getPageNum() * pp.getPageSize() : pp.getPageSize());
        pageInfo.setList(claimCaseObjectList);
        pageInfo.setNavigatePages(8);

        // 计算导航页
        int[] navigatepageNums = null;
        navigatepageNums = ESTool.calcNavigatepageNums(pageInfo.getPages(), 8, navigatepageNums, pp.getPageNum());
        pageInfo.setNavigatepageNums(navigatepageNums);

        // 第一页
        int firstPage = 0;
        // 前一页
        int prePage = 0;
        // 下一页
        int nextPage = 0;
        // 最后一页
        int lastPage = 0;
        // 计算前后页，第一页，最后一页
        ESTool.calcPage(navigatepageNums, firstPage, lastPage, prePage, nextPage,
                pp.getPageNum(), pages, pageInfo
        );

        // 是否为第一页
        boolean isFirstPage = false;
        // 是否为最后一页
        boolean isLastPage = false;
        // 是否有前一页
        boolean hasPreviousPage = false;
        // 是否有下一页
        boolean hasNextPage = false;
        // 判断页面边界
        ESTool.judgePageBoudary(isFirstPage, isLastPage, hasPreviousPage, hasNextPage,
                pp.getPageNum(), pages, pageInfo
        );

        return pageInfo;
    }

    @Override
    public PageInfo<ClaimCaseObjectReq> claimCaseObjectList4BSJD(Map<String, Object> paramMap, PageParam pp) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);
        searchSourceBuilder.from(pp.getPageNum() != 0 ? (pp.getPageNum() - 1) * pp.getPageSize() : 0);
        searchSourceBuilder.size(pp.getPageSize());
        searchSourceBuilder.sort("createTime", SortOrder.DESC);
        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();

        Integer comeFrom = Integer.parseInt(paramMap.get("comeFrom").toString());

        if (paramMap.containsKey("status")) {

            boolBuilder.must(QueryBuilders.termQuery("isCaseClosed", 0));
            Integer status = Integer.parseInt(paramMap.get("status").toString());

            // 处理岗
            if (comeFrom == 2) {
                // 采集审核(个人池)
                if (status == 3) {
                    boolBuilder.must(QueryBuilders.termQuery("checkAuditer.keyword", paramMap.get("userId")));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX22.getCode()));
                }
                // 理算审核(个人池)
                if (status == 4) {
                    boolBuilder.must(QueryBuilders.termQuery("checkAuditer.keyword", paramMap.get("userId")));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX32.getCode()));
                }
                // 采集审核(公共池)
                if (status == 5) {
                    boolBuilder.mustNot(QueryBuilders.existsQuery("checkAuditer"));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX22.getCode()));
                }
                // 理算审核(公共池)
                if (status == 6) {
                    boolBuilder.mustNot(QueryBuilders.existsQuery("checkAuditer"));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX32.getCode()));
                }
            }
            if (comeFrom == 3) {
                // 采集审核(个人池)
                if (status == 3) {
                    boolBuilder.must(QueryBuilders.termQuery("insAuditer.keyword", paramMap.get("userId")));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX25.getCode()));
                }
                // 理算审核(个人池)
                if (status == 4) {
                    boolBuilder.must(QueryBuilders.termQuery("insAuditer.keyword", paramMap.get("userId")));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX35.getCode()));
                }
                // 采集审核(公共池)
                if (status == 5) {
                    boolBuilder.mustNot(QueryBuilders.existsQuery("insAuditer"));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX25.getCode()));
                }
                // 理算审核(公共池)
                if (status == 6) {
                    boolBuilder.mustNot(QueryBuilders.existsQuery("insAuditer"));
                    boolBuilder.must(QueryBuilders.termQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX35.getCode()));
                }
            }
        }

        // 案件号
        if (paramMap.containsKey("claimCaseNo")) {
            boolBuilder.must(QueryBuilders.wildcardQuery("claimCaseNo.keyword", "*" + paramMap.get("claimCaseNo") + "*"));
        }

        // 被保人姓名和证件号
        if (paramMap.containsKey("treatName") || paramMap.containsKey("treatIdNum")) {
            Map<String, Object> treatParam = new HashMap<>();
            if (paramMap.containsKey("treatName")) {
                treatParam.put("treatName", paramMap.get("treatName"));
            }
            if (paramMap.containsKey("treatIdNum")) {
                treatParam.put("treatIdNum", paramMap.get("treatIdNum"));
            }
            List<ClaimCase> byTreatParam = claimCaseService.findByTreatParam(treatParam);
            if (CollectionUtils.isNotEmpty(byTreatParam)) {
                boolBuilder.must(QueryBuilders.termsQuery("claimCaseNo.keyword", byTreatParam.stream().map(ClaimCase::getClaimCaseNo).collect(Collectors.toList())));
            }else {
                boolBuilder.must(QueryBuilders.termsQuery("claimCaseNo.keyword", ""));
            }
        }

        // 类型
        if (paramMap.containsKey("type")) {
            boolBuilder.must(QueryBuilders.termQuery("type", paramMap.get("type")));
        }

        // 类型
        if (paramMap.containsKey("category")) {
            boolBuilder.must(QueryBuilders.termQuery("category", paramMap.get("category")));
        }

        // 保司标识
        if (paramMap.containsKey("insCode")) {
            if (paramMap.get("insCode").toString().equals("DD")) {
                BoolQueryBuilder shouldQueryBuilder = new BoolQueryBuilder();
                shouldQueryBuilder.should(QueryBuilders.termQuery("insCode.keyword", paramMap.get("insCode")));
                shouldQueryBuilder.should(new BoolQueryBuilder().mustNot(QueryBuilders.existsQuery("insCode")));
                boolBuilder.must(shouldQueryBuilder);
            } else {
                boolBuilder.must(QueryBuilders.termQuery("insCode.keyword", paramMap.get("insCode")));
            }
        }

        searchSourceBuilder.query(boolBuilder);
        SearchSourceBuilder searchSourceBuilderModifyTime = new SearchSourceBuilder();
        searchSourceBuilderModifyTime.query(boolBuilder);
        searchSourceBuilderModifyTime.trackTotalHits(true);
        searchSourceBuilderModifyTime.from(pp.getPageNum() != 0 ? (pp.getPageNum() - 1) * pp.getPageSize() : 0);
        searchSourceBuilderModifyTime.size(pp.getPageSize());
        searchSourceBuilderModifyTime.sort("modifyTime", SortOrder.DESC);
        SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilderModifyTime));
        SearchHits searchHits = searchResponse.getHits();
        SearchHit[] hits = searchHits.getHits();
        List<ClaimCaseObjectReq> claimCaseObjectList = new ArrayList<>();
        List<String> claimCaseIdList = new ArrayList<>();
        for (SearchHit searchHit : hits) {
            ClaimCaseObject claimCaseObject = JSON.parseObject(searchHit.getSourceAsString(), ClaimCaseObject.class);
            claimCaseIdList.add(claimCaseObject.getClaimCaseId());

            ClaimCaseObjectReq claimCaseObjectReq = new ClaimCaseObjectReq();
            BeanUtils.copyProperties(claimCaseObject, claimCaseObjectReq);
            claimCaseObjectList.add(claimCaseObjectReq);

        }
        BoolQueryBuilder boolBuilderRet = QueryBuilders.boolQuery();
        boolBuilderRet.must(QueryBuilders.termsQuery("id.keyword", claimCaseIdList));
        searchSourceBuilder.clearRescorers();
        searchSourceBuilder.from(0);
        searchSourceBuilder.size(pp.getPageSize());
        searchSourceBuilder.query(boolBuilderRet);
        searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE, searchSourceBuilder));
        SearchHits searchHitsRet = searchResponse.getHits();
        SearchHit[] hitsRet = searchHitsRet.getHits();
        for (SearchHit searchHit : hitsRet) {
            ClaimCase claimCase = JSON.parseObject(searchHit.getSourceAsString(), ClaimCase.class);
            for (ClaimCaseObjectReq claimCaseObjectReq : claimCaseObjectList) {
                if (claimCaseObjectReq.getClaimCaseId().equals(claimCase.getId())) {
                    claimCaseObjectReq.setStartDate(claimCase.getStartDate());
                    claimCaseObjectReq.setTreatDate(claimCase.getTreatDate());
                    claimCaseObjectReq.setLabel(claimCase.getLabel());
                    claimCaseObjectReq.setProvince(claimCase.getProvince());
                }
            }
        }
        PageInfo<ClaimCaseObjectReq> pageInfo = new PageInfo<>(claimCaseObjectList);
        Long totalHits = searchHits.getTotalHits().value;
        Integer total = totalHits.intValue();
        Integer pages = 0;

        //计算总页数
        if (total == -1) {
            pages = 1;
        }
        if (pp.getPageSize() > 0) {
            pages = (int) (total / pp.getPageSize() + ((total % pp.getPageSize() == 0) ? 0 : 1));
        }
        pageInfo.setPageNum(pp.getPageNum());
        pageInfo.setPageSize(pp.getPageSize());
        pageInfo.setTotal(total);
        pageInfo.setPages(pages);
        pageInfo.setSize(pp.getPageSize());
        pageInfo.setStartRow(pp.getPageNum() > 1 ? (pp.getPageNum() - 1) * pp.getPageSize() + 1 : 1);
        pageInfo.setEndRow(pp.getPageNum() > 1 ? pp.getPageNum() * pp.getPageSize() : pp.getPageSize());
        pageInfo.setList(claimCaseObjectList);
        pageInfo.setNavigatePages(8);

        // 计算导航页
        int[] navigatepageNums = null;
        navigatepageNums = ESTool.calcNavigatepageNums(pageInfo.getPages(), 8, navigatepageNums, pp.getPageNum());
        pageInfo.setNavigatepageNums(navigatepageNums);

        // 第一页
        int firstPage = 0;
        // 前一页
        int prePage = 0;
        // 下一页
        int nextPage = 0;
        // 最后一页
        int lastPage = 0;
        // 计算前后页，第一页，最后一页
        ESTool.calcPage(navigatepageNums, firstPage, lastPage, prePage, nextPage,
                pp.getPageNum(), pages, pageInfo
        );

        // 是否为第一页
        boolean isFirstPage = false;
        // 是否为最后一页
        boolean isLastPage = false;
        // 是否有前一页
        boolean hasPreviousPage = false;
        // 是否有下一页
        boolean hasNextPage = false;
        // 判断页面边界
        ESTool.judgePageBoudary(isFirstPage, isLastPage, hasPreviousPage, hasNextPage,
                pp.getPageNum(), pages, pageInfo
        );

        return pageInfo;
    }


    @Override
    public PageInfo<ClaimCaseObjectReq> claimCaseSignatureListJD(Map<String, Object> paramMap, PageParam pp) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);
        searchSourceBuilder.from(pp.getPageNum() != 0 ? (pp.getPageNum() - 1) * pp.getPageSize() : 0);
        searchSourceBuilder.size(pp.getPageSize());
        searchSourceBuilder.sort("createTime", SortOrder.DESC);
        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();

        if (paramMap.containsKey("status")) {

            boolBuilder.must(QueryBuilders.termQuery("isCaseClosed", 0));
            Integer status = Integer.parseInt(paramMap.get("status").toString());

            //新增签章状态枚举
            if (status == 1) {
                boolBuilder.must(QueryBuilders.termQuery("checkAuditer.keyword", paramMap.get("userId")));
                boolBuilder.must(QueryBuilders.termsQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX33.getCode(),ClaimCaseObjectStatusEnum.BAX36.getCode(),ClaimCaseObjectStatusEnum.BAX40));
            }
            if (status == 0) {
                boolBuilder.mustNot(QueryBuilders.existsQuery("checkAuditer"));
                boolBuilder.must(QueryBuilders.termsQuery("status.keyword", ClaimCaseObjectStatusEnum.BAX33.getCode(),ClaimCaseObjectStatusEnum.BAX36.getCode(),ClaimCaseObjectStatusEnum.BAX40));
            }
        }

        // 案件号
        if (paramMap.containsKey("claimCaseNo")) {
            boolBuilder.must(QueryBuilders.wildcardQuery("claimCaseNo.keyword", "*" + paramMap.get("claimCaseNo") + "*"));
        }

        // 被保人姓名和证件号
        if (paramMap.containsKey("treatName") || paramMap.containsKey("treatIdNum")) {
            Map<String, Object> treatParam = new HashMap<>();
            if (paramMap.containsKey("treatName")) {
                treatParam.put("treatName", paramMap.get("treatName"));
            }
            if (paramMap.containsKey("treatIdNum")) {
                treatParam.put("treatIdNum", paramMap.get("treatIdNum"));
            }
            List<ClaimCase> byTreatParam = claimCaseService.findByTreatParam(treatParam);
            if (CollectionUtils.isNotEmpty(byTreatParam)) {
                boolBuilder.must(QueryBuilders.termsQuery("claimCaseNo.keyword", byTreatParam.stream().map(ClaimCase::getClaimCaseNo).collect(Collectors.toList())));
            }else {
                boolBuilder.must(QueryBuilders.termsQuery("claimCaseNo.keyword", ""));
            }
        }

        // 类型
        if (paramMap.containsKey("type")) {
            boolBuilder.must(QueryBuilders.termQuery("type", paramMap.get("type")));
        }

        // 类型
        if (paramMap.containsKey("category")) {
            boolBuilder.must(QueryBuilders.termQuery("category", paramMap.get("category")));
        }

        // 保司标识
        if (paramMap.containsKey("insCode")) {
            if (paramMap.get("insCode").toString().equals("DD")) {
                BoolQueryBuilder shouldQueryBuilder = new BoolQueryBuilder();
                shouldQueryBuilder.should(QueryBuilders.termQuery("insCode.keyword", paramMap.get("insCode")));
                shouldQueryBuilder.should(new BoolQueryBuilder().mustNot(QueryBuilders.existsQuery("insCode")));
                boolBuilder.must(shouldQueryBuilder);
            } else {
                boolBuilder.must(QueryBuilders.termQuery("insCode.keyword", paramMap.get("insCode")));
            }
        }

        if (paramMap.containsKey("auditer")) {
            boolBuilder.must(QueryBuilders.termQuery("auditer", paramMap.get("auditer")));
        }


        searchSourceBuilder.query(boolBuilder);
        SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
        SearchHits searchHits = searchResponse.getHits();
        SearchHit[] hits = searchHits.getHits();
        List<ClaimCaseObjectReq> claimCaseObjectList = new ArrayList<>();
        List<String> claimCaseIdList = new ArrayList<>();
        for (SearchHit searchHit : hits) {
            ClaimCaseObject claimCaseObject = JSON.parseObject(searchHit.getSourceAsString(), ClaimCaseObject.class);
            claimCaseIdList.add(claimCaseObject.getClaimCaseId());

            ClaimCaseObjectReq claimCaseObjectReq = new ClaimCaseObjectReq();
            BeanUtils.copyProperties(claimCaseObject, claimCaseObjectReq);
            claimCaseObjectList.add(claimCaseObjectReq);

        }
        BoolQueryBuilder boolBuilderRet = QueryBuilders.boolQuery();
        boolBuilderRet.must(QueryBuilders.termsQuery("id.keyword", claimCaseIdList));
        searchSourceBuilder.clearRescorers();
        searchSourceBuilder.from(0);
        searchSourceBuilder.size(pp.getPageSize());
        searchSourceBuilder.query(boolBuilderRet);
        searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE, searchSourceBuilder));
        SearchHits searchHitsRet = searchResponse.getHits();
        SearchHit[] hitsRet = searchHitsRet.getHits();
        for (SearchHit searchHit : hitsRet) {
            ClaimCase claimCase = JSON.parseObject(searchHit.getSourceAsString(), ClaimCase.class);
            for (ClaimCaseObjectReq claimCaseObjectReq : claimCaseObjectList) {
                if (claimCaseObjectReq.getClaimCaseId().equals(claimCase.getId())) {
                    claimCaseObjectReq.setStartDate(claimCase.getStartDate());
                    claimCaseObjectReq.setTreatDate(claimCase.getTreatDate());
                    claimCaseObjectReq.setLabel(claimCase.getLabel());
                    claimCaseObjectReq.setProvince(claimCase.getProvince());
                }
            }
        }
        PageInfo<ClaimCaseObjectReq> pageInfo = new PageInfo<>(claimCaseObjectList);
        Long totalHits = searchHits.getTotalHits().value;
        Integer total = totalHits.intValue();
        Integer pages = 0;

        //计算总页数
        if (total == -1) {
            pages = 1;
        }
        if (pp.getPageSize() > 0) {
            pages = (int) (total / pp.getPageSize() + ((total % pp.getPageSize() == 0) ? 0 : 1));
        }
        pageInfo.setPageNum(pp.getPageNum());
        pageInfo.setPageSize(pp.getPageSize());
        pageInfo.setTotal(total);
        pageInfo.setPages(pages);
        pageInfo.setSize(pp.getPageSize());
        pageInfo.setStartRow(pp.getPageNum() > 1 ? (pp.getPageNum() - 1) * pp.getPageSize() + 1 : 1);
        pageInfo.setEndRow(pp.getPageNum() > 1 ? pp.getPageNum() * pp.getPageSize() : pp.getPageSize());
        pageInfo.setList(claimCaseObjectList);
        pageInfo.setNavigatePages(8);

        // 计算导航页
        int[] navigatepageNums = null;
        navigatepageNums = ESTool.calcNavigatepageNums(pageInfo.getPages(), 8, navigatepageNums, pp.getPageNum());
        pageInfo.setNavigatepageNums(navigatepageNums);

        // 第一页
        int firstPage = 0;
        // 前一页
        int prePage = 0;
        // 下一页
        int nextPage = 0;
        // 最后一页
        int lastPage = 0;
        // 计算前后页，第一页，最后一页
        ESTool.calcPage(navigatepageNums, firstPage, lastPage, prePage, nextPage,
                pp.getPageNum(), pages, pageInfo
        );

        // 是否为第一页
        boolean isFirstPage = false;
        // 是否为最后一页
        boolean isLastPage = false;
        // 是否有前一页
        boolean hasPreviousPage = false;
        // 是否有下一页
        boolean hasNextPage = false;
        // 判断页面边界
        ESTool.judgePageBoudary(isFirstPage, isLastPage, hasPreviousPage, hasNextPage,
                pp.getPageNum(), pages, pageInfo
        );

        return pageInfo;
    }

    @Override
    public int clearInsAudit(ClaimCaseObject claimCaseObject) {
        return claimCaseObjectDao.clearInsAudit(claimCaseObject);
    }

    /**
     * 查询两个时间段内预处理数据
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public List<ClaimCaseObjectV2> findObjectAndCaseLog(String startTime, String endTime) {

        List<ClaimCaseObjectV2> claimCaseObjecVoList = new ArrayList<>();

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);
        searchSourceBuilder.size(0);
        searchSourceBuilder.sort("createTime", SortOrder.DESC);

        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
        List<Integer> typeList = new ArrayList<>();
        typeList.add(ClaimCaseLogTypeEnum.电话生成案件.getCode());
        typeList.add(ClaimCaseLogTypeEnum.报案审核完成.getCode());
        boolBuilder.must(QueryBuilders.termsQuery("type", typeList));
        RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("createTime");
        rangeQueryBuilder.gte(DateUtils.parse(startTime, DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS).getTime());
        rangeQueryBuilder.lte(DateUtils.parse(endTime, DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS).getTime());
        boolBuilder.must(rangeQueryBuilder);

        searchSourceBuilder.query(boolBuilder);
        SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_LOG, searchSourceBuilder));
        SearchHits searchHits = searchResponse.getHits();
        Long totalHits = searchHits.getTotalHits().value;
        if (totalHits == 0l) {
            return new ArrayList<>();
        }
        searchSourceBuilder.size(totalHits.intValue());
        searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_LOG, searchSourceBuilder));
        searchHits = searchResponse.getHits();
        SearchHit[] hits = searchHits.getHits();

        Map<String, ClaimCaseLog> logMap = new HashMap<>();
        for (SearchHit searchHit : hits) {
            ClaimCaseLog claimCaseLog = JSON.parseObject(searchHit.getSourceAsString(), ClaimCaseLog.class);
            logMap.put(claimCaseLog.getClaimCaseId(), claimCaseLog);
        }

        List<ClaimCaseObject> claimCaseObjectList = findListByClaimCaseIdSet(logMap.keySet());
        if (CollectionUtils.isNotEmpty(claimCaseObjectList)) {
            for (ClaimCaseObject claimCaseObject : claimCaseObjectList) {
                ClaimCaseObjectV2 claimCaseObjectV2 = new ClaimCaseObjectV2();
                claimCaseObjectV2.setClaimCaseId(claimCaseObject.getClaimCaseId());
                claimCaseObjectV2.setEnterTime(logMap.get(claimCaseObject.getClaimCaseId()).getCreateTime());       // 进入预处理时间
                claimCaseObjectV2.setEstimatedOverallLoss(claimCaseObject.getEstimatedOverallLoss());
                claimCaseObjectV2.setType(claimCaseObject.getType());
                claimCaseObjectV2.setCategory(claimCaseObject.getCategory());
                claimCaseObjecVoList.add(claimCaseObjectV2);
            }
        }

        searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(logMap.keySet().size());

        BoolQueryBuilder boolBuilderRet = QueryBuilders.boolQuery();
        boolBuilderRet.must(QueryBuilders.termsQuery("id.keyword", logMap.keySet()));

        searchSourceBuilder.query(boolBuilderRet);
        searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE, searchSourceBuilder));
        SearchHits searchHitsRet = searchResponse.getHits();
        SearchHit[] hitsRet = searchHitsRet.getHits();
        for (SearchHit searchHit : hitsRet) {
            ClaimCase claimCase = JSON.parseObject(searchHit.getSourceAsString(), ClaimCase.class);
            claimCaseObjecVoList.stream().forEach(claimCaseObjectVo -> {
                if (claimCaseObjectVo.getClaimCaseId().equals(claimCase.getId())) {
                    if (StringUtils.isNotBlank(claimCase.getLabel())) {
                        String[] split = claimCase.getLabel().split(",");
                        List<String> labelList = new ArrayList<>();
                        for (String s : split) {
                            labelList.add(ClaimCaseLabelEnum.codeToMsg(s));
                        }
                        claimCaseObjectVo.setLabel(StringUtils.join(labelList, ","));
                    }
                    claimCaseObjectVo.setClaimCaseNo(claimCase.getClaimCaseNo());
                    claimCaseObjectVo.setCreateTime(claimCase.getCreateTime());
                    claimCaseObjectVo.setAuditer(claimCase.getPretreatmentAuditer());
                }
            });
        }

        List<String> managerIdList = claimCaseObjecVoList.stream().map(x -> x.getAuditer()).filter(Objects :: nonNull).collect(Collectors.toList());
        List<Manager> managerList = managerService.findByManagerIdList(managerIdList);
        if (CollectionUtils.isNotEmpty(managerList)) {
            for (Manager manager : managerList) {
                claimCaseObjecVoList.stream().forEach(claimCaseObjectVo -> {
                    if (manager.getId().equals(claimCaseObjectVo.getAuditer())) {
                        claimCaseObjectVo.setRealName(manager.getRealName());
                    }
                });
            }
        }

        return claimCaseObjecVoList;
    }

    /**
     * 当前预处理数据
     * @return
     */
    @Override
    public List<ClaimCaseObjectV2>  findObjectAndCaseLogNow() {

        List<ClaimCaseObjectV2> claimCaseObjecVoList = new ArrayList<>();

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);
        searchSourceBuilder.size(0);
        searchSourceBuilder.sort("createTime", SortOrder.DESC);
        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
        List<String> claimCaseStatusList = new ArrayList<>();
        claimCaseStatusList.add(ClaimCaseStatusEum.AAX30.getCode());
        claimCaseStatusList.add(ClaimCaseStatusEum.AAX31.getCode());
        boolBuilder.must(QueryBuilders.termsQuery("status.keyword",claimCaseStatusList));
        searchSourceBuilder.query(boolBuilder);
        SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE, searchSourceBuilder));
        SearchHits searchHits = searchResponse.getHits();
        Long totalHits = searchHits.getTotalHits().value;
        if (totalHits == 0l) {
            return new ArrayList<>();
        }
        searchSourceBuilder.size(totalHits.intValue());
        searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE, searchSourceBuilder));
        searchHits = searchResponse.getHits();
        SearchHit[] hits = searchHits.getHits();

        Map<String, ClaimCase> logMap = new HashMap<>();
        for (SearchHit searchHit : hits) {
            ClaimCase claimcase = JSON.parseObject(searchHit.getSourceAsString(), ClaimCase.class);
            logMap.put(claimcase.getId(), claimcase);
        }

        List<ClaimCaseObject> claimCaseObjectList = findListByClaimCaseIdSet(logMap.keySet());
        if (CollectionUtils.isNotEmpty(claimCaseObjectList)) {
            for (ClaimCaseObject claimCaseObject : claimCaseObjectList) {
                ClaimCaseObjectV2 claimCaseObjectV2 = new ClaimCaseObjectV2();
                claimCaseObjectV2.setClaimCaseId(claimCaseObject.getClaimCaseId());
                claimCaseObjectV2.setEnterTime(logMap.get(claimCaseObject.getClaimCaseId()).getCreateTime());   // 案件创建时间
                claimCaseObjectV2.setEstimatedOverallLoss(claimCaseObject.getEstimatedOverallLoss());
                claimCaseObjectV2.setType(claimCaseObject.getType());
                claimCaseObjectV2.setCategory(claimCaseObject.getCategory());
                claimCaseObjecVoList.add(claimCaseObjectV2);
            }
        }

        searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(logMap.keySet().size());

        BoolQueryBuilder boolBuilderRet = QueryBuilders.boolQuery();
        boolBuilderRet.must(QueryBuilders.termsQuery("id.keyword", logMap.keySet()));

        searchSourceBuilder.query(boolBuilderRet);
        searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE, searchSourceBuilder));
        SearchHits searchHitsRet = searchResponse.getHits();
        SearchHit[] hitsRet = searchHitsRet.getHits();
        for (SearchHit searchHit : hitsRet) {
            ClaimCase claimCase = JSON.parseObject(searchHit.getSourceAsString(), ClaimCase.class);
            claimCaseObjecVoList.stream().forEach(claimCaseObjectVo -> {
                if (claimCaseObjectVo.getClaimCaseId().equals(claimCase.getId())) {
                    if (StringUtils.isNotBlank(claimCase.getLabel())) {
                        String[] split = claimCase.getLabel().split(",");
                        List<String> labelList = new ArrayList<>();
                        for (String s : split) {
                            labelList.add(ClaimCaseLabelEnum.codeToMsg(s));
                        }
                        claimCaseObjectVo.setLabel(StringUtils.join(labelList, ","));
                    }
                    claimCaseObjectVo.setClaimCaseNo(claimCase.getClaimCaseNo());
                    claimCaseObjectVo.setCreateTime(claimCase.getCreateTime());
                    claimCaseObjectVo.setClaimCaseStatus(claimCase.getStatus());
                    claimCaseObjectVo.setAuditer(claimCase.getPretreatmentAuditer());
                }
            });
        }

        List<String> managerIdList = claimCaseObjecVoList.stream().map(x -> x.getAuditer()).filter(Objects :: nonNull).collect(Collectors.toList());
        List<Manager> managerList = managerService.findByManagerIdList(managerIdList);
        if (CollectionUtils.isNotEmpty(managerList)) {
            for (Manager manager : managerList) {
                claimCaseObjecVoList.stream().forEach(claimCaseObjectVo -> {
                    if (manager.getId().equals(claimCaseObjectVo.getAuditer())) {
                        claimCaseObjectVo.setRealName(manager.getRealName());
                    }
                });
            }
        }

        return claimCaseObjecVoList;
    }

    @Override
    public ClaimCaseObjectScrollVo findPretreatmentClaimCaseObjectListByScroll(String applyType, Set<String> totalClaimCaseIdSet, int size) {
        ApplyTypeNewEnum appyTypeNewByCode = ApplyTypeNewEnum.getAppyTypeNewByCode(applyType);
        if (appyTypeNewByCode != null) {
            SearchSourceBuilder objectSearchSourceBuilder = new SearchSourceBuilder();
            objectSearchSourceBuilder.trackTotalHits(true);
            objectSearchSourceBuilder.size(size);
            BoolQueryBuilder objectBoolBuilder = new BoolQueryBuilder();
            objectBoolBuilder.must(QueryBuilders.termQuery("type", appyTypeNewByCode.getType()));
            objectBoolBuilder.must(QueryBuilders.termQuery("category", appyTypeNewByCode.getCategory()));
            objectBoolBuilder.must(QueryBuilders.termsQuery("claimCaseId.keyword", totalClaimCaseIdSet));

            objectSearchSourceBuilder.query(objectBoolBuilder);

            SearchResponse objectSearchResponse = ESHighLevelRESTClientTool.scrollSearchInit(ElasticIndexName.CLAIM_CASE_OBJECT, objectSearchSourceBuilder);;
            SearchHits objectSearchHits = objectSearchResponse.getHits();

            List<ClaimCaseObject> claimCaseObjectList = new ArrayList<ClaimCaseObject>();
            String object_scroll_id = objectSearchResponse.getScrollId();

            if (objectSearchHits.getTotalHits().value != 0l) {
                SearchHit[] hits = objectSearchHits.getHits();

                for (SearchHit hit : hits) {
                    ClaimCaseObject claimCaseObject = JSON.parseObject(hit.getSourceAsString(), ClaimCaseObject.class);
                    claimCaseObjectList.add(claimCaseObject);
                }
            }

            ClaimCaseObjectScrollVo objectScrollDataVo = new ClaimCaseObjectScrollVo();
            objectScrollDataVo.setScrollId(object_scroll_id);
            objectScrollDataVo.setClaimCaseObjectList(claimCaseObjectList);
            return objectScrollDataVo;

        } else {
            return null;
        }

    }
}
