package kd.beijingRoastDuck.service.impl;

import com.alibaba.fastjson.JSON;
import kd.beijingRoastDuck.dao.SignTemplatePushLogDao;
import kd.beijingRoastDuck.service.SignTemplatePushLogService;
import kd.beijingRoastDuck.service.SignTemplateService;
import kd.beijingRoastDuck.vo.SignTemplatePushVo;
import kd.common.tool.ESHighLevelRESTClientTool;
import kd.entity.SignTemplate;
import kd.entity.SignTemplatePushLog;
import kd.main.common.ElasticIndexName;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: hoomik
 * @description: 签字推送日志
 * @date: 2024/11/07 14:32
 * @param
 * @return
 */
@Service
@Transactional
public class SignTemplatePushLogServiceImpl implements SignTemplatePushLogService {


    @Autowired
    private SignTemplatePushLogDao signTemplatePushLogDao;

    @Autowired
    private SignTemplateService signTemplateService;

    /**
     * @author: hoomik
     * @description: 添加签字推送日志
     * @date: 2024/11/07 14:32
     * @param
     * @return
     */
    @Override
    public int insertSelective(SignTemplatePushLog signTemplatePushLog) {
        return signTemplatePushLogDao.insertSelective(signTemplatePushLog);
    }

    /**
     * @author: hoomik
     * @description: 查询签字推送日志
     * @date: 2024/11/07 14:32
     * @param
     * @return
     */
    @Override
    public List<SignTemplatePushVo> findByParam(Map<String, Object> paramMap) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);
        searchSourceBuilder.sort("createTime", SortOrder.DESC);
        BoolQueryBuilder boolBuilder = new BoolQueryBuilder();

        if (paramMap.containsKey("claimCaseObjectId")) {
            boolBuilder.must(QueryBuilders.termQuery("claimCaseObjectId.keyword", paramMap.get("claimCaseObjectId")));
        }

        if (paramMap.containsKey("status")) {
            boolBuilder.must(QueryBuilders.termQuery("status", paramMap.get("status")));
        }

        if (paramMap.containsKey("claimCaseNo")) {
            boolBuilder.must(QueryBuilders.termQuery("claimCaseNo.keyword", paramMap.get("claimCaseNo")));
        }
        searchSourceBuilder.query(boolBuilder);
        SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.SIGN_TEMPLATE_PUSH_LOG, searchSourceBuilder));
        SearchHits searchHits = searchResponse.getHits();
        Long searchHitsValue = searchHits.getTotalHits().value;
        int total = searchHitsValue.intValue();
        if (total == 0) {
            return null;
        }

        searchSourceBuilder.size(total);

        searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.SIGN_TEMPLATE_PUSH_LOG, searchSourceBuilder));
        searchHits = searchResponse.getHits();
        SearchHit[] hits = searchHits.getHits();
        List<SignTemplatePushVo> signTemplatePushLogList = new ArrayList<>();

        Map<String, SignTemplate> signTemplateMap = new HashMap<>();
        for (SearchHit searchHit : hits) {
            SignTemplatePushVo signTemplatePushLog = JSON.parseObject(searchHit.getSourceAsString(), SignTemplatePushVo.class);
            String signTemplateId = signTemplatePushLog.getSignTemplateId();
            if (StringUtils.isNotBlank(signTemplateId)) {
                if (!signTemplateMap.containsKey(signTemplateId)) {
                    SignTemplate signTemplate = signTemplateService.selectByPrimaryKey(signTemplateId);
                    signTemplateMap.put(signTemplateId, signTemplate);
                }
                signTemplatePushLog.setSignTemplateName(signTemplateMap.get(signTemplateId).getTemplateName());
            }

            signTemplatePushLogList.add(signTemplatePushLog);
        }
        return signTemplatePushLogList;
    }

    /**
     * @author: hoomik
     * @description: 查询单个签字推送日志
     * @date: 2024/11/07 14:32
     * @param
     * @return
     */
    @Override
    public SignTemplatePushLog selectByPrimaryKey(String id) {
        GetResponse getResponse = ESHighLevelRESTClientTool.selectById(ElasticIndexName.SIGN_TEMPLATE_PUSH_LOG, id);
        if (getResponse.isSourceEmpty()) {
            return null;
        } else {
            SignTemplatePushLog signTemplatePushLog = JSON.parseObject(getResponse.getSourceAsString(), SignTemplatePushLog.class);
            return signTemplatePushLog;
        }
    }

    /**
     * @author: hoomik
     * @description: 修改签字推送日志
     * @date: 2024/11/07 14:32
     * @param
     * @return
     */
    @Override
    public int updateByPrimaryKeySelective(SignTemplatePushLog signTemplatePushLog) {
        return signTemplatePushLogDao.updateByPrimaryKeySelective(signTemplatePushLog);
    }


}
