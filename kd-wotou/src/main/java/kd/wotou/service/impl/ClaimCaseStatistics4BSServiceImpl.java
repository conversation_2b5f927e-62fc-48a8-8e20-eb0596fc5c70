package kd.wotou.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import kd.main.common.ElasticStatisticsIndexName;
import kd.main.util.DateUtils;
import kd.wotou.data.ClaimCaseStatistics4BS;
import kd.wotou.service.ClaimCaseStatistics4BSService;
import kd.wotou.util.ESHighLevelRESTClientTool_V2;
import kd.wotou.vo.data.ClaimCaseStatistics4BSDataVo;
import kd.wotou.vo.data.ClaimCaseStatistics4BSVo;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;


@Service
public class ClaimCaseStatistics4BSServiceImpl implements ClaimCaseStatistics4BSService {

    @Override
    public ClaimCaseStatistics4BS selectByPrimaryKey(String id) {
        GetResponse getResponse = ESHighLevelRESTClientTool_V2.selectById(ElasticStatisticsIndexName.CLAIM_CASE_STATISTICS_4BS, id);
        if (getResponse.isSourceEmpty()) {
            return null;
        } else {
            ClaimCaseStatistics4BS claimCaseStatistics = JSON.parseObject(getResponse.getSourceAsString(), ClaimCaseStatistics4BS.class);
            return claimCaseStatistics;
        }
    }

    @Override
    public void insertOrUpdateData(ClaimCaseStatistics4BS claimCaseStatistics, boolean isUpdate) {
        if (isUpdate) {
            ESHighLevelRESTClientTool_V2.updateAsyn(ESHighLevelRESTClientTool_V2.genUpdateRequest(
                    ElasticStatisticsIndexName.CLAIM_CASE_STATISTICS_4BS,
                    claimCaseStatistics.getId(),
                    JSON.toJSONString(claimCaseStatistics, SerializerFeature.WriteMapNullValue)
                                                                                                 ));
        } else {
            ESHighLevelRESTClientTool_V2.insertAsyn(ESHighLevelRESTClientTool_V2.genInsertRequest(
                    ElasticStatisticsIndexName.CLAIM_CASE_STATISTICS_4BS,
                    claimCaseStatistics.getId(),
                    JSON.toJSONString(claimCaseStatistics, SerializerFeature.WriteMapNullValue)
                                                                                                 ));
        }
        return;
    }

    @Override
    public List<ClaimCaseStatistics4BS> selectAllByInsCode(Map<String, Object> prarMap) {
        int size = 1000;

        ClaimCaseStatistics4BSVo claimCaseStatisticsVo = findAllCaseIdByScroll4DataByInsCode(size, prarMap);

        List<ClaimCaseStatistics4BS> claimCaseStatistics = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(claimCaseStatisticsVo.getClaimCaseStatistics())) {
            while (CollectionUtils.isNotEmpty(claimCaseStatisticsVo.getClaimCaseStatistics())) {

                claimCaseStatistics.addAll(claimCaseStatisticsVo.getClaimCaseStatistics());
                // 插入完成之后再行插入之后的数据
                claimCaseStatisticsVo = findAllCaseIdByScroll4Data(claimCaseStatisticsVo.getScrollId());

            }
        }

        return claimCaseStatistics;
    }

    /**
     * 根据paramMap滚动查询案件统计表
     * @param size
     * @param prarMap
     * @return
     */
    private ClaimCaseStatistics4BSVo findAllCaseIdByScroll4DataByInsCode(int size, Map<String, Object> prarMap) {
        //使用滚动查询 遍历case表进行同步
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);
        searchSourceBuilder.size(size);
        searchSourceBuilder.sort("claimCaseNo.keyword", SortOrder.ASC);
        BoolQueryBuilder boolBuilder = new BoolQueryBuilder();

        if (prarMap.containsKey("insCode")) {
            String[] insCodes = (String[]) prarMap.get("insCode");
            if (!(insCodes.length == 1 && "-1".equals(insCodes[0]))) {
                boolBuilder.must(QueryBuilders.termsQuery("insCode.keyword", insCodes));
            }
        }

        if (prarMap.containsKey("startDateStr") && prarMap.containsKey("endDateStr")) {
            boolBuilder.must(QueryBuilders.rangeQuery("treatDate")
                    .gte(DateUtils.parse(prarMap.get("startDateStr") + " 00:00:00", "yyyy-MM-dd HH:mm:ss").getTime())
                    .lte(DateUtils.parse(prarMap.get("endDateStr") + " 23:59:59", "yyyy-MM-dd HH:mm:ss").getTime()));
        }

        searchSourceBuilder.query(boolBuilder);
        SearchResponse searchResponse = ESHighLevelRESTClientTool_V2.scrollSearchInit(ElasticStatisticsIndexName.CLAIM_CASE_STATISTICS_4BS, searchSourceBuilder);
        SearchHits searchHits = searchResponse.getHits();
        List<ClaimCaseStatistics4BS> claimCaseStatistics4BSList = new ArrayList<ClaimCaseStatistics4BS>();
        String _scroll_id = "";
        if (searchHits.getTotalHits().value == 0l) {

        } else {
            _scroll_id = searchResponse.getScrollId();
            SearchHit[] hitArray = searchHits.getHits();
            for (SearchHit searchHit : hitArray) {
                ClaimCaseStatistics4BS claimCaseStatistics = JSON.parseObject(searchHit.getSourceAsString(), ClaimCaseStatistics4BS.class);
                claimCaseStatistics4BSList.add(claimCaseStatistics);
            }
        }
        ClaimCaseStatistics4BSVo claimCaseStatisticsVo = new ClaimCaseStatistics4BSVo();
        claimCaseStatisticsVo.setScrollId(_scroll_id);
        claimCaseStatisticsVo.setClaimCaseStatistics(claimCaseStatistics4BSList);
        return claimCaseStatisticsVo;

    }


    /**
     * 根据滚动ID查询案件统计
     * @param scrollId
     * @return
     */
    private ClaimCaseStatistics4BSVo findAllCaseIdByScroll4Data(String scrollId) {

        SearchResponse searchResponse = ESHighLevelRESTClientTool_V2.scrollSearchSyn(scrollId);
        SearchHits searchHits = searchResponse.getHits();
        List<ClaimCaseStatistics4BS> caseStatisticsList = new ArrayList<ClaimCaseStatistics4BS>();
        String _scroll_id = "";
        if (searchHits.getTotalHits().value == 0l) {

        } else {
            _scroll_id = searchResponse.getScrollId();
            SearchHit[] hitArray = searchHits.getHits();
            for (SearchHit searchHit : hitArray) {
                ClaimCaseStatistics4BS claimCaseStatistics = JSON.parseObject(searchHit.getSourceAsString(), ClaimCaseStatistics4BS.class);
                caseStatisticsList.add(claimCaseStatistics);
            }
            if (caseStatisticsList.size() == 0) {
                ESHighLevelRESTClientTool_V2.clearScollSearchSyn(ESHighLevelRESTClientTool_V2.genClearScollSearchReq(new ArrayList<String>() {{
                    this.add(scrollId);
                }}));
            }
        }
        ClaimCaseStatistics4BSVo claimCaseStatisticsVo = new ClaimCaseStatistics4BSVo();
        claimCaseStatisticsVo.setScrollId(_scroll_id);
        claimCaseStatisticsVo.setClaimCaseStatistics(caseStatisticsList);
        return claimCaseStatisticsVo;

    }


    @Override
    public ClaimCaseStatistics4BSDataVo findAllListByScroll4Statistics(int size, String[] insCodes) {
        //使用滚动查询 遍历case表进行同步
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);
        searchSourceBuilder.size(size);
        searchSourceBuilder.sort("claimCaseNo.keyword", SortOrder.ASC);

        BoolQueryBuilder boolBuilder = new BoolQueryBuilder();
        if (!(insCodes.length == 1 && "-1".equals(insCodes[0]))) {
            boolBuilder.must(QueryBuilders.termsQuery("insCode.keyword", insCodes));
        }
        searchSourceBuilder.query(boolBuilder);
        SearchResponse searchResponse = ESHighLevelRESTClientTool_V2.scrollSearchInit(ElasticStatisticsIndexName.CLAIM_CASE_STATISTICS_4BS, searchSourceBuilder);
        SearchHits searchHits = searchResponse.getHits();
        List<ClaimCaseStatistics4BS> claimCaseStatistics4BSList = new ArrayList<ClaimCaseStatistics4BS>();
        String _scroll_id = "";
        if (searchHits.getTotalHits().value == 0l) {

        } else {
            _scroll_id = searchResponse.getScrollId();
            SearchHit[] hitArray = searchHits.getHits();
            for (SearchHit searchHit : hitArray) {
                claimCaseStatistics4BSList.add(JSON.parseObject(searchHit.getSourceAsString(), ClaimCaseStatistics4BS.class));
            }
        }
        ClaimCaseStatistics4BSDataVo claimCaseStatisticsDataVo = new ClaimCaseStatistics4BSDataVo();
        claimCaseStatisticsDataVo.setScrollId(_scroll_id);
        claimCaseStatisticsDataVo.setClaimCaseStatistics4BSList(claimCaseStatistics4BSList);
        return claimCaseStatisticsDataVo;
    }

    @Override
    public ClaimCaseStatistics4BSDataVo findAllListByScroll4Statistics(int size, String[] insCodes, Date startDate, Date endDate) {
        //使用滚动查询 遍历case表进行同步
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);
        searchSourceBuilder.size(size);
        searchSourceBuilder.sort("claimCaseNo.keyword", SortOrder.ASC);

        BoolQueryBuilder boolBuilder = new BoolQueryBuilder();
        boolBuilder.must(QueryBuilders.termsQuery("insCode.keyword", insCodes));
        boolBuilder.must(QueryBuilders.rangeQuery("treatDate").gte(startDate.getTime()).lte(endDate.getTime()));

        searchSourceBuilder.query(boolBuilder);
        SearchResponse searchResponse = ESHighLevelRESTClientTool_V2.scrollSearchInit(ElasticStatisticsIndexName.CLAIM_CASE_STATISTICS_4BS, searchSourceBuilder);
        SearchHits searchHits = searchResponse.getHits();
        List<ClaimCaseStatistics4BS> claimCaseStatistics4BSList = new ArrayList<ClaimCaseStatistics4BS>();
        String _scroll_id = "";
        if (searchHits.getTotalHits().value == 0l) {

        } else {
            _scroll_id = searchResponse.getScrollId();
            SearchHit[] hitArray = searchHits.getHits();
            for (SearchHit searchHit : hitArray) {
                claimCaseStatistics4BSList.add(JSON.parseObject(searchHit.getSourceAsString(), ClaimCaseStatistics4BS.class));
            }
        }
        ClaimCaseStatistics4BSDataVo claimCaseStatisticsDataVo = new ClaimCaseStatistics4BSDataVo();
        claimCaseStatisticsDataVo.setScrollId(_scroll_id);
        claimCaseStatisticsDataVo.setClaimCaseStatistics4BSList(claimCaseStatistics4BSList);
        return claimCaseStatisticsDataVo;
    }


    @Override
    public ClaimCaseStatistics4BSDataVo findAllListByScrollId4Statistics(String scrollId) {
        SearchResponse searchResponse = ESHighLevelRESTClientTool_V2.scrollSearchSyn(scrollId);
        SearchHits searchHits = searchResponse.getHits();
        List<ClaimCaseStatistics4BS> claimCaseStatistics4BSList = new ArrayList<ClaimCaseStatistics4BS>();
        String _scroll_id = "";
        if (searchHits.getTotalHits().value == 0l) {
        } else {
            _scroll_id = searchResponse.getScrollId();
            SearchHit[] hitArray = searchHits.getHits();
            for (SearchHit searchHit : hitArray) {
                claimCaseStatistics4BSList.add(JSON.parseObject(searchHit.getSourceAsString(), ClaimCaseStatistics4BS.class));
            }
            if (claimCaseStatistics4BSList.size() == 0) {
                ESHighLevelRESTClientTool_V2.clearScollSearchSyn(ESHighLevelRESTClientTool_V2.genClearScollSearchReq(new ArrayList<String>() {{
                    this.add(scrollId);
                }}));
            }

        }
        ClaimCaseStatistics4BSDataVo claimCaseStatisticsDataVo = new ClaimCaseStatistics4BSDataVo();
        claimCaseStatisticsDataVo.setScrollId(_scroll_id);
        claimCaseStatisticsDataVo.setClaimCaseStatistics4BSList(claimCaseStatistics4BSList);
        return claimCaseStatisticsDataVo;
    }


    @Override
    public ClaimCaseStatistics4BSDataVo findAllNotFinishCaseInfo(int size, Date before) {
//使用滚动查询 遍历case表进行同步
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);
        searchSourceBuilder.size(size);
        searchSourceBuilder.sort("claimCaseNo.keyword", SortOrder.ASC);

        BoolQueryBuilder boolBuilder = new BoolQueryBuilder();
        boolBuilder.must(QueryBuilders.rangeQuery("modifyTime").lte(before.getTime()));
        boolBuilder.must(QueryBuilders.termQuery("status.keyword", "未决"));
        searchSourceBuilder.query(boolBuilder);
        SearchResponse searchResponse = ESHighLevelRESTClientTool_V2.scrollSearchInit(ElasticStatisticsIndexName.CLAIM_CASE_STATISTICS_4BS, searchSourceBuilder);
        SearchHits searchHits = searchResponse.getHits();
        List<ClaimCaseStatistics4BS> claimCaseStatistics4BSList = new ArrayList<ClaimCaseStatistics4BS>();
        String _scroll_id = "";
        if (searchHits.getTotalHits().value == 0l) {

        } else {
            _scroll_id = searchResponse.getScrollId();
            SearchHit[] hitArray = searchHits.getHits();
            for (SearchHit searchHit : hitArray) {
                claimCaseStatistics4BSList.add(JSON.parseObject(searchHit.getSourceAsString(), ClaimCaseStatistics4BS.class));
            }
        }
        ClaimCaseStatistics4BSDataVo claimCaseStatisticsDataVo = new ClaimCaseStatistics4BSDataVo();
        claimCaseStatisticsDataVo.setScrollId(_scroll_id);
        claimCaseStatisticsDataVo.setClaimCaseStatistics4BSList(claimCaseStatistics4BSList);
        return claimCaseStatisticsDataVo;

    }

    @Override
    public List<ClaimCaseStatistics4BS> findAllByIdList(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(idList.size());
        BoolQueryBuilder boolBuilder = new BoolQueryBuilder();
        boolBuilder.must(QueryBuilders.termsQuery("id", idList));
        searchSourceBuilder.query(boolBuilder);
        SearchResponse searchResponse = ESHighLevelRESTClientTool_V2.searchSyn(ESHighLevelRESTClientTool_V2.gentSearchRequest(ElasticStatisticsIndexName.CLAIM_CASE_STATISTICS_4BS, searchSourceBuilder));
        SearchHits searchHits = searchResponse.getHits();
        List<ClaimCaseStatistics4BS> claimCaseStatistics4BSList = new ArrayList<>();
        if (searchHits.getTotalHits().value == 0l) {

        } else {
            SearchHit[] hitArray = searchHits.getHits();
            for (SearchHit searchHit : hitArray) {
                claimCaseStatistics4BSList.add(JSON.parseObject(searchHit.getSourceAsString(), ClaimCaseStatistics4BS.class));
            }
        }
        return claimCaseStatistics4BSList;

    }
}
