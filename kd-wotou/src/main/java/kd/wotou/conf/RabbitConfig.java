package kd.wotou.conf;

import kd.main.common.QueueName;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.amqp.SimpleRabbitListenerContainerFactoryConfigurer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * @Description: rabbitmq配置文件
 */
@Configuration
public class RabbitConfig {

    //并发数量
    public static final int DEFAULT_CONCURRENT = 10;

    @Bean("customContainerFactory")
    public SimpleRabbitListenerContainerFactory containerFactory(SimpleRabbitListenerContainerFactoryConfigurer configurer,
                                                                 ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConcurrentConsumers(DEFAULT_CONCURRENT);
        factory.setMaxConcurrentConsumers(DEFAULT_CONCURRENT);
        configurer.configure(factory, connectionFactory);
        return factory;
    }

    @Bean
    public Queue KD_SYNC_POLICY_PERSON_JOB() {
        return new Queue(QueueName.KD_SYNC_POLICY_PERSON_JOB, true);
    }

    @Bean
    public Queue KD_SYNC_AGENT_INFO_JOB() {
        return new Queue(QueueName.KD_SYNC_AGENT_INFO_JOB, true);
    }

    @Bean
    public Queue KD_SYNC_AREA_JOB() {
        return new Queue(QueueName.KD_SYNC_AREA_JOB, true);
    }

    @Bean
    public Queue KD_SYNC_FLOAT_PRODUCT_JOB() {
        return new Queue(QueueName.KD_SYNC_FLOAT_PRODUCT_JOB, true);
    }

    @Bean
    public Queue KD_SYNC_POLICY_PERSON() {
        return new Queue(QueueName.KD_SYNC_POLICY_PERSON, true);
    }

    @Bean
    public Queue KD_SYNC_CLAIM_JOB() {
        return new Queue(QueueName.KD_SYNC_CLAIM_JOB, true);
    }

    @Bean
    public Queue KD_SYNC_CLAIM_4BS_JOB() {
        return new Queue(QueueName.KD_SYNC_CLAIM_4BS_JOB, true);
    }

    @Bean
    public Queue KD_GEN_EXPORT_TASK() {
        return new Queue(QueueName.KD_GEN_EXPORT_TASK, true);
    }

    @Bean
    public Queue KD_SYNC_CLAIM_CASE() {
        return new Queue(QueueName.KD_SYNC_CLAIM_CASE, true);
    }

    @Bean
    public Queue KD_EXPORT_POLICY_LN_JOB() {
        return new Queue(QueueName.KD_EXPORT_POLICY_LN_JOB, true);
    }

    @Bean
    public Queue KD_EXPORT_PICC_POLICY_JOB() {
        return new Queue(QueueName.KD_EXPORT_PICC_POLICY_JOB, true);
    }

    @Bean
    public Queue KD_EXPORT_HX_POLICY_JOB() {
        return new Queue(QueueName.KD_EXPORT_HX_POLICY_JOB, true);
    }

    @Bean
    public Queue KD_EXPORT_CASE_DD_JOB() {
        return new Queue(QueueName.KD_EXPORT_CASE_DD_JOB, true);
    }

    @Bean
    public Queue KD_GEN_CLAIM_CASE_MIRROR() {
        return new Queue(QueueName.KD_GEN_CLAIM_CASE_MIRROR, true);
    }

    @Bean
    public Queue KD_SYNC_INSURANCE_COMPANY_CASE_PUSH_LOG_JOB() {
        return new Queue(QueueName.KD_SYNC_INSURANCE_COMPANY_CASE_PUSH_LOG_JOB, true);
    }

    @Bean
    public Queue KD_SYNC_INSURANCE_COMPANY_CASE_PUSH_LOG() {
        return new Queue(QueueName.KD_SYNC_INSURANCE_COMPANY_CASE_PUSH_LOG, true);
    }

    /**
     * 普通消息交换机配置
     *
     * @return
     */
    @Bean
    DirectExchange messageInsuranceDirect() {
        return (DirectExchange) ExchangeBuilder
                .directExchange(QueueName.KD_GEN_INSURANCE_STATISTICS_EXCHANGE)
                .durable(true)
                .build();
    }

    /**
     * 延时消息交换机配置
     *
     * @return
     */
    @Bean
    DirectExchange messageInsuranceTtlDirect() {
        return (DirectExchange) ExchangeBuilder
                .directExchange(QueueName.KD_GEN_INSURANCE_STATISTICS_TTL_EXCHANGE)
                .durable(true)
                .build();
    }


    @Bean
    public Queue messageInsuranceQueue() {
        return new Queue(QueueName.KD_GEN_INSURANCE_STATISTICS);
    }


    /**
     * TTL消息队列配置
     *
     * @return
     */
    @Bean
    Queue messageInsuranceTtlQueue() {
        return QueueBuilder
                .durable(QueueName.KD_GEN_INSURANCE_STATISTICS_TTL)
                // 配置到期后转发的交换
                .withArgument("x-dead-letter-exchange", QueueName.KD_GEN_INSURANCE_STATISTICS_EXCHANGE)
                // 配置到期后转发的路由键
                .withArgument("x-dead-letter-routing-key", QueueName.KD_GEN_INSURANCE_STATISTICS)
                .build();
    }


    /**
     * 普通队列和普通交换机的绑定-routekey
     * @param messageInsuranceDirect 消息中心交换配置
     * @param messageInsuranceQueue  消息中心队列
     * @return
     */
    @Bean
    Binding messageInsuranceBinding(DirectExchange messageInsuranceDirect, Queue messageInsuranceQueue) {
        return BindingBuilder
                .bind(messageInsuranceQueue)
                .to(messageInsuranceDirect)
                .with(QueueName.KD_GEN_INSURANCE_STATISTICS);
    }

    /**
     * ttl队列和ttl交换机的绑定-routekey
     *
     * @param messageInsuranceTtlQueue
     * @param messageInsuranceTtlDirect
     * @return
     */
    @Bean
    public Binding messageInsuranceTtlBinding(Queue messageInsuranceTtlQueue, DirectExchange messageInsuranceTtlDirect) {
        return BindingBuilder
                .bind(messageInsuranceTtlQueue)
                .to(messageInsuranceTtlDirect)
                .with(QueueName.KD_GEN_INSURANCE_STATISTICS_TTL);
    }


    @Bean("KD_GEN_CLAIM_CASE_MIRROR_STATICS")
    public Queue KD_GEN_CLAIM_CASE_MIRROR_STATICS() {
        return new Queue(QueueName.KD_GEN_CLAIM_CASE_MIRROR_STATICS, true);
    }


    /**
     * 普通消息交换机配置
     *
     * @return
     */
    @Bean
    DirectExchange messageDirect() {
        return (DirectExchange) ExchangeBuilder
                .directExchange(QueueName.KD_GEN_POLICY_STATISTICS_EXCHANGE)
                .durable(true)
                .build();
    }

    /**
     * 延时消息交换机配置
     *
     * @return
     */
    @Bean
    DirectExchange messageTtlDirect() {
        return (DirectExchange) ExchangeBuilder
                .directExchange(QueueName.KD_GEN_POLICY_STATISTICS_TTL_EXCHANGE)
                .durable(true)
                .build();
    }


    @Bean
    public Queue messageQueue() {
        return new Queue(QueueName.KD_GEN_POLICY_STATISTICS);
    }


    /**
     * TTL消息队列配置
     *
     * @return
     */
    @Bean
    Queue messageTtlQueue() {
        return QueueBuilder
                .durable(QueueName.KD_GEN_POLICY_STATISTICS_TTL)
                // 配置到期后转发的交换
                .withArgument("x-dead-letter-exchange", QueueName.KD_GEN_POLICY_STATISTICS_EXCHANGE)
                // 配置到期后转发的路由键
                .withArgument("x-dead-letter-routing-key", QueueName.KD_GEN_POLICY_STATISTICS)
                .build();
    }


    //
//    /**
//     * 普通队列和普通交换机的绑定-routekey
//     *
//     * @param messageDirect 消息中心交换配置
//     * @param messageQueue  消息中心队列
//     * @return
//     */
    @Bean
    Binding messageBinding(DirectExchange messageDirect, Queue messageQueue) {
        return BindingBuilder
                .bind(messageQueue)
                .to(messageDirect)
                .with(QueueName.KD_GEN_POLICY_STATISTICS);
    }

    /**
     * ttl队列和ttl交换机的绑定-routekey
     *
     * @param messageTtlQueue
     * @param messageTtlDirect
     * @return
     */
    @Bean
    public Binding messageTtlBinding(Queue messageTtlQueue, DirectExchange messageTtlDirect) {
        return BindingBuilder
                .bind(messageTtlQueue)
                .to(messageTtlDirect)
                .with(QueueName.KD_GEN_POLICY_STATISTICS_TTL);
    }


    /**
     * 普通消息交换机配置
     *
     * @return
     */
    @Bean
    DirectExchange messageDirect4Statistics() {
        return (DirectExchange) ExchangeBuilder
                .directExchange(QueueName.KD_GEN_CLAIM_CASE_MIRROR_STATICS_EXCHANGE)
                .durable(true)
                .build();
    }

    /**
     * 延时消息交换机配置
     *
     * @return
     */
    @Bean
    DirectExchange messageTtlDirect4Statistics() {
        return (DirectExchange) ExchangeBuilder
                .directExchange(QueueName.KD_GEN_CLAIM_CASE_MIRROR_STATICS_TTL_EXCHANGE)
                .durable(true)
                .build();
    }

    /**
     * TTL消息队列配置
     *
     * @return
     */
    @Bean
    Queue messageTtlQueue4Statistics() {
        return QueueBuilder
                .durable(QueueName.KD_GEN_CLAIM_CASE_MIRROR_STATICS_TTL)
                // 配置到期后转发的交换
                .withArgument("x-dead-letter-exchange", QueueName.KD_GEN_CLAIM_CASE_MIRROR_STATICS_EXCHANGE)
                // 配置到期后转发的路由键
                .withArgument("x-dead-letter-routing-key", QueueName.KD_GEN_CLAIM_CASE_MIRROR_STATICS)
                .build();
    }

    /**
     * 普通队列和普通交换机的绑定-routekey
     *
     * @param messageDirect4Statistics         消息中心交换配置
     * @param KD_GEN_CLAIM_CASE_MIRROR_STATICS 消息中心队列
     * @return
     */
    @Bean
    Binding messageBinding4Statistics(DirectExchange messageDirect4Statistics
            , Queue KD_GEN_CLAIM_CASE_MIRROR_STATICS) {
        return BindingBuilder
                .bind(KD_GEN_CLAIM_CASE_MIRROR_STATICS)
                .to(messageDirect4Statistics)
                .with(QueueName.KD_GEN_CLAIM_CASE_MIRROR_STATICS);
    }

    /**
     * ttl队列和ttl交换机的绑定-routekey
     *
     * @param messageTtlQueue4Statistics
     * @param messageTtlDirect4Statistics
     * @return
     */
    @Bean
    public Binding messageTtlBinding4Statistics(Queue messageTtlQueue4Statistics, DirectExchange messageTtlDirect4Statistics) {
        return BindingBuilder
                .bind(messageTtlQueue4Statistics)
                .to(messageTtlDirect4Statistics)
                .with(QueueName.KD_GEN_CLAIM_CASE_MIRROR_STATICS_TTL);
    }

    //盒马理赔数据
    @Bean
    public Queue KD_EXPORT_SETTLEMENT_CLAIM_HMRB_SEND_EMAIL() {
        return new Queue(QueueName.KD_EXPORT_SETTLEMENT_CLAIM_HMRB_SEND_EMAIL, true);
    }

    //盒马理赔清单全量
    @Bean
    public Queue KD_EXPORT_SETTLEMENT_CLAIM_ALL_HMRB_SEND_EMAIL() {
        return new Queue(QueueName.KD_EXPORT_SETTLEMENT_CLAIM_ALL_HMRB_SEND_EMAIL, true);
    }

    @Bean
    public Queue KD_SYNC_CLAIM_CASE_PUSH_OTHER() {
        return new Queue(QueueName.KD_SYNC_CLAIM_CASE_PUSH_OTHER, true);
    }

    @Bean
    public Queue KD_SYNC_CLAIM_CASE_PUSH_OTHER_JOB() {
        return new Queue(QueueName.KD_SYNC_CLAIM_CASE_PUSH_OTHER_JOB, true);
    }

    /**
     * 普通消息交换机配置
     *
     * @return
     */
    @Bean
    DirectExchange messageDirectPushOther() {
        return (DirectExchange) ExchangeBuilder
                .directExchange(QueueName.KD_GEN_CLAIM_CASE_PUSH_OTHER_STATICS_EXCHANGE)
                .durable(true)
                .build();
    }

    /**
     * 延时消息交换机配置
     *
     * @return
     */
    @Bean
    DirectExchange messageTtlDirectPushOther() {
        return (DirectExchange) ExchangeBuilder
                .directExchange(QueueName.KD_GEN_CLAIM_CASE_PUSH_OTHER_STATICS_TTL_EXCHANGE)
                .durable(true)
                .build();
    }

    /**
     * 普通隊列
     *
     * @return
     */
    @Bean
    public Queue messageQueuePushOther() {
        return new Queue(QueueName.KD_GEN_CLAIM_CASE_PUSH_OTHER_STATICS);
    }


    /**
     * TTL消息队列配置
     *
     * @return
     */
    @Bean
    Queue messageTtlQueuePushOther() {
        return QueueBuilder
                .durable(QueueName.KD_GEN_CLAIM_CASE_PUSH_OTHER_STATICS_TTL)
                // 配置到期后转发的交换
                .withArgument("x-dead-letter-exchange", QueueName.KD_GEN_CLAIM_CASE_PUSH_OTHER_STATICS_EXCHANGE)
                // 配置到期后转发的路由键
                .withArgument("x-dead-letter-routing-key", QueueName.KD_GEN_CLAIM_CASE_PUSH_OTHER_STATICS)
                .build();
    }


    /**
     * 普通队列和普通交换机的绑定-routekey
     *
     * @param messageDirectPushOther 消息中心交换配置
     * @param messageQueuePushOther  消息中心队列
     * @return
     */
    @Bean
    Binding messageBindingPushOther(DirectExchange messageDirectPushOther, Queue messageQueuePushOther) {
        return BindingBuilder
                .bind(messageQueuePushOther)
                .to(messageDirectPushOther)
                .with(QueueName.KD_GEN_CLAIM_CASE_PUSH_OTHER_STATICS);
    }

    /**
     * ttl队列和ttl交换机的绑定-routekey
     *
     * @param messageTtlQueuePushOther
     * @param messageTtlDirectPushOther
     * @return
     */
    @Bean
    public Binding messageTtlBindingPushOther(Queue messageTtlQueuePushOther, DirectExchange messageTtlDirectPushOther) {
        return BindingBuilder
                .bind(messageTtlQueuePushOther)
                .to(messageTtlDirectPushOther)
                .with(QueueName.KD_GEN_CLAIM_CASE_PUSH_OTHER_STATICS_TTL);
    }

    //10点发送盒马承保数据
    @Bean
    public Queue KD_SEND_EMAIL_UNDERWRITING_DATA_HMRB_T10() {
        return new Queue(QueueName.KD_SEND_EMAIL_UNDERWRITING_DATA_HMRB_T10, true);
    }

    //16点定时发送盒马承保数据
    @Bean
    public Queue KD_SEND_EMAIL_UNDERWRITING_DATA_HMRB_T16() {
        return new Queue(QueueName.KD_SEND_EMAIL_UNDERWRITING_DATA_HMRB_T16, true);
    }

    //导出盒马投保数据到sftp
    @Bean
    public Queue KD_EXPORT_HMRB_POLICY_JOB() {
        return new Queue(QueueName.KD_EXPORT_HMRB_POLICY_JOB, true);
    }

    @Bean
    public Queue KD_SYNC_CLAIM_CASE_4BS() {
        return new Queue(QueueName.KD_SYNC_CLAIM_CASE_4BS, true);
    }


    @Bean(QueueName.KD_PUSH_PREMIUM_CALCULATION_QUEUE)
    public Queue kdPushPremiumCalculationQueue() {
        return new Queue(QueueName.KD_PUSH_PREMIUM_CALCULATION_QUEUE, false);
    }

    @Bean(QueueName.KD_PUSH_PREMIUM_CALCULATION_EXCHANGE)
    public DirectExchange kdPushPremiumCalculationExchange() {
        return (DirectExchange) ExchangeBuilder
                .directExchange(QueueName.KD_GEN_CLAIM_CASE_PUSH_OTHER_STATICS_TTL_EXCHANGE)
                .durable(true)
                .build();
    }

    @Bean(QueueName.KD_PUSH_PREMIUM_CALCULATION_BINDING)
    public Binding kdPushPremiumCalculationBinding(@Qualifier(QueueName.KD_PUSH_PREMIUM_CALCULATION_EXCHANGE) DirectExchange kdPushPremiumCalculationExchange,
                                                   @Qualifier(QueueName.KD_PUSH_PREMIUM_CALCULATION_QUEUE) Queue kdPushPremiumCalculationQueue) {
        return BindingBuilder
                .bind(kdPushPremiumCalculationQueue)
                .to(kdPushPremiumCalculationExchange)
                .with(QueueName.KD_PUSH_PREMIUM_CALCULATION_QUEUE);
    }

    //众安达达雇主案件定时推送保司
    @Bean
    public Queue KD_EXPORT_CLAIM_DDZA_SEND_EMAIL_DAILY() {
        return new Queue(QueueName.KD_EXPORT_CLAIM_DDZA_SEND_EMAIL_DAILY, true);
    }

}
