package kd.injury.app.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import kd.common.tool.AliOssToolV3;
import kd.injury.app.service.CaseAttachmentService;
import kd.injury.common.constants.AliOssBucketPrefix;
import kd.injury.common.constants.AttachmentTypeEnum;
import kd.injury.dao.entity.CaseAttachment;
import kd.injury.dao.mapper.CaseAttachmentMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 案件附件Service实现类
 * 继承ServiceImpl实现具体业务逻辑
 */
@Slf4j
@Service
@DS("injury")
public class CaseAttachmentServiceImpl extends ServiceImpl<CaseAttachmentMapper, CaseAttachment> implements CaseAttachmentService {


    @Override
    public CaseAttachment uploadAttachment(String caseId, MultipartFile file, String uploader,
                                           String attachmentType, String description) {

        // 生成唯一文件名
        String originalFilename = file.getOriginalFilename();
        String fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
        String uniqueFilename = UUID.randomUUID() + fileExtension;


        String objectId = AliOssBucketPrefix.genFullObject(AliOssBucketPrefix.KD_INJURY_CASE, uniqueFilename);
        try {
            AliOssToolV3.uploadByStreamOuter(AliOssToolV3.PRIVATE_BUCKET_ID, objectId, file.getInputStream(), file.getContentType());
        } catch (Exception e) {
            log.error("CaseAttachmentServiceImpl.uploadAttachment:fileName{}", originalFilename);
            return null;
        }

        // 保存附件信息到数据库
        CaseAttachment attachment = new CaseAttachment();
        attachment.setCaseId(caseId);
        attachment.setId(UUID.randomUUID().toString());
        attachment.setFileName(originalFilename);
        attachment.setFileSize(file.getSize());
        attachment.setFileType(file.getContentType());
        attachment.setUploadTime(LocalDateTime.now());
        attachment.setUploader(uploader);
        attachment.setAttachmentType(attachmentType);
        attachment.setDescription(description);
        attachment.setIsDeleted(false);
        attachment.setFileObjectId(objectId);

        baseMapper.insert(attachment);
        return attachment;
    }

    @Override
    public CaseAttachment uploadAttachment(String caseId, String base64File, String uploader, String attachmentType, String description,String fileNamePrefix) {
        //处理截图文件
        String base64Image = base64File;
        if (StringUtils.isNotBlank(base64Image)) {
            //去除base64前缀
            String base64Data = base64Image.contains(",") ?
                    base64Image.substring(base64Image.indexOf(",") + 1) :
                    base64Image;
            //解码
            byte[] imageBytes = Base64.getDecoder().decode(base64Data);
            //创建并保存附件
            CaseAttachment attachment = new CaseAttachment();
            attachment.setId(UUID.randomUUID().toString());
            attachment.setCaseId(caseId);
            attachment.setAttachmentType(attachmentType);
            attachment.setFileName(fileNamePrefix + System.currentTimeMillis() + ".png");
            attachment.setFileSize((long) imageBytes.length);

            //设置文件类型
            attachment.setFileType("image/png");


            // 生成OSS存储路径
            String fileObjectId = AliOssBucketPrefix.genFullObject(
                    AliOssBucketPrefix.KD_INJURY_CASE,
                    UUID.randomUUID().toString()
            );
            // 设置文件对象ID
            attachment.setFileObjectId(fileObjectId);
            attachment.setFilePath(fileObjectId);

            //上传文件到OSS
            try {
                AliOssToolV3.uploadByStreamOuter(
                        AliOssToolV3.PRIVATE_BUCKET_ID,
                        fileObjectId,
                        new ByteArrayInputStream(imageBytes)
                );
            } catch (Exception e) {
                log.error("上传截图到OSS失败", e);
                throw new RuntimeException("文件上传失败");
            }


            // 设置上传信息
            attachment.setUploadTime(LocalDateTime.now());
            attachment.setUploader("system_sync");

            // 设置描述信息
            attachment.setDescription(description);

            // 设置删除状态
            attachment.setIsDeleted(false);

            // 设置创建和更新时间
            attachment.setCreateTime(LocalDateTime.now());
            attachment.setUpdateTime(LocalDateTime.now());

            // 保存附件记录
            baseMapper.insert(attachment);
            return attachment;
        }
        return null;
    }

    @Override
    public boolean deleteAttachment(String attachmentId, String operator) {
        CaseAttachment attachment = baseMapper.selectById(attachmentId);
        if (attachment != null) {
            // 逻辑删除
            attachment.setIsDeleted(true);
            return baseMapper.updateById(attachment) > 0;
        }
        return false;
    }

    @Override
    public boolean deleteAttachment(List<String> attachmentIdList) {
        List<CaseAttachment> caseAttachments = baseMapper.selectBatchIds(attachmentIdList);
        if (!ObjectUtils.isEmpty(caseAttachments)) {
            // 逻辑删除
            List<Integer> collect = caseAttachments.stream().peek(x -> x.setIsDeleted(true)).map(x -> baseMapper.updateById(x)).collect(Collectors.toList());
            return collect.stream().allMatch(x -> x > 1);
        }
        return false;
    }

    @Override
    public List<CaseAttachment> selectByCaseAndAttachmentId(String id, List<String> fileObjectIdList, String attachmentType) {
        return baseMapper.selectByAttachmentTypeAndCaseId(id, fileObjectIdList, attachmentType);
    }

    @Override
    public Map<String, String> downloadFile(Map<String, CaseAttachment> collect) {
        Map<String, String> result = new HashMap<>();
        for (Map.Entry<String, CaseAttachment> entry : collect.entrySet()) {
            String key = entry.getKey();
            String downloadUrl = AliOssToolV3.getPrivateUrlOuter(AliOssToolV3.PRIVATE_BUCKET_ID, key);
            result.put(key, downloadUrl);
        }
        return result;
    }

    /**
     * 计算文件哈希值
     *
     * @param file 文件
     * @return 文件哈希
     */
    private String calculateFileHash(File file) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(Files.readAllBytes(file.toPath()));

            StringBuilder hexString = new StringBuilder(2 * hash.length);
            for (byte b : hash) {
                String hex = String.format("%02x", b);
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException | IOException e) {
            log.error("计算文件哈希失败", e);
            return null;
        }
    }
}