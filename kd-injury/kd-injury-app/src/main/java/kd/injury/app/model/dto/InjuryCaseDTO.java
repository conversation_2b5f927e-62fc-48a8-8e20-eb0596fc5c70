package kd.injury.app.model.dto;

import kd.injury.dao.entity.CaseInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class InjuryCaseDTO extends CaseInfo {

    /**
     * 操作类型 add/update
     */
    private String operateType;

    /**
     * 案件备注
     */
    private String remark;

    /**
     * 案件状态名称
     */
    private String caseStatusName;

    /**
     * 风神状态名称
     */
    private String externalStateName;


    /**
     * 登陆人
     */
    private String loginUser;

    /**
     * 上传的所有文件
     */
    private MultipartFile[] filesArray;


    /**
     * 附件归属的业务类型
     *
     * @see kd.injury.common.constants.AttachmentTypeEnum
     */
    private String attachmentType;


    /**
     * 对象存储id集合
     */
    private List<String> fileObjectId;

    /**
     * 附件id
     */
    private List<String> attachmentIdList;
    /**
     * 页面可操作按钮集合
     */
    private List<String> btnList;

    /**
     * 申报提示
     */
    private String declarationTips;

    /***
     * 截图文件
     */
    private String screenshotFile;

    /**
     * 事故照片(风神爬虫传输数据使用)
     */
    private List<String> accidentPhotoList;

    /**
     * TODO:董原绯
     * 判断骑手类型的描述
     */
//    private String determinePlayer;

}
