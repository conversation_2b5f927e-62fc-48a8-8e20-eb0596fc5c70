package kd.injury.app.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.common.collect.Lists;
import kd.common.context.RedisCommonKeyEnum;
import kd.common.tool.ESHighLevelRESTClientTool;
import kd.common.tool.RedisTool3;
import kd.injury.app.acl.ManagerAclService;
import kd.injury.app.acl.mapper.ManagerMapper;
import kd.injury.app.acl.mapper.RoleMapper;
import kd.injury.app.convert.InjuryEntityDTOConverter;
import kd.injury.app.es.EsClientFactory;
import kd.injury.app.model.dto.*;
import kd.injury.app.model.req.CaseAssignmentRequest;
import kd.injury.app.model.req.CaseRequest;
import kd.injury.app.model.vo.CaseInfoVo;
import kd.injury.app.security.UserContextUtil;
import kd.injury.app.service.CaseAttachmentService;
import kd.injury.app.service.CaseInfoService;
import kd.injury.app.service.OperatingStrategy;
import kd.injury.app.service.impl.strategy.CaseLogUtil;
import kd.injury.app.util.DateUtils;
import kd.injury.common.constants.*;
import kd.injury.common.util.AssertUtil;
import kd.injury.common.util.JsonTool;
import kd.injury.dao.entity.CaseAttachment;
import kd.injury.dao.entity.CaseInfo;
import kd.injury.dao.entity.CaseRemark;
import kd.injury.dao.mapper.CaseAttachmentMapper;
import kd.injury.dao.mapper.CaseInfoMapper;
import kd.injury.dao.mapper.CaseRemarkMapper;
import kd.injury.dao.mapper.CaseStatusLogMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 案件信息Service实现类
 * 继承ServiceImpl实现具体业务逻辑
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CaseInfoServiceImpl extends ServiceImpl<CaseInfoMapper, CaseInfo> implements CaseInfoService {

    private final CaseRemarkMapper caseRemarkMapper;
    private final CaseStatusLogMapper caseStatusLogMapper;
    private final OperatingStrategy operatingStrategy;
    private final CaseAttachmentMapper caseAttachmentMapper;
    private final CaseAttachmentService caseAttachmentService;
    private final InjuryEntityDTOConverter injuryEntityDTOConverter;
    private final ManagerMapper managerMapper;
    private final ManagerAclService managerAclService;
    private final RoleMapper roleMapper;
    private final Executor executor;

    @Autowired
    private EsClientFactory esClientFactory;


    @Override
    public InjuryCaseDTO getCaseDetailById(String id) {
        CaseInfo caseInfo = baseMapper.selectById(id);
        AssertUtil.check(ObjectUtils.isEmpty(caseInfo), "案件未查询");
        InjuryCaseDTO dto = JsonTool.json2Obj(JsonTool.obj2json(caseInfo), InjuryCaseDTO.class);
//        // 查询关联备注
        List<CaseRemark> remarks = caseRemarkMapper.getRemarksByCaseId(id);
        dto.setRemarks(remarks);

//        // 查询关联附件
        List<CaseAttachment> attachments = caseAttachmentMapper.getCaseAttachmentsByCaseId(id);
        dto.setAttachments(attachments);

        Map<String, String> caseStatusMap = RedisTool3.hgetAll(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "CASE_STATUS");
        Map<String, String> externalStatusMap = RedisTool3.hgetAll(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX + "FENGSHEN_CASE");
        dto.setCaseStatusName(caseStatusMap.get(dto.getCaseStatus()));
        dto.setExternalStateName(externalStatusMap.get(dto.getExternalState()));
        dto.setDeclarationTips(calculateRemainingTime(LocalDateTime.now(), dto.getAccidentTime(), dto.getDeclarationTime()));
        return dto;
    }

    public static String calculateRemainingTime(LocalDateTime currentTime, LocalDateTime accidentTime, LocalDateTime claimTime) {
        if (currentTime == null || accidentTime == null) {
            return null;
        }
        long allowedSeconds = 29L * 24 * 60 * 60; // 29天的总秒数

        // 如果申报时间不为null，说明已经申报
        if (claimTime != null) {
            long secondsBetweenAccidentAndClaim = ChronoUnit.SECONDS.between(accidentTime, claimTime);

            // 申报时间在29天内，合规申报
            if (secondsBetweenAccidentAndClaim <= allowedSeconds) {
                return "已在规定时间内申报";
            }

            // 申报时间超过29天，计算逾期时间
            long overdueSeconds = secondsBetweenAccidentAndClaim - allowedSeconds;
            return "逾期申报，逾期时间为" + formatExceededTime(overdueSeconds);
        }

        // 未申报，计算剩余时间
        long secondsSinceAccident = ChronoUnit.SECONDS.between(accidentTime, currentTime);
        long remainingSeconds = allowedSeconds - secondsSinceAccident;

        if (remainingSeconds <= 0) {
            return "申报超时，逾期" + formatExceededTime(Math.abs(remainingSeconds));
        }

        return formatRemainingTime(remainingSeconds);
    }

    private static String formatRemainingTime(long seconds) {
        long days = seconds / (24 * 60 * 60);
        seconds %= (24 * 60 * 60);
        long hours = seconds / (60 * 60);
        seconds %= (60 * 60);
        long minutes = seconds / 60;
        seconds %= 60;

//        return String.format("剩余%d天%d小时%d分%d秒", days, hours, minutes, seconds);
        return String.format("剩余%d天%d小时", days, hours);
    }

    private static String formatExceededTime(long seconds) {
        long days = seconds / (24 * 60 * 60);
        seconds %= (24 * 60 * 60);
        long hours = seconds / (60 * 60);
        seconds %= (60 * 60);
        long minutes = seconds / 60;
        seconds %= 60;

//        return String.format("%d天%d小时%d分%d秒", days, hours, minutes, seconds);//精确到秒
        //精确到天
        return String.format("%d天", days);
    }

    @Override
    public List<ManagerDTO> getManagersBySameRole(Integer cormForm, String userId) {
        // 1. 查询该人员的角色ID
        AssertUtil.check(cormForm == null || cormForm != 1 && cormForm != 2, "参数错误");
        List<ManagerDTO> dto = managerMapper.selectByManagerId(userId);
        AssertUtil.check(ObjectUtils.isEmpty(dto), "角色不存在");
        List<String> roleId = dto.stream().map(ManagerDTO::getRoleId).collect(Collectors.toList());
        List<RoleDTO> currentUserRole = roleMapper.selectBatchIds(roleId);
        if (ObjectUtils.isEmpty(currentUserRole)) {
            return Collections.emptyList();
        }
        Set<String> set = currentUserRole.stream().map(RoleDTO::getCode).collect(Collectors.toSet());
        List<RoleDTO> roleDTOS;
        List<String> targetRoleList = new ArrayList<>();
        //判断管理员角色 是管理员则加上管理员角色ID
        if (set.contains(RoleConstants.INJURY_BIZ_MANAGER_ROLE_CODE)) {
            targetRoleList.add(RoleConstants.INJURY_BIZ_MANAGER_ROLE_CODE);
        }
        //若在申报页面
        if (cormForm == 1) {
            targetRoleList.add(RoleConstants.INJURY_APPLY_ROLE_CODE);
            roleDTOS = roleMapper.selectList(new LambdaQueryWrapper<RoleDTO>()
                    .in(RoleDTO::getCode, targetRoleList));
        } else {
            targetRoleList.add(RoleConstants.INJURY_CHECK_ROLE_CODE);
            roleDTOS = roleMapper.selectList(new LambdaQueryWrapper<RoleDTO>()
                    .in(RoleDTO::getCode, targetRoleList));
        }
        Set<String> roleIdList = roleDTOS.stream().map(RoleDTO::getId).collect(Collectors.toSet());
        List<ManagerDTO> managerDTOS = managerMapper.selectManagersByRoleIds(roleIdList);
        if (!ObjectUtils.isEmpty(managerDTOS)) {
            managerDTOS.sort(Comparator.comparing(ManagerDTO::getUserName));
        }
        return managerDTOS;
    }

    @Override
    public String updateAssignment(CaseAssignmentRequest request) {
        //验证案件是否存在
        List<CaseInfo> list = baseMapper.selectByIds(request.getCaseIds());
        AssertUtil.check(ObjectUtils.isEmpty(list), "案件不存在");
        //影响行数
        int affectedRows;
        // 根据角色ID确定更新字段
        switch (request.getRoleId()) {
            case 1: // 更新责任人
                affectedRows = baseMapper.updateResponsiblePerson(request.getUserName(), request.getCaseIds());
                if (affectedRows == 0) {
                    throw new RuntimeException("更新责任人失败");
                }
                return "responsible_person";
            case 2: // 更新审核人
                affectedRows = baseMapper.updateReviewer(request.getUserName(), request.getCaseIds());
                if (affectedRows == 0) {
                    throw new RuntimeException("更新审核人失败");
                }
                return "reviewer";
            default:
                throw new IllegalArgumentException("无效的角色ID");
        }
    }


    public PageDTO<CaseInfoVo> getCasePage(CaseRequest caseRequest) {

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.sort("accidentTime", SortOrder.ASC);
        // 设置分页
        int from = (caseRequest.getPage() - 1) * caseRequest.getSize();
        searchSourceBuilder.from(from)
                .size(caseRequest.getSize())
                .trackTotalHits(true); // 修复：启用精确的总数统计，避免超过1万条时只返回10000
        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
        //构建查询条件 备案号 骑手姓名 骑手身份证号 省份 城市 责任人 事故标签 案件状态

        // 案件通用查询逻辑
        esCaseCommonQuery(caseRequest, boolBuilder);

        // 按唯一字段去重
        // searchSourceBuilder.collapse(new CollapseBuilder("id"));

        searchSourceBuilder.query(boolBuilder);
        SearchResponse searchResponse = null;
        try {
            searchResponse = esClientFactory.getClient().search(ESHighLevelRESTClientTool.gentSearchRequest(
                    ElasticIndexName.CASE_INFO, searchSourceBuilder), RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("search es error:{}", e);
            return new PageDTO<>(
                    Collections.emptyList(),
                    0,
                    caseRequest.getPage(),
                    caseRequest.getSize(),
                    0
            );
        }

        SearchHits searchHits = searchResponse.getHits();
        Integer total = (int) searchHits.getTotalHits().value;

        List<CaseInfoVo> results = new ArrayList<>();
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());

        for (SearchHit hit : searchHits.getHits()) {
            CaseInfoVo dto = JsonTool.json2Obj(hit.getSourceAsString(), CaseInfoVo.class);
            if (dto != null) {
                // 查询关联备注
                List<CaseRemark> remarks = caseRemarkMapper.getRemarksByCaseId(dto.getId());
                dto.setRemarks(remarks);
                results.add(dto);
            }
        }
        convertList(caseRequest, results);
        LocalDateTime now = LocalDateTime.now();
        int pages = (caseRequest.getSize() == 0) ? 0 : (total + caseRequest.getSize() - 1) / caseRequest.getSize();
        //计算申报提示字段
        Optional.of(results).orElse(Collections.emptyList()).forEach(x -> x.setDeclarationTips(calculateRemainingTime(now,
                x.getAccidentTime() != null ? x.getAccidentTime().toInstant().atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime() : null,
                x.getDeclarationTime() != null ? x.getDeclarationTime().toInstant().atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime() : null)));
        //to do 转换一下分页
        return new PageDTO<>(
                results,
                total,
                caseRequest.getPage(),
                caseRequest.getSize(),
                pages
        );
    }

    /**
     * 构建超时状态查询条件
     */
    private QueryBuilder buildTimeoutStatusQuery(int timeoutStatus) {
        long allowedSeconds = 29L * 24 * 60 * 60;

        // 超时判断脚本：严格定义超时条件
        String timeoutScript =
                "def isTimeout = false;  // 默认不超时\n" +
                        // 事故时间不存在 → 不超时（单独占一行注释）
                        "if (doc['accidentTime'].size() > 0) {\n" +
                        "  def accidentSec = doc['accidentTime'].value.toInstant().getEpochSecond();\n" +
                        "  def deadline = accidentSec + params.allowedSeconds;\n" +
                        "  if (doc['declarationTime'].size() > 0) {\n" +
                        "    def declarationSec = doc['declarationTime'].value.toInstant().getEpochSecond();\n" +
                        "    isTimeout = declarationSec > deadline;\n" +
                        "  } else {\n" +
                        "    isTimeout = params.nowEpochSecond > deadline;\n" +
                        "  }\n" +
                        "}\n" +
                        "return isTimeout;";  // 超时判断结果

        // 未超时脚本：取反超时结果（所有不超时的均算未超时）
        String notTimeoutScript =
                "def isTimeout = false;\n" +
                        "if (doc['accidentTime'].size() > 0) {\n" +
                        "  def accidentSec = doc['accidentTime'].value.toInstant().getEpochSecond();\n" +
                        "  def deadline = accidentSec + params.allowedSeconds;\n" +
                        "  if (doc['declarationTime'].size() > 0) {\n" +
                        "    def declarationSec = doc['declarationTime'].value.toInstant().getEpochSecond();\n" +
                        "    isTimeout = declarationSec > deadline;\n" +
                        "  } else {\n" +
                        "    isTimeout = params.nowEpochSecond > deadline;\n" +
                        "  }\n" +
                        "}\n" +
                        "return !isTimeout;";  // 直接取反超时结果，无额外注释

        long nowEpochSecond = LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond();

        // Java 8 兼容的 Map 创建方式
        Map<String, Object> params = new HashMap<>();
        params.put("nowEpochSecond", nowEpochSecond);
        params.put("allowedSeconds", allowedSeconds);

        if (timeoutStatus == 1) {
            return QueryBuilders.scriptQuery(new Script(ScriptType.INLINE, "painless", timeoutScript, params));
        } else if (timeoutStatus == 2) {
            return QueryBuilders.scriptQuery(new Script(ScriptType.INLINE, "painless", notTimeoutScript, params));
        }
        return null;
    }

    /**
     * 列表数据转换(根据登录用户角色和菜单页面查询按钮列表)
     *
     * @param caseRequest 请求参数
     * @param list        查询列表
     */
    private void convertList(CaseRequest caseRequest, List<CaseInfoVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 获取当前登录用户拥有角色列表
        List<RoleDTO> roleDTOList = UserContextUtil.getRoles();
        if (CollectionUtils.isEmpty(roleDTOList)) {
            log.info("当前登录用户无角色");
            return;
        }
        List<String> currentUserRoleCodeList = roleDTOList.stream().map(RoleDTO::getCode)
                .distinct().collect(Collectors.toList());
        for (CaseInfoVo dto : list) {
            List<String> btnList = new ArrayList<>();
            // 申报岗可查看按钮
            if (currentUserRoleCodeList.contains(RoleConstants.INJURY_APPLY_ROLE_CODE)
                    && null != caseRequest.getComeFrom() && 1 == caseRequest.getComeFrom()) {
                addApplyRoleBtnList(caseRequest, dto, btnList);
            }
            // 审核岗可查看按钮
            if (currentUserRoleCodeList.contains(RoleConstants.INJURY_CHECK_ROLE_CODE)
                    && null != caseRequest.getComeFrom() && 2 == caseRequest.getComeFrom()) {
                addCheckRoleBtnList(caseRequest, dto, btnList);
            }
            // 业务管理员可查看按钮
            if (currentUserRoleCodeList.contains(RoleConstants.INJURY_BIZ_MANAGER_ROLE_CODE)) {
                // 申报岗
                if (null != caseRequest.getComeFrom() && 1 == caseRequest.getComeFrom()) {
                    addApplyRoleBtnList(caseRequest, dto, btnList);
                }
                // 审核岗
                if (null != caseRequest.getComeFrom() && 2 == caseRequest.getComeFrom()) {
                    addCheckRoleBtnList(caseRequest, dto, btnList);
                }
            }
            dto.setBtnList(btnList.stream().distinct().collect(Collectors.toList()));
        }
    }

    /**
     * 添加申报岗可查询按钮列表
     *
     * @param dto     案件信息
     * @param btnList 按钮列表
     */
    private void addApplyRoleBtnList(CaseRequest caseRequest, CaseInfoVo dto, List<String> btnList) {
        // 全部
        if (StringUtils.isBlank(caseRequest.getCaseStatus())) {
            // 案件详情
            btnList.add(CaseQueryBtnEnum.CASE_DETAIL.getCode());
            // 改派
            btnList.add(CaseQueryBtnEnum.REASSIGNMENT.getCode());
            return;
        }
        // 待处理状态
        if (CaseStatus.PENDING.getCode().equals(dto.getCaseStatus())) {
            // 案件详情
            btnList.add(CaseQueryBtnEnum.CASE_DETAIL.getCode());
            // 首访
            if (null == dto.getFirstVisitTime()
                    && StringUtils.isBlank(dto.getFirstVisitRemark())) {
                btnList.add(CaseQueryBtnEnum.FIRST_VISIT.getCode());
            }
            // 撤案
            btnList.add(CaseQueryBtnEnum.WITHDRAW_CASE.getCode());
            // 中止
            btnList.add(CaseQueryBtnEnum.STOP.getCode());
            // 上传材料
            btnList.add(CaseQueryBtnEnum.UPLOAD_MATERIALS.getCode());
            // 案件备注
            btnList.add(CaseQueryBtnEnum.CASE_REMARK.getCode());
            // 案件申报
            btnList.add(CaseQueryBtnEnum.CASE_APPLY.getCode());
            // 改派
            btnList.add(CaseQueryBtnEnum.REASSIGNMENT.getCode());
        }
        // 待补正材料状态
        if (CaseStatus.AWAITING_SUPPLEMENTARY_MATERIALS.getCode().equals(dto.getCaseStatus())) {
            // 案件详情
            btnList.add(CaseQueryBtnEnum.CASE_DETAIL.getCode());
            // 撤案
            btnList.add(CaseQueryBtnEnum.WITHDRAW_CASE.getCode());
            // 中止
            btnList.add(CaseQueryBtnEnum.STOP.getCode());
            // 上传材料
            btnList.add(CaseQueryBtnEnum.UPLOAD_MATERIALS.getCode());
            // 案件备注
            btnList.add(CaseQueryBtnEnum.CASE_REMARK.getCode());
            // 补材完成
            btnList.add(CaseQueryBtnEnum.MATERIALS_FINISH.getCode());
            // 改派
            btnList.add(CaseQueryBtnEnum.REASSIGNMENT.getCode());
        }
        // 已撤案状态
        if (CaseStatus.WITHDRAWN.getCode().equals(dto.getCaseStatus())) {
            // 案件详情
            btnList.add(CaseQueryBtnEnum.CASE_DETAIL.getCode());
            // 案件备注
            btnList.add(CaseQueryBtnEnum.CASE_REMARK.getCode());
            // 改派
            btnList.add(CaseQueryBtnEnum.REASSIGNMENT.getCode());
        }
        // 小中止状态
        if (CaseStatus.SUSPENDED.getCode().equals(dto.getCaseStatus())) {
            // 案件详情
            btnList.add(CaseQueryBtnEnum.CASE_DETAIL.getCode());
            // 取消中止
            btnList.add(CaseQueryBtnEnum.CANCEL_STOP.getCode());
            // 案件备注
            btnList.add(CaseQueryBtnEnum.CASE_REMARK.getCode());
            // 改派
            btnList.add(CaseQueryBtnEnum.REASSIGNMENT.getCode());
        }
    }

    /**
     * 添加审核岗可查询按钮列表
     *
     * @param dto     案件信息
     * @param btnList 按钮列表
     */
    private void addCheckRoleBtnList(CaseRequest caseRequest, CaseInfoVo dto, List<String> btnList) {
        // 全部
        if (StringUtils.isBlank(caseRequest.getCaseStatus())) {
            // 案件详情
            btnList.add(CaseQueryBtnEnum.CASE_DETAIL.getCode());
            // 改派
            btnList.add(CaseQueryBtnEnum.REASSIGNMENT.getCode());
            return;
        }
        // 待处理状态
        if (CaseStatus.PENDING.getCode().equals(dto.getCaseStatus())) {
            // 案件详情
            btnList.add(CaseQueryBtnEnum.CASE_DETAIL.getCode());
            // 案件备注
            btnList.add(CaseQueryBtnEnum.CASE_REMARK.getCode());
        }
        // 申报中状态
        if (CaseStatus.DECLARING.getCode().equals(dto.getCaseStatus())) {
            // 案件详情
            btnList.add(CaseQueryBtnEnum.CASE_DETAIL.getCode());
            // 案件备注
            btnList.add(CaseQueryBtnEnum.CASE_REMARK.getCode());
            // 申报确认
            btnList.add(CaseQueryBtnEnum.APPLY_CONFIRM.getCode());
        }
        // 已申报状态
        if (CaseStatus.DECLARED.getCode().equals(dto.getCaseStatus())) {
            // 案件详情
            btnList.add(CaseQueryBtnEnum.CASE_DETAIL.getCode());
            // 案件备注
            btnList.add(CaseQueryBtnEnum.CASE_REMARK.getCode());
            // 退回补材
            btnList.add(CaseQueryBtnEnum.RETURN_MATERIALS.getCode());
            // 职伤认定
            btnList.add(CaseQueryBtnEnum.OCCUPATIONAL_INJURY.getCode());
            // 风神状态同步
            btnList.add(CaseQueryBtnEnum.SYNC_STATUS.getCode());
            // 改派
            btnList.add(CaseQueryBtnEnum.REASSIGNMENT.getCode());
        }
        // 职伤已认定状态
        if (CaseStatus.OCCUPATIONAL_INJURY_CONFIRMED.getCode().equals(dto.getCaseStatus())) {
            // 案件详情
            btnList.add(CaseQueryBtnEnum.CASE_DETAIL.getCode());
            // 案件备注
            btnList.add(CaseQueryBtnEnum.CASE_REMARK.getCode());
            // 风神状态同步
            btnList.add(CaseQueryBtnEnum.SYNC_STATUS.getCode());
            // 结案
            btnList.add(CaseQueryBtnEnum.FINISH.getCode());
            // 改派
            btnList.add(CaseQueryBtnEnum.REASSIGNMENT.getCode());
        }
        // 职伤不予认定状态
        if (CaseStatus.OCCUPATIONAL_INJURY_DENIED.getCode().equals(dto.getCaseStatus())) {
            // 案件详情
            btnList.add(CaseQueryBtnEnum.CASE_DETAIL.getCode());
            // 案件备注
            btnList.add(CaseQueryBtnEnum.CASE_REMARK.getCode());
            // 风神状态同步
            btnList.add(CaseQueryBtnEnum.SYNC_STATUS.getCode());
            // 结案
            btnList.add(CaseQueryBtnEnum.FINISH.getCode());
            // 改派
            btnList.add(CaseQueryBtnEnum.REASSIGNMENT.getCode());
        }
    }

    @Override
    public InjuryCaseResponseDTO createOrUpdateInjuryCase(InjuryCaseDTO caseInfoDTO) {
        return operatingStrategy.process(caseInfoDTO);
    }

    @Override
    public InjuryCaseResponseDTO uploadInjuryMaterial(InjuryCaseDTO body) {
        AssertUtil.check(ObjectUtils.isEmpty(body.getId()), "案件id不能为空");
        AssertUtil.check(ObjectUtils.isEmpty(body.getFilesArray()), "文件不能为空");
        AssertUtil.check(StringUtils.isBlank(body.getAttachmentType()), "业务附件类型不能为空");
        CaseInfo update = baseMapper.selectById(body.getId());
        AssertUtil.check(ObjectUtils.isEmpty(update), "案件不存在");
        List<String> objectIdList = uploadFile(body);
        return new InjuryCaseResponseDTO().setFileObjectIdList(objectIdList).setResult(true);
    }

    private List<String> uploadFile(InjuryCaseDTO body) {
        return Arrays.stream(body.getFilesArray()).map(file ->
                        caseAttachmentService.uploadAttachment(body.getId(),
                                file,
                                body.getLoginUser(),
                                body.getAttachmentType(),
                                null).getFileObjectId())
                .collect(Collectors.toList());
    }

    @Override
    public InjuryCaseResponseDTO downloadInjuryMaterial(InjuryCaseDTO body) {
        List<CaseAttachment> all = caseAttachmentService.selectByCaseAndAttachmentId(body.getId(), body.getAttachmentIdList(), body.getAttachmentType());
        Map<String, CaseAttachment> collect = all.stream()
                .collect(Collectors.toMap(CaseAttachment::getFileObjectId, x -> x, (x, y) -> x));
        Map<String, String> downloadFile = caseAttachmentService.downloadFile(collect);
        List<InjuryCaseFileDTO> injuryCaseFileDTOS = Optional.ofNullable(injuryEntityDTOConverter.entity2DTOList(all))
                .orElse(Collections.emptyList());
        injuryCaseFileDTOS.forEach(x -> x.setDownloadUrl(downloadFile.get(x.getFileObjectId())));
        return new InjuryCaseResponseDTO().setFileList(injuryCaseFileDTOS);
    }

    @Override
    public InjuryCaseResponseDTO deleteInjuryMaterial(InjuryCaseDTO body) {
        List<String> attachmentIdList = body.getAttachmentIdList();
        return new InjuryCaseResponseDTO().setResult(!ObjectUtils.isEmpty(attachmentIdList) && caseAttachmentService.deleteAttachment(attachmentIdList));
    }

    @Override
    public List<CaseInfoVo> queryCaseList(CaseRequest caseRequest) {
        List<CaseInfoVo> caseInfoVoList = new ArrayList<>();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.sort("accidentTime", SortOrder.ASC);
        searchSourceBuilder.trackTotalHits(true);
        searchSourceBuilder.size(100000);
        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
        // 案件通用查询逻辑
        esCaseCommonQuery(caseRequest, boolBuilder);
        searchSourceBuilder.query(boolBuilder);
        SearchResponse searchResponse = null;
        try {
            searchResponse = esClientFactory.getClient().search(ESHighLevelRESTClientTool.gentSearchRequest(
                    ElasticIndexName.CASE_INFO, searchSourceBuilder), RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("search es error:{}", e);
            return caseInfoVoList;
        }

        SearchHits searchHits = searchResponse.getHits();
        int total = (int) searchHits.getTotalHits().value;
        if (total == 0) {
            return caseInfoVoList;
        }
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        List<String> caseIdList = new ArrayList<>();
        for (SearchHit hit : searchHits.getHits()) {
            CaseInfoVo caseInfoVo = JsonTool.json2Obj(hit.getSourceAsString(), CaseInfoVo.class);
            if (caseInfoVo != null) {
                caseIdList.add(caseInfoVo.getId());
                caseInfoVoList.add(caseInfoVo);
            }
        }
        if (CollectionUtils.isNotEmpty(caseIdList)) {
            List<CaseRemark> caseRemarkList = caseRemarkMapper.queryByCaseIdList(caseIdList);
            if (CollectionUtils.isNotEmpty(caseRemarkList)) {
                caseInfoVoList.forEach(caseInfoVo -> {
                    // 查询关联备注列表
                    caseInfoVo.setRemarks(caseRemarkList.stream().filter(caseRemark ->
                            caseRemark.getCaseId().equals(caseInfoVo.getId())).collect(Collectors.toList()));
                });
            }
        }
        // 计算申报提示
        Optional.of(caseInfoVoList).orElse(Collections.emptyList()).forEach(x ->
                x.setDeclarationTips(calculateRemainingTime(LocalDateTime.now(),
                        x.getAccidentTime() != null ? x.getAccidentTime().toInstant().atZone(
                                ZoneId.of("Asia/Shanghai")).toLocalDateTime() : null,
                        x.getDeclarationTime() != null ? x.getDeclarationTime().toInstant().atZone(
                                ZoneId.of("Asia/Shanghai")).toLocalDateTime() : null)));
        return caseInfoVoList;
    }

    /**
     * 爬虫同步外部（风神）的案件
     *
     * @param injuryCaseDTOS
     * @return
     */
    @Transactional
    @Override
    public Boolean syncExternalCase(InjuryCaseDTO injuryCaseDTOS) {
        log.info("风神同步信息:{}",JsonTool.obj2json(injuryCaseDTOS));
        //判断案件是否已存在
        String caseNumber = injuryCaseDTOS.getCaseNumber();
        if (!ObjectUtils.isEmpty(baseMapper.selectByCaseNumberList(caseNumber))) {
            log.error("案件已存在, caseNumber: {}", caseNumber);
            return false;
        }
        //设置案件状态为未认领
        String caseId = UUID.randomUUID().toString();
        injuryCaseDTOS.setId(caseId);
        injuryCaseDTOS.setCaseStatus(CaseStatus.UNCLAIMED.getCode());
        boolean insertResult = baseMapper.insert(injuryCaseDTOS) > 0;
        if (insertResult && StringUtils.isNotBlank(injuryCaseDTOS.getScreenshotFile())) {
            insertResult = caseAttachmentService.uploadAttachment(caseId, injuryCaseDTOS.getScreenshotFile(),
                    "system_sync", AttachmentTypeEnum.SCREENSHOT_MATERIAL.name(), "风神同步案件" ,"screenshot_") != null;
        }

        if (insertResult && CollectionUtils.isNotEmpty(injuryCaseDTOS.getAccidentPhotoList())) {
            /**
             * 处理事故图片;
             * Step1.上传到oss
             * Step2.添加数据
             */
            injuryCaseDTOS.getAccidentPhotoList().forEach(x -> {
                caseAttachmentService.uploadAttachmentFromUrl(caseId, x,
                        "system_sync", AttachmentTypeEnum.ACCIDENT_MATERIALS.name(), "风神同步事故照片");
            });
        }
        return insertResult;
    }


    /**
     * 未认领案件更新责任人
     */
    @Override
    public Boolean updatePersonResponsible(String username, String caseId) {
        //参数校验
        if (StringUtils.isBlank(username)) {
            log.error("责任人用户名不能为空, caseId: {}", caseId);
            return false;
        }
        if (StringUtils.isBlank(caseId)) {
            log.error("案件ID不能为空");
            return false;
        }

        //根据caseId查询案件
        CaseInfo caseInfo = baseMapper.selectById(caseId);
        if (caseInfo == null) {
            log.error("案件不存在, caseId: {}");
            return false;
        }

        //检查案件状态是否为"未认领"
        if (!CaseStatus.UNCLAIMED.getCode().equals(caseInfo.getCaseStatus())) {
            log.error("案件状态非法, 当前状态: {}, 要求状态: {}, caseId: {}",
                    caseInfo.getCaseStatus(), CaseStatus.UNCLAIMED.getCode(), caseId);
            return false;
        }

        //如果为未认领，将责任人的名字修改为登陆人
        CaseInfo update = new CaseInfo();
        update.setId(caseInfo.getId());
        update.setResponsiblePerson(username);
        update.setCaseStatus(CaseStatus.PENDING.getCode());
        CaseLogUtil.saveCaseStatusLog(OperateTypeConstant.ASSIGN_RESPONSIBLE_PERSON, "改派责任人", caseInfo, update);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public Boolean verifyCaseExist(String caseNumber) throws IOException {
        AssertUtil.check(StringUtils.isBlank(caseNumber), "案件号不能为空");
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.fetchSource(false);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery().filter(QueryBuilders.termQuery("caseNumber", caseNumber.trim()));
        searchSourceBuilder.query(boolQueryBuilder);
        SearchRequest request = new SearchRequest(ElasticIndexName.CASE_INFO);
        request.source(searchSourceBuilder);
        SearchResponse search = esClientFactory.getClient().search(request, RequestOptions.DEFAULT);
        long value = search.getHits().getTotalHits().value;
        return value > 0;
    }

    /**
     * 批量更新责任人
     */
    @Override
    public void updatePersonResponsibleByCaseNumber(List<InjuryCaseDTO> caseDTOS) {
        List<List<InjuryCaseDTO>> partition = Lists.partition(caseDTOS, 1000);
        for (List<InjuryCaseDTO> injuryCaseDTOS : partition) {
            CompletableFuture.runAsync(() -> {
                Map<String, List<InjuryCaseDTO>> target = injuryCaseDTOS.stream()
                        .filter(Objects::nonNull)
                        .filter(dto -> dto.getResponsiblePerson() != null)
                        .collect(Collectors.groupingBy(InjuryCaseDTO::getResponsiblePerson));
                //校验责任人是否为合法的责任人
                List<ManagerDTO> managerDTOS = managerAclService.selectByUsernameList(new ArrayList<>(target.keySet()));
                Set<String> effectiveList = managerDTOS.stream().map(ManagerDTO::getUserName).collect(Collectors.toSet());
                for (Map.Entry<String, List<InjuryCaseDTO>> entry : target.entrySet()) {
                    if (!effectiveList.contains(entry.getKey())) {
                        continue;
                    }
                    List<String> caseNumberList = entry.getValue()
                            .stream()
                            .map(InjuryCaseDTO::getCaseNumber)
                            .filter(Objects::nonNull)
                            .map(String::trim)
                            .collect(Collectors.toList());
                    List<CaseInfo> caseInfoByCaseNumber = getCaseInfoByCaseNumber(caseNumberList);
                    if (!caseInfoByCaseNumber.isEmpty()) { // 避免空集合更新
                        List<CaseInfo> targetCaseInfo = caseInfoByCaseNumber.stream().map(x -> {
                            CaseInfo update = new CaseInfo();
                            update.setId(x.getId());
                            update.setResponsiblePerson(entry.getKey());
                            return update;
                        }).collect(Collectors.toList());
                        updateBatchById(targetCaseInfo);
                        logOperationInfo(entry.getKey(), caseInfoByCaseNumber);
                    }
                }
            }, executor);
        }
    }

    private void logOperationInfo(String responsiblePerson, List<CaseInfo> caseInfoByCaseNumber) {
        for (CaseInfo old : caseInfoByCaseNumber) {
            CaseInfo update = injuryEntityDTOConverter.caseInfoCopy(old);
            update.setResponsiblePerson(responsiblePerson);
            CaseLogUtil.saveCaseStatusLog(OperateTypeConstant.BATCH_ASSIGN_RESPONSIBLE_PERSON, "批量改派责任人", old, update);
        }
    }

    @Override
    public void convertQueryAuth(CaseRequest caseRequest) {
        String userName = UserContextUtil.getManager().getUserName();
        List<RoleDTO> roles = UserContextUtil.getRoles();
        //申报岗只能查询自己的
        boolean isAdmin = !ObjectUtils.isEmpty(roles) && roles.stream().anyMatch(x -> "RENSHEYWGLY".equals(x.getCode()));

        if (!isAdmin && ObjectUtils.nullSafeEquals(caseRequest.getComeFrom(), 1) || caseRequest.getComeFrom() == null) {
            caseRequest.setResponsiblePerson(userName);
        }
        //待认领页面不限制责任人
        if (caseRequest.getStatus() == 9) {
            caseRequest.setResponsiblePerson(null);
        }

        String caseStatus = "";

        AssertUtil.check(ObjectUtils.isEmpty(caseRequest.getStatus()), "Illegal parameter");
        if (caseRequest.getStatus() != null) {
            caseStatus = convertStatus(caseRequest.getStatus());
            //审核岗只有当传递值时需要查询自己的
            if (isAdmin) {
                //ignore
            } else if (ObjectUtils.nullSafeEquals(caseRequest.getComeFrom(), 2) && (
                    CaseStatus.DECLARED.eqCode(caseStatus)
                            || CaseStatus.OCCUPATIONAL_INJURY_CONFIRMED.eqCode(caseStatus)
                            || CaseStatus.OCCUPATIONAL_INJURY_DENIED.eqCode(caseStatus))) {
                caseRequest.setReviewer(userName);
            }
            //当status!=0时，说明用户选择了其它的label eg：待审核等
            if (caseRequest.getStatus() != 0) {
                caseRequest.setCaseStatus(caseStatus);
            }
        }
    }

    public List<CaseInfo> getCaseInfoByCaseNumber(List<String> caseNumberList) {
        if (ObjectUtils.isEmpty(caseNumberList)) {
            return Collections.emptyList();
        }
        List<String> targetCaseNumber = caseNumberList.stream().filter(StringUtils::isNotBlank).map(String::trim).distinct().collect(Collectors.toList());
        SearchSourceBuilder builder = SearchSourceBuilder.searchSource().query(QueryBuilders.boolQuery().filter(QueryBuilders.termsQuery("caseNumber.keyword", targetCaseNumber)));
        builder.size(2000);
        SearchRequest searchRequest = new SearchRequest(ElasticIndexName.CASE_INFO);
        searchRequest.source(builder);
        try {
            SearchResponse search = esClientFactory.getClient().search(searchRequest, RequestOptions.DEFAULT);
            SearchHit[] hits = search.getHits().getHits();
            List<CaseInfoVo> result = Arrays.stream(hits).map(x -> JsonTool.json2Obj(x.getSourceAsString(), CaseInfoVo.class)).collect(Collectors.toList());
            return injuryEntityDTOConverter.vo2CaseInfoEntityList(result);
        } catch (Exception e) {
            log.error("getCaseInfoByCaseNumber", e);
            return Collections.emptyList();
        }

    }

    @Override
    public String convertStatus(Integer status) {
        switch (status) {
            case 1:
                return CaseStatus.PENDING.getCode();
            case 2:
                return CaseStatus.DECLARING.getCode();
            case 3:
                return CaseStatus.DECLARED.getCode();
            case 4:
                return CaseStatus.AWAITING_SUPPLEMENTARY_MATERIALS.getCode();
            case 5:
                return CaseStatus.OCCUPATIONAL_INJURY_CONFIRMED.getCode();
            case 6:
                return CaseStatus.OCCUPATIONAL_INJURY_DENIED.getCode();
            case 7:
                return CaseStatus.WITHDRAWN.getCode();
            case 8:
                return CaseStatus.SUSPENDED.getCode();
            case 9:
                return CaseStatus.UNCLAIMED.getCode();
            default:
                return null;
        }
    }

    @Override
    public void esCaseCommonQuery(CaseRequest caseRequest, BoolQueryBuilder boolBuilder) {
        // 备案号精确匹配
        if (StringUtils.isNotBlank(caseRequest.getCaseNumber())) {
            boolBuilder.must(QueryBuilders.termQuery("caseNumber.keyword", caseRequest.getCaseNumber()));
        }

        // 骑手姓名精确匹配
        if (StringUtils.isNotBlank(caseRequest.getRiderName())) {
            boolBuilder.must(QueryBuilders.termQuery("riderName.keyword", caseRequest.getRiderName()));
        }

        // 骑手身份证号精确匹配
        if (StringUtils.isNotBlank(caseRequest.getRiderIdCard())) {
            boolBuilder.must(QueryBuilders.termQuery("riderIdCard.keyword", caseRequest.getRiderIdCard()));
        }

        // 省份精确匹配
        if (StringUtils.isNotBlank(caseRequest.getAccidentProvince())) {
            boolBuilder.must(QueryBuilders.termQuery("accidentProvince.keyword", caseRequest.getAccidentProvince()));
        }

        // 城市精确匹配
        if (StringUtils.isNotBlank(caseRequest.getAccidentCity())) {
            boolBuilder.must(QueryBuilders.termQuery("accidentCity.keyword", caseRequest.getAccidentCity()));
        }

        // 责任人精确匹配
        if (StringUtils.isNotBlank(caseRequest.getResponsiblePerson())) {
            boolBuilder.must(QueryBuilders.termQuery("responsiblePerson.keyword", caseRequest.getResponsiblePerson()));
        }

        // 审核人精确匹配
        if (StringUtils.isNotBlank(caseRequest.getReviewer())) {
            boolBuilder.must(QueryBuilders.termQuery("reviewer.keyword", caseRequest.getReviewer()));
        }

        // 事故标签多条件精确匹配（AND关系）
        if (StringUtils.isNotBlank(caseRequest.getAccidentTags())) {
            // 拆分多个标签
            String[] tags = caseRequest.getAccidentTags().split(",");
            // 创建子查询构建器
            BoolQueryBuilder tagsQuery = QueryBuilders.boolQuery();
            for (String tag : tags) {
                String trimmedTag = tag.trim();
                if (!trimmedTag.isEmpty()) {
                    // 使用match_phrase查询实现模糊匹配（作为整体短语匹配）
                    tagsQuery.should(QueryBuilders.wildcardQuery("accidentTags.keyword", "*" + trimmedTag + "*"));
                }
            }
            // 设置至少匹配一个标签
            tagsQuery.minimumShouldMatch(1);
            // 添加到主查询
            boolBuilder.must(tagsQuery);
        }

        // 案件状态与多个状态精确匹配
        if (caseRequest.getStatus() != null && caseRequest.getStatus() == 9) {
            // 查询caseStatus为0的案件，增强判断
            boolBuilder.must(QueryBuilders.termQuery("caseStatus.keyword", "0"));
        } else if (StringUtils.isNotBlank(caseRequest.getCaseStatus())) {
            // 拆分为多个状态值
            String[] statuses = caseRequest.getCaseStatus().split(",");
            // 创建子查询构建器（OR关系）
            BoolQueryBuilder statusQuery = QueryBuilders.boolQuery();

            for (String status : statuses) {
                String trimmedStatus = status.trim();
                if (!trimmedStatus.isEmpty()) {
                    // 每个状态都是一个should条件（OR关系）
                    statusQuery.should(QueryBuilders.wildcardQuery("caseStatus.keyword", "*" + trimmedStatus + "*"));
                }
            }
            // 设置至少匹配一个状态
            statusQuery.minimumShouldMatch(1);
            // 添加到主查询
            boolBuilder.must(statusQuery);
        }

        // 风神案件状态
        if (StringUtils.isNotBlank(caseRequest.getExternalState())) {
            // 拆分多个标签
            String[] states = caseRequest.getExternalState().split(",");
            // 创建子查询构建器
            BoolQueryBuilder statesQuery = QueryBuilders.boolQuery();
            for (String state : states) {
                String trimmedState = state.trim();
                if (!trimmedState.isEmpty()) {
                    // 每个状态都是一个should条件（OR关系）
                    statesQuery.should(QueryBuilders.wildcardQuery("externalState.keyword", "*" + trimmedState + "*"));
                }
            }
            // 设置至少匹配一个状态
            statesQuery.minimumShouldMatch(1);
            // 添加到主查询
            boolBuilder.must(statesQuery);
        }

        // 创建开始时间范围查询
        if (StringUtils.isNotBlank(caseRequest.getStartCreateTime())) {
            boolBuilder.must(QueryBuilders.rangeQuery("createTime").gte(DateUtils.formatZonedDateTime(caseRequest.getStartCreateTime() + " 00:00:00")));
        }
        // 创建结束时间范围查询
        if (StringUtils.isNotBlank(caseRequest.getEndCreateTime())) {
            boolBuilder.must(QueryBuilders.rangeQuery("createTime").lte(DateUtils.formatZonedDateTime(caseRequest.getEndCreateTime() + " 23:59:59")));
        }

        // 新增超时状态筛选
        if (caseRequest.getTimeoutStatus() != null && caseRequest.getTimeoutStatus() != 0) {
            boolBuilder.must(buildTimeoutStatusQuery(caseRequest.getTimeoutStatus()));
        }
    }
}