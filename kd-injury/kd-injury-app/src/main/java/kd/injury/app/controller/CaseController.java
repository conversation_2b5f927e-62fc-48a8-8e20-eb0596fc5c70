package kd.injury.app.controller;

import kd.injury.app.model.dto.*;
import kd.injury.app.model.req.CaseAssignmentRequest;
import kd.injury.app.model.req.CaseRequest;
import kd.injury.app.model.vo.CaseInfoVo;
import kd.injury.app.security.UserContextUtil;
import kd.injury.app.service.CaseInfoService;
import kd.injury.app.util.ExcelUtil;
import kd.injury.common.util.AssertUtil;
import kd.injury.common.util.JsonTool;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@RequestMapping("/api/cases")
@RestController
@RequiredArgsConstructor
public class CaseController {

    private final CaseInfoService caseInfoService;

    @Value("${case.access-sync-token:8CEuBL7uLiIyhALYCHcKZkduyb5Jw2i7}")
    public String accessSyncToken;


    @PostMapping(value = "/createOrUpdateInjuryCase")
    public ResponseResult<InjuryCaseResponseDTO> createCase(@RequestPart("json") String json,
                                                            @RequestPart(value = "files", required = false) MultipartFile[] files) {
        InjuryCaseDTO body = JsonTool.json2Obj(json, InjuryCaseDTO.class);
        AssertUtil.check(ObjectUtils.isEmpty(body), "Serialization error");
        String userName = UserContextUtil.getManager().getUserName();
        body.setLoginUser(userName);
        body.setFilesArray(files);
        InjuryCaseResponseDTO createdCase = caseInfoService.createOrUpdateInjuryCase(body);
        return ResponseResult.success(createdCase);
    }

    /**
     * 上传人伤案件文件
     *
     * @param attachmentType 附件类型{@link kd.injury.common.constants.AttachmentTypeEnum}
     * @param files          文件
     */
    @PostMapping(value = "/uploadInjuryMaterial")
    public ResponseResult<InjuryCaseResponseDTO> uploadInjuryMaterial(@RequestPart("caseId") String caseId,
                                                                      @RequestPart("attachmentType") String attachmentType,
                                                                      @RequestPart("files") MultipartFile[] files) {
        String userName = UserContextUtil.getManager().getUserName();
        InjuryCaseDTO body = new InjuryCaseDTO();
        body.setLoginUser(userName);
        body.setFilesArray(files);
        body.setId(caseId);
        body.setAttachmentType(attachmentType);
        InjuryCaseResponseDTO uploadInjuryMaterial = caseInfoService.uploadInjuryMaterial(body);
        return ResponseResult.success(uploadInjuryMaterial);
    }

    /**
     * 下载人伤案件文件
     */
    @PostMapping(value = "/downloadInjuryMaterial")
    public ResponseResult<InjuryCaseResponseDTO> downloadInjuryMaterial(@RequestBody InjuryCaseDTO body) {
        String userName = UserContextUtil.getManager().getUserName();
        body.setLoginUser(userName);
        InjuryCaseResponseDTO uploadInjuryMaterial = caseInfoService.downloadInjuryMaterial(body);
        return ResponseResult.success(uploadInjuryMaterial);
    }

    /**
     * 删除人伤案件文件
     */
    @PostMapping(value = "/deleteInjuryMaterial")
    public ResponseResult<InjuryCaseResponseDTO> deleteInjuryMaterial(@RequestBody InjuryCaseDTO body) {
        String userName = UserContextUtil.getManager().getUserName();
        body.setLoginUser(userName);
        InjuryCaseResponseDTO uploadInjuryMaterial = caseInfoService.deleteInjuryMaterial(body);
        return ResponseResult.success(uploadInjuryMaterial);
    }

    /**
     * 代理下载文件接口
     * 用于解决CORS问题，通过后端代理下载文件
     */
    @GetMapping(value = "/proxyDownload")
    public ResponseResult<String> proxyDownload(@RequestParam("url") String url) {
        try {
            // 创建URL连接
            java.net.URL fileUrl = new java.net.URL(url);
            java.net.HttpURLConnection connection = (java.net.HttpURLConnection) fileUrl.openConnection();

            // 设置请求头
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(30000);
            connection.setReadTimeout(30000);

            // 获取响应
            int responseCode = connection.getResponseCode();
            if (responseCode == 200) {
                // 读取文件内容
                try (java.io.InputStream inputStream = connection.getInputStream();
                     java.io.ByteArrayOutputStream outputStream = new java.io.ByteArrayOutputStream()) {
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }

                    // 将文件内容转换为Base64编码
                    String base64Content = java.util.Base64.getEncoder().encodeToString(outputStream.toByteArray());
                    return ResponseResult.success(base64Content);
                }
            } else {
                return ResponseResult.error(500, "Failed to download file, response code: " + responseCode);
            }
        } catch (Exception e) {
            return ResponseResult.error(500, "Proxy download failed: " + e.getMessage());
        }
    }


    /**
     * 获取案件详情
     *
     * @param id 案件ID
     * @return 案件详情
     */
    @GetMapping("/{id}")
    public ResponseResult<InjuryCaseDTO> getCaseDetail(@PathVariable String id) {
        InjuryCaseDTO caseDetail = caseInfoService.getCaseDetailById(id);
        return ResponseResult.success(caseDetail);
    }

    /**
     * 更新案件分配
     *
     * @param request
     * @return
     */
    @PostMapping("/update/assignment")
    public ResponseResult<String> updateAssignment(
            @RequestBody CaseAssignmentRequest request) {
        return ResponseResult.success(caseInfoService.updateAssignment(request));
    }

    /**
     * 根据人员ID查询其角色下的所有管理人员
     *
     * @param
     * @return 管理人员列表
     */
    @GetMapping("/same/role/managers/{comeFrom}")
    public ResponseResult<List<ManagerDTO>> getSameRoleManagers(@PathVariable("comeFrom") Integer comeFrom) {
        String userId = UserContextUtil.getManager().getId();
        List<ManagerDTO> managers = caseInfoService.getManagersBySameRole(comeFrom, userId);
        return ResponseResult.success(managers);
    }

    /**
     * 查询案件 分页
     *
     * @param caseRequest
     * @return
     */
    @PostMapping("/search")
    public ResponseResult<PageDTO<CaseInfoVo>> searchCases(@RequestBody CaseRequest caseRequest) {
        return ResponseResult.success(caseInfoService.getCasePage(caseRequest));
    }

    /**
     * 同步爬虫数据接口
     *
     * @param injuryCaseDTOS  同步的参数
     * @param accessSyncToken 访问请求头
     */
    @PostMapping("/syncExternalCase")
    public ResponseResult<Boolean> syncExternalCase(@RequestBody InjuryCaseDTO injuryCaseDTOS, @RequestHeader("Access-Sync-Token") String accessSyncToken) {
        AssertUtil.check(!this.accessSyncToken.equals(accessSyncToken), "No operation permission");
        if(StringUtils.isEmpty(injuryCaseDTOS.getReporterName()) || StringUtils.isNotEmpty(injuryCaseDTOS.getNoWaybillScenarioType()) || "undefined".equals(injuryCaseDTOS.getNoWaybillScenarioType())){
            return ResponseResult.error(500,"缺少参数!");
        }
        return ResponseResult.success(caseInfoService.syncExternalCase(injuryCaseDTOS));
    }

    /**
     * 校验案件是否存在
     *
     * @param caseNumber 案件号
     * @return
     */
    @GetMapping("/verifyCaseExist/{caseNumber}")
    public ResponseResult<Boolean> verifyCaseExist(@PathVariable("caseNumber") String caseNumber, @RequestHeader("Access-Sync-Token") String accessSyncToken) throws IOException {
        AssertUtil.check(!this.accessSyncToken.equals(accessSyncToken), "No operation permission");
        return ResponseResult.success(caseInfoService.verifyCaseExist(caseNumber));
    }

    /**
     * 更新责任人接口
     *
     * @param caseId
     * @return
     */
    @PutMapping("/updatePersonResponsible")
    public ResponseResult<Boolean> updatePersonResponsible(@RequestParam("caseId") String caseId) {
        String username = UserContextUtil.getManager().getUserName();
        Boolean flag = caseInfoService.updatePersonResponsible(username, caseId);
        AssertUtil.check(!flag, "更新责任人失败");
        return ResponseResult.success(true);
    }


    /**
     * 批量更新责任人
     */
    @PostMapping("/updatePersonResponsibleByCaseNumber")
    public ResponseResult<Void> updatePersonResponsibleByCaseNumber(@RequestPart("file") MultipartFile file) throws IOException {
        Collection<InjuryCaseDTO> caseDTOS = ExcelUtil.importExcel(file.getBytes(), "1=caseNumber=案件号,2=responsiblePerson=责任人", InjuryCaseDTO.class);
        caseInfoService.updatePersonResponsibleByCaseNumber(ObjectUtils.isEmpty(caseDTOS) ? new ArrayList<>(0) : new ArrayList<>(caseDTOS));
        return ResponseResult.success();
    }
}
