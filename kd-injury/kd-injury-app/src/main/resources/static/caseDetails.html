<!DOCTYPE html>
<html lang="zh-CN">

    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>案件详情</title>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
        <style>
            :root {
                --primary-color: #2c7be5;
                --secondary-color: #6b9bc3;
                --accent-color: #f7ba1e;
                --success-color: #00b42a;
                --danger-color: #f53f3f;
                --warning-color: #ff7d00;
                --info-color: #86909c;
                --bg-color: #f5f7fa;
                --card-bg: #ffffff;
                --border-color: #e5e6eb;
                --disabled-color: #f2f3f5;
                --text-color: #1d2129;
                --text-secondary: #4e5969;
                --text-disabled: #86909c;
            }

            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
                background-color: var(--bg-color);
                color: var(--text-color);
                line-height: 1.5;
                padding: 16px;
            }

            .container {
                max-width: 1200px;
                margin: 0 auto;
                background-color: var(--card-bg);
                border-radius: 8px;
                box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
                overflow: hidden;
            }

            h1 {
                text-align: center;
                padding: 24px 0;
                margin-bottom: 8px; /* 减少标题与下方元素的间距 */
                color: var(--primary-color);
                border-bottom: 1px solid var(--border-color);
            }

            /* 新增：案件状态标签样式 */
            .case-status-container {
                display: flex;
                justify-content: center;
                gap: 12px;
                padding: 12px 0;
                background-color: var(--bg-color);
            }

            .case-status-tag {
                padding: 6px 12px;
                border-radius: 16px;
                font-size: 14px;
                font-weight: 500;
                display: flex;
                align-items: center;
                gap: 6px;
            }

            .case-status-tag i {
                font-size: 12px;
            }

            .status-primary {
                background-color: rgba(44, 123, 229, 0.1);
                color: var(--primary-color);
                border: 1px solid var(--primary-color);
            }

            .status-secondary {
                background-color: rgba(107, 155, 195, 0.1);
                color: var(--secondary-color);
                border: 1px solid var(--secondary-color);
            }

            .status-accent {
                background-color: rgba(247, 186, 30, 0.1);
                color: var(--accent-color);
                border: 1px solid var(--accent-color);
            }

            .status-success {
                background-color: rgba(0, 180, 42, 0.1);
                color: var(--success-color);
                border: 1px solid var(--success-color);
            }

            .status-danger {
                background-color: rgba(245, 63, 63, 0.1);
                color: var(--danger-color);
                border: 1px solid var(--danger-color);
            }

            .status-warning {
                background-color: rgba(255, 125, 0, 0.1);
                color: var(--warning-color);
                border: 1px solid var(--warning-color);
            }

            .status-info {
                background-color: rgba(134, 144, 156, 0.1);
                color: var(--info-color);
                border: 1px solid var(--info-color);
            }

            .error {
                border-color: var(--danger-color) !important;
            }

            .error-message {
                color: var(--danger-color);
                font-size: 12px;
                margin-top: 4px;
            }

            .action-bar {
                padding: 16px 24px;
                display: flex;
                justify-content: flex-end;
                gap: 12px;
                border-bottom: 1px solid var(--border-color);
            }

            button {
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
                font-weight: 500;
                transition: all 0.2s ease;
            }

            button:hover {
                opacity: 0.9;
            }

            button.primary {
                background-color: var(--primary-color);
                color: white;
            }

            button.secondary {
                background-color: var(--bg-color);
                color: var(--text-color);
            }

            button.danger {
                background-color: var(--danger-color);
                color: white;
            }

            .info-container {
                display: flex;
                flex-wrap: wrap;
                gap: 24px;
                padding: 24px;
            }

            .info-section {
                flex: 1 1 calc(50% - 12px);
                background-color: var(--card-bg);
                border-radius: 8px;
                border: 1px solid var(--border-color);
                overflow: hidden;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            }

            .info-section h2 {
                padding: 16px 24px;
                background-color: var(--bg-color);
                color: var(--primary-color);
                font-size: 16px;
                font-weight: 600;
                border-bottom: 1px solid var(--border-color);
            }

            .info-content {
                padding: 24px;
            }

            .form-group {
                margin-bottom: 16px;
            }

            label {
                display: block;
                margin-bottom: 8px;
                font-size: 14px;
                color: var(--text-color);
                font-weight: 500;
            }

            label.required::after {
                content: " *";
                color: var(--danger-color);
            }

            input,
            select,
            textarea {
                width: 100%;
                padding: 10px 12px;
                border: 1px solid var(--border-color);
                border-radius: 4px;
                font-size: 14px;
                transition: border-color 0.2s ease;
            }

            /* 时间选择器特殊样式 - 支持秒级选择 */
            /* 功能特性：
             * 1. 支持选择到秒级别 (step="1")
             * 2. 使用等宽字体显示，便于阅读
             * 3. 自动设置当前时间作为默认值
             * 4. 保持原生功能不受影响
             */
            input[type="datetime-local"] {
                min-width: 200px;
                font-family: 'Courier New', monospace;
                letter-spacing: 0.5px;
            }

            table {
                width: 100%;
                border-collapse: collapse;
            }

            th,
            td {
                padding: 10px 12px;
                border: 1px solid var(--border-color);
                text-align: center;
            }

            /* 新增：案件备注表格样式，解决内容超长错位问题 */
            .info-section h2+.info-content table {
                margin-top: 16px;
            }

            .info-section h2+.info-content table th,
            .info-section h2+.info-content table td {
                padding: 12px;
                vertical-align: top;
                word-break: break-word;
                white-space: normal;
            }

            .info-section h2+.info-content table th {
                background-color: var(--bg-color);
                font-weight: 600;
            }

            .info-section h2+.info-content table td.remark-content {
                max-width: 300px;
                min-width: 200px;
                overflow-wrap: break-word;
                text-align: left;
            }

            tr:hover {
                background-color: var(--bg-color);
            }

            .attachment-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 10px 12px;
                margin-bottom: 8px;
                border: 1px dashed var(--border-color);
                border-radius: 4px;
                background-color: var(--bg-color);
            }

            .attachment-item span {
                flex-grow: 1;
                margin-right: 8px;
                font-size: 14px;
            }

            .attachment-actions button {
                padding: 4px 8px;
                font-size: 12px;
            }

            .readonly-field {
                background-color: var(--card-bg);
                border: none;
                padding: 10px 12px;
                font-size: 14px;
                color: var(--text-color);
                min-height: 36px;
            }

            .section-title {
                font-size: 16px;
                font-weight: 600;
                margin-bottom: 16px;
                color: var(--primary-color);
            }

            .editable-field {
                background-color: var(--bg-color);
            }

            .error {
                border-color: var(--danger-color) !important;
            }

            .error-message {
                color: var(--danger-color);
                font-size: 12px;
                margin-top: 4px;
            }

            /* 响应式设计 */
            @media (max-width: 768px) {
                .info-section {
                    flex: 1 1 100%;
                }

                .info-container {
                    padding: 16px;
                    gap: 16px;
                }

                .action-bar {
                    flex-direction: column;
                    gap: 8px;
                    padding: 12px;
                }

                /* 响应式：小屏幕下状态标签换行 */
                .case-status-container {
                    flex-direction: column;
                    align-items: center;
                    gap: 8px;
                }

                /* 弹窗响应式优化 */
                .custom-modal-content {
                    max-width: 95%;
                    margin: 20px;
                }

                .custom-modal-header {
                    padding: 20px 24px 0;
                }

                .custom-modal-body {
                    padding: 16px 24px 24px;
                    font-size: 14px;
                }

                .custom-modal-footer {
                    padding: 0 24px 24px;
                    flex-direction: column;
                    gap: 8px;
                }

                .custom-modal-btn {
                    width: 100%;
                    min-width: auto;
                }

                .remark-modal-content {
                    max-width: 95%;
                }

                .remark-textarea {
                    min-height: 120px;
                    padding: 12px;
                    font-size: 14px;
                }

                /* 文件上传组件响应式 */
                .file-upload-area {
                    padding: 16px;
                    gap: 12px;
                }

                .file-upload-icon {
                    font-size: 28px;
                }

                .file-upload-title {
                    font-size: 15px;
                }

                .file-upload-desc {
                    font-size: 12px;
                }

                .upload-actions {
                    flex-direction: column;
                    gap: 8px;
                }

                .upload-btn, .download-btn {
                    width: 100%;
                    min-width: auto;
                }

                /* 文件状态响应式 */
                .file-status-content {
                    flex-direction: column;
                    gap: 8px;
                }

                .file-status-actions {
                    justify-content: center;
                }

                .file-list-container {
                    max-height: 150px;
                }

                .file-item {
                    padding: 8px;
                }

                .file-name {
                    font-size: 11px;
                }

                .file-size {
                    font-size: 10px;
                }
            }

            @media (max-width: 480px) {
                .custom-modal-content {
                    max-width: 98%;
                    margin: 10px;
                }

                .custom-modal-title {
                    font-size: 18px;
                }

                .custom-modal-body {
                    padding: 12px 20px 20px;
                    font-size: 13px;
                }

                .custom-modal-footer {
                    padding: 0 20px 20px;
                }
            }

            .radio-group {
                display: flex;
                gap: 16px;
                /* 选项之间的间距 */
                align-items: center;
            }

            .radio-group label {
                display: flex;
                align-items: center;
                gap: 6px;
                /* 图标与文本之间的间距 */
                cursor: pointer;
            }

            .radio-group input[type="radio"] {
                width: auto;
                height: auto;
                margin: 0;
            }

            .tag-container {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
                padding: 8px;
                border: 1px solid var(--border-color);
                border-radius: 4px;
                background-color: var(--bg-color);
            }

            .tag-button {
                padding: 4px 12px;
                border: 1px solid var(--border-color);
                border-radius: 16px;
                background-color: white;
                color: var(--text-color);
                font-size: 12px;
                cursor: pointer;
                transition: all 0.2s ease;
            }

            .tag-button.selected {
                background-color: var(--primary-color);
                color: white;
                border-color: var(--primary-color);
            }

            .tag-button:hover:not(.selected) {
                background-color: var(--bg-color);
            }

            /* 预览模态框样式 */
            .preview-modal {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.7);
                z-index: 1000;
                justify-content: center;
                align-items: center;
            }

            .preview-content {
                background-color: white;
                border-radius: 8px;
                max-width: 90%;
                max-height: 90%;
                overflow: auto;
                position: relative;
            }

            .preview-header {
                padding: 12px 16px;
                border-bottom: 1px solid var(--border-color);
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .preview-header h3 {
                margin: 0;
                color: var(--primary-color);
            }

            .preview-close {
                background: none;
                border: none;
                font-size: 24px;
                cursor: pointer;
                color: var(--text-secondary);
            }

            .preview-body {
                padding: 16px;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .preview-image {
                max-width: 100%;
                max-height: 70vh;
                border-radius: 4px;
            }

            .preview-pdf {
                width: 100%;
                height: 70vh;
                border: none;
            }

            .unsupported-file {
                padding: 20px;
                text-align: center;
                color: var(--text-secondary);
            }

            /* 禁用按钮样式 */
            button:disabled {
                opacity: 0.5;
                cursor: not-allowed;
                background-color: var(--disabled-color) !important;
                color: var(--text-disabled) !important;
            }

            button:disabled:hover {
                opacity: 0.5;
                background-color: var(--disabled-color) !important;
            }

            /* 自定义弹窗样式 - 统一优化版本
             * 统一了页面中所有弹窗提示的样式，包括：
             * 1. 成功提示 (showSuccess)
             * 2. 错误提示 (showError) 
             * 3. 警告提示 (showWarning)
             * 4. 信息提示 (showInfo)
             * 5. 确认对话框 (showConfirm)
             * 6. 备注输入弹窗
             * 
             * 优化特性：
             * - 现代化设计风格
             * - 流畅的动画效果
             * - 响应式布局
             * - 统一的视觉语言
             * - 良好的用户体验
             */
            .custom-modal {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.6);
                z-index: 10000;
                justify-content: center;
                align-items: center;
                animation: fadeIn 0.3s ease;
                backdrop-filter: blur(4px);
            }

            .custom-modal.show {
                display: flex;
            }

            .custom-modal-content {
                background: white;
                border-radius: 16px;
                box-shadow: 0 25px 80px rgba(0, 0, 0, 0.25);
                max-width: 420px;
                width: 90%;
                position: relative;
                animation: slideIn 0.3s ease;
                overflow: hidden;
                border: 1px solid rgba(255, 255, 255, 0.1);
            }

            .custom-modal-header {
                padding: 24px 28px 0;
                display: flex;
                align-items: center;
                gap: 14px;
            }

            .custom-modal-icon {
                width: 28px;
                height: 28px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
                color: white;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }

            .custom-modal-icon.success {
                background: linear-gradient(135deg, #00b42a, #00d42a);
                box-shadow: 0 4px 12px rgba(0, 180, 42, 0.3);
            }

            .custom-modal-icon.error {
                background: linear-gradient(135deg, #f53f3f, #ff5f5f);
                box-shadow: 0 4px 12px rgba(245, 63, 63, 0.3);
            }

            .custom-modal-icon.warning {
                background: linear-gradient(135deg, #ff7d00, #ff9d00);
                box-shadow: 0 4px 12px rgba(255, 125, 0, 0.3);
            }

            .custom-modal-icon.info {
                background: linear-gradient(135deg, #2c7be5, #4c9be5);
                box-shadow: 0 4px 12px rgba(44, 123, 229, 0.3);
            }

            .custom-modal-title {
                font-size: 20px;
                font-weight: 600;
                color: var(--text-color);
                margin: 0;
                letter-spacing: -0.02em;
            }

            .custom-modal-body {
                padding: 20px 28px 28px;
                color: var(--text-secondary);
                line-height: 1.6;
                font-size: 15px;
            }

            .custom-modal-footer {
                padding: 0 28px 28px;
                display: flex;
                gap: 12px;
                justify-content: flex-end;
            }

            .custom-modal-btn {
                padding: 10px 20px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
                min-width: 90px;
                position: relative;
                overflow: hidden;
            }

            .custom-modal-btn::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                transition: left 0.5s;
            }

            .custom-modal-btn:hover::before {
                left: 100%;
            }

            .custom-modal-btn.primary {
                background: var(--primary-color);
                color: white;
                box-shadow: 0 4px 12px rgba(44, 123, 229, 0.3);
            }

            .custom-modal-btn.primary:hover {
                background: #1a6fd4;
                transform: translateY(-1px);
                box-shadow: 0 6px 16px rgba(44, 123, 229, 0.4);
            }

            .custom-modal-btn.secondary {
                background: var(--bg-color);
                color: var(--text-color);
                border: 1px solid var(--border-color);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }

            .custom-modal-btn.secondary:hover {
                background: #e8eaed;
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }

            .custom-modal-btn.danger {
                background: var(--danger-color);
                color: white;
                box-shadow: 0 4px 12px rgba(245, 63, 63, 0.3);
            }

            .custom-modal-btn.danger:hover {
                background: #d63333;
                transform: translateY(-1px);
                box-shadow: 0 6px 16px rgba(245, 63, 63, 0.4);
            }

            @keyframes fadeIn {
                from { 
                    opacity: 0; 
                    backdrop-filter: blur(0px);
                }
                to { 
                    opacity: 1; 
                    backdrop-filter: blur(4px);
                }
            }

            @keyframes slideIn {
                from { 
                    opacity: 0;
                    transform: translateY(-30px) scale(0.9);
                }
                to { 
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
            }

            @keyframes iconPulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.1); }
                100% { transform: scale(1); }
            }

            .custom-modal-icon {
                animation: iconPulse 0.6s ease-out;
            }

            /* 确认对话框样式 */
            .confirm-modal .custom-modal-content {
                max-width: 380px;
            }

            .confirm-modal .custom-modal-body {
                text-align: center;
                padding: 24px 28px;
                font-size: 16px;
                line-height: 1.5;
            }

            .confirm-modal .custom-modal-footer {
                justify-content: center;
                gap: 16px;
            }

            .confirm-modal .custom-modal-btn {
                min-width: 100px;
                font-weight: 600;
            }

            /* 备注弹窗样式 */
            .remark-modal-content {
                max-width: 520px;
                width: 90%;
            }

            .remark-input-container {
                position: relative;
                margin-bottom: 16px;
            }

            .remark-textarea {
                width: 100%;
                min-height: 140px;
                padding: 16px;
                border: 2px solid var(--border-color);
                border-radius: 12px;
                font-size: 15px;
                line-height: 1.6;
                font-family: inherit;
                resize: vertical;
                transition: all 0.3s ease;
                box-sizing: border-box;
                background: #fafbfc;
            }

            .remark-textarea:focus {
                outline: none;
                border-color: var(--primary-color);
                box-shadow: 0 0 0 4px rgba(44, 123, 229, 0.1);
                background: white;
            }

            .remark-textarea::placeholder {
                color: var(--text-secondary);
                opacity: 0.6;
            }

            .remark-counter {
                position: absolute;
                bottom: 12px;
                right: 12px;
                font-size: 13px;
                color: var(--text-secondary);
                background: rgba(255, 255, 255, 0.95);
                padding: 4px 8px;
                border-radius: 6px;
                pointer-events: none;
                backdrop-filter: blur(4px);
                border: 1px solid rgba(0, 0, 0, 0.05);
            }

            .remark-counter.warning {
                color: #ff7d00;
                background: rgba(255, 125, 0, 0.1);
            }

            .remark-counter.danger {
                color: #f53f3f;
                background: rgba(245, 63, 63, 0.1);
            }

            /* 文件上传组件样式 - 现代化优化版本
             * 优化特性：
             * 1. 现代化拖拽上传界面
             * 2. 直观的文件选择提示
             * 3. 实时文件状态显示
             * 4. 支持拖拽上传功能
             * 5. 响应式设计适配
             * 6. 流畅的动画效果
             * 7. 统一的操作按钮样式
             */
            .file-upload-container {
                margin-bottom: 16px;
            }

            .file-upload-area {
                display: flex;
                align-items: center;
                gap: 16px;
                padding: 20px;
                border: 2px dashed var(--border-color);
                border-radius: 12px;
                background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
                cursor: pointer;
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
                user-select: none;
            }

            .file-upload-area.drag-over {
                border-color: var(--primary-color);
                background: linear-gradient(135deg, #e8f4ff 0%, #ffffff 100%);
                transform: scale(1.02);
                box-shadow: 0 8px 25px rgba(44, 123, 229, 0.2);
            }

            .file-upload-area::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(44, 123, 229, 0.05), transparent);
                transition: left 0.6s;
            }

            .file-upload-area:hover::before {
                left: 100%;
            }

            .file-upload-area:hover {
                border-color: var(--primary-color);
                background: linear-gradient(135deg, #f0f7ff 0%, #ffffff 100%);
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(44, 123, 229, 0.15);
            }

            .file-upload-area:active {
                transform: translateY(0);
                box-shadow: 0 4px 15px rgba(44, 123, 229, 0.1);
            }

            .file-upload-icon {
                font-size: 32px;
                color: var(--primary-color);
                opacity: 0.8;
                transition: all 0.3s ease;
            }

            .file-upload-area:hover .file-upload-icon {
                opacity: 1;
                transform: scale(1.1);
            }

            .file-upload-text {
                flex: 1;
                display: flex;
                flex-direction: column;
                gap: 4px;
            }

            .file-upload-title {
                font-size: 16px;
                font-weight: 600;
                color: var(--text-color);
                margin: 0;
            }

            .file-upload-desc {
                font-size: 13px;
                color: var(--text-secondary);
                margin: 0;
            }

            .upload-actions {
                display: flex;
                gap: 12px;
                margin-top: 12px;
                flex-wrap: wrap;
            }

            .upload-btn, .download-btn {
                padding: 10px 20px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
                min-width: 120px;
            }

            .upload-btn {
                background: var(--primary-color);
                color: white;
                box-shadow: 0 4px 12px rgba(44, 123, 229, 0.3);
            }

            .upload-btn:hover {
                background: #1a6fd4;
                transform: translateY(-2px);
                box-shadow: 0 6px 16px rgba(44, 123, 229, 0.4);
            }

            .download-btn {
                background: var(--success-color);
                color: white;
                box-shadow: 0 4px 12px rgba(0, 180, 42, 0.3);
            }

            .download-btn:hover {
                background: #00a426;
                transform: translateY(-2px);
                box-shadow: 0 6px 16px rgba(0, 180, 42, 0.4);
            }

            .upload-btn::before, .download-btn::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                transition: left 0.5s;
            }

            .upload-btn:hover::before, .download-btn:hover::before {
                left: 100%;
            }

            /* 文件上传状态指示器 */
            .file-upload-status {
                margin-top: 8px;
                padding: 12px;
                border-radius: 8px;
                font-size: 13px;
                font-weight: 500;
            }

            .file-status-content {
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: 12px;
            }

            .file-status-info {
                display: flex;
                align-items: center;
                gap: 8px;
                flex: 1;
            }

            .file-status-icon {
                color: var(--success-color);
                font-weight: bold;
            }

            .file-status-text {
                color: var(--text-color);
                font-weight: 500;
            }

            .file-status-actions {
                display: flex;
                align-items: center;
            }

            .clear-files-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 24px;
                height: 24px;
                border: none;
                border-radius: 50%;
                background: rgba(245, 63, 63, 0.1);
                color: var(--danger-color);
                cursor: pointer;
                transition: all 0.2s ease;
                font-size: 12px;
                font-weight: bold;
            }

            .clear-files-btn:hover {
                background: var(--danger-color);
                color: white;
                transform: scale(1.1);
                box-shadow: 0 2px 8px rgba(245, 63, 63, 0.3);
            }

            .clear-files-btn:active {
                transform: scale(0.95);
            }

            .file-names-preview {
                margin-top: 6px;
                padding-top: 6px;
                border-top: 1px solid rgba(0, 0, 0, 0.05);
            }

            /* 文件列表样式 */
            .file-list-container {
                margin-top: 8px;
                padding: 8px;
                background: rgba(255, 255, 255, 0.8);
                border-radius: 6px;
                border: 1px solid rgba(0, 0, 0, 0.05);
                max-height: 200px;
                overflow-y: auto;
            }

            .file-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 6px 8px;
                margin-bottom: 4px;
                background: white;
                border-radius: 4px;
                border: 1px solid rgba(0, 0, 0, 0.05);
                transition: all 0.2s ease;
            }

            .file-item:hover {
                background: #f8f9fa;
                border-color: var(--primary-color);
            }

            .file-item:last-child {
                margin-bottom: 0;
            }

            .file-item-info {
                display: flex;
                align-items: center;
                gap: 8px;
                flex: 1;
                min-width: 0;
            }

            .file-icon {
                font-size: 14px;
                opacity: 0.7;
            }

            .file-name {
                font-size: 12px;
                color: var(--text-color);
                font-weight: 500;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                flex: 1;
            }

            .file-size {
                font-size: 11px;
                color: var(--text-secondary);
                white-space: nowrap;
            }

            .remove-file-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 20px;
                height: 20px;
                border: none;
                border-radius: 50%;
                background: rgba(245, 63, 63, 0.1);
                color: var(--danger-color);
                cursor: pointer;
                transition: all 0.2s ease;
                font-size: 12px;
                font-weight: bold;
                margin-left: 8px;
            }

            .remove-file-btn:hover {
                background: var(--danger-color);
                color: white;
                transform: scale(1.1);
            }

            .view-files-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 24px;
                height: 24px;
                border: none;
                border-radius: 50%;
                background: rgba(44, 123, 229, 0.1);
                color: var(--primary-color);
                cursor: pointer;
                transition: all 0.2s ease;
                font-size: 12px;
                margin-right: 4px;
            }

            .view-files-btn:hover {
                background: var(--primary-color);
                color: white;
                transform: scale(1.1);
                box-shadow: 0 2px 8px rgba(44, 123, 229, 0.3);
            }

            .file-upload-status.success {
                background: rgba(0, 180, 42, 0.1);
                color: var(--success-color);
                border: 1px solid rgba(0, 180, 42, 0.2);
            }

            .file-upload-status.error {
                background: rgba(245, 63, 63, 0.1);
                color: var(--danger-color);
                border: 1px solid rgba(245, 63, 63, 0.2);
            }

            .file-upload-status.warning {
                background: rgba(255, 125, 0, 0.1);
                color: var(--warning-color);
                border: 1px solid rgba(255, 125, 0, 0.2);
            }

            /* 备注表格样式优化 */
            .remark-table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 10px;
            }

            .remark-table th,
            .remark-table td {
                padding: 12px;
                text-align: left;
                border-bottom: 1px solid var(--border-color);
            }

            .remark-table th {
                background-color: var(--bg-color);
                font-weight: 600;
                color: var(--text-color);
            }

            .remark-table tr:hover {
                background-color: rgba(44, 123, 229, 0.05);
            }

            .remark-content {
                max-width: 300px;
                word-wrap: break-word;
                line-height: 1.4;
            }

            .remark-meta {
                font-size: 12px;
                color: var(--text-secondary);
            }

            /* 备注头部样式 */
            .remark-header {
                margin-bottom: 15px;
                display: flex;
                justify-content: flex-end;
            }

            .remark-header button {
                display: flex;
                align-items: center;
                padding: 8px 16px;
                font-weight: 500;
            }

            /* 区块头部样式 */
            .section-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 15px;
            }

            .section-header h2 {
                margin: 0;
            }

            .section-header button {
                display: flex;
                align-items: center;
                padding: 6px 12px;
                font-size: 14px;
                font-weight: 500;
            }

            /* 空备注状态 */
            .remark-empty {
                text-align: center;
                padding: 40px 20px;
                color: var(--text-secondary);
                font-style: italic;
            }

            .remark-empty-icon {
                font-size: 48px;
                margin-bottom: 10px;
                opacity: 0.5;
            }
        </style>
    </head>

    <body>
        <div class="container">
            <h1>案件详情</h1>
            <!-- 新增：案件状态标签容器 -->
            <div class="case-status-container" id="caseStatusContainer">
                <!-- 案件状态标签将通过JavaScript动态添加 -->
            </div>
            <div class="action-bar">
                模板类型：
                <select id="templateType" style="width: 400px;">
                    <option value="">请选择</option>
                </select>
                <button id="downloadTemplateFile" onclick="downloadTemplateFile()" class="primary">下载</button>
                <button id="submitButton" onclick="submitForm()" class="primary">提交</button>
            </div>
            <div class="info-container">
                <div class="info-section">
                    <h2>报案人信息</h2>
                    <div class="info-content">
                        <div class="form-group">
                            <label for="reporterName" class="required">报案人姓名：</label>
                            <input type="text" id="reporterName" placeholder="请输入">
                        </div>
                        <div class="form-group">
                            <label for="reporterMobile" class="required">报案人手机号：</label>
                            <input type="text" id="reporterMobile" placeholder="请输入">
                        </div>
                        <div class="form-group">
                            <label for="reportTime" class="required">报案时间：</label>
                            <input type="datetime-local" id="reportTime" step="1">
                        </div>
                    </div>
                    <h2>骑手信息</h2>
                    <div class="info-content">
                        <div class="form-group">
                            <label for="riderName">骑手姓名：</label>
                            <input type="text" id="riderName" placeholder="请输入">
                        </div>
                        <div class="form-group">
                            <label for="riderIdCard">骑手身份证号：</label>
                            <input type="text" id="riderIdCard" placeholder="请输入">
                            <div class="error-message" id="idCardError" style="display: none; color: red;"></div>
                        </div>
                        <div class="form-group">
                            <label for="riderMobile">骑手手机号：</label>
                            <input type="text" id="riderMobile" placeholder="请输入">
                        </div>
                        <div class="form-group">
                            <label for="riderType">骑手类型：</label>
                            <select id="riderType">
                                <option value="">请选择</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="hasWaybill">是否有运单：</label>
                            <div class="radio-group">
                                <label for="hasWaybillYes">
                                    <input type="radio" id="hasWaybillYes" name="hasWaybill" value="是">
                                    是
                                </label>
                                <label for="hasWaybillNo">
                                    <input type="radio" id="hasWaybillNo" name="hasWaybill" value="否">
                                    否
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="relatedWaybillNumber">关联运单号：</label>
                            <textarea id="relatedWaybillNumber" maxlength="500" placeholder="请输入（限500字）"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="orderAcceptTime">接单时间：</label>
                            <input type="datetime-local" id="orderAcceptTime" step="1">
                        </div>
                        <div class="form-group">
                            <label for="merchantAddress">商家地址：</label>
                            <input type="text" id="merchantAddress" placeholder="请输入">
                        </div>
                        <div class="form-group">
                            <label for="noWaybillScenarioType">无运单场景类型：</label>
                            <input type="text" id="noWaybillScenarioType" placeholder="请输入">
                        </div>
                    </div>
                    <h2>服务商信息</h2>
                    <div class="info-content">
                        <div class="form-group">
                            <label for="merchantType">商类型：</label>
                            <input type="text" id="merchantType" placeholder="请输入">
                        </div>
                        <div class="form-group">
                            <label for="merchantName">服务商名称：</label>
                            <input type="text" id="merchantName" placeholder="请输入">
                        </div>
                        <div class="form-group">
                            <label for="merchantId">商ID：</label>
                            <input type="text" id="merchantId" placeholder="请输入">
                        </div>
                        <div class="form-group">
                            <label for="siteCity">站点城市：</label>
                            <input type="text" id="siteCity" placeholder="请输入">
                        </div>
                        <div class="form-group">
                            <label for="siteName">站点名称：</label>
                            <input type="text" id="siteName" placeholder="请输入">
                        </div>
                    </div>
                    <h2>商联系人信息</h2>
                    <div class="info-content">
                        <div class="form-group">
                            <label for="contactName">联系人姓名：</label>
                            <input type="text" id="contactName" placeholder="请输入">
                        </div>
                        <div class="form-group">
                            <label for="contactPhone">联系人电话：</label>
                            <input type="text" id="contactPhone" placeholder="请输入">
                        </div>
                        <div class="form-group">
                            <label for="stationManagerName">站长姓名：</label>
                            <input type="text" id="stationManagerName" placeholder="请输入">
                        </div>
                        <div class="form-group">
                            <label for="stationManagerPhone">站长电话：</label>
                            <input type="text" id="stationManagerPhone" placeholder="请输入">
                        </div>
                    </div>
                    <h2>异地医疗机构信息</h2>
                    <div class="info-content">
                        <div class="form-group">
                            <label>是否异地医疗：</label>
                            <div class="radio-group">
                                <label for="isRemoteMedicalYes">
                                    <input type="radio" id="isRemoteMedicalYes" name="isRemoteMedical" value="1">
                                    是
                                </label>
                                <label for="isRemoteMedicalNo">
                                    <input type="radio" id="isRemoteMedicalNo" name="isRemoteMedical" value="0" checked>
                                    否
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="medicalInstitutionName">机构名称：</label>
                            <input type="text" id="medicalInstitutionName" placeholder="请输入">
                        </div>
                        <div class="form-group">
                            <label for="medicalInstitutionLevel">机构级别：</label>
                            <input type="text" id="medicalInstitutionLevel" placeholder="请输入">
                        </div>
                        <div class="form-group">
                            <label for="medicalInstitutionAddress">机构通讯地址：</label>
                            <input type="text" id="medicalInstitutionAddress" placeholder="请输入">
                        </div>
                        <div class="form-group">
                            <label for="medicalInstitutionContact">机构联系电话：</label>
                            <input type="text" id="medicalInstitutionContact" placeholder="请输入">
                        </div>
                        <div class="form-group">
                            <label for="platformEnterpriseOpinion">平台企业意见：</label>
                            <input type="text" id="platformEnterpriseOpinion" placeholder="请输入">
                        </div>
                    </div>

                </div>
                <div class="info-section">
                    <h2>案件信息</h2>
                    <div class="info-content">
                        <div class="form-group">
                            <label for="caseNumber" class="required">备案号：</label>
                            <input type="text" id="caseNumber" placeholder="请输入">
                        </div>
                        <div class="form-group">
                            <label for="caseServiceType">案件服务类型：</label>
                            <input type="text" id="caseServiceType" placeholder="请输入">
                        </div>
                        <div class="form-group">
                            <label for="accidentTime" class="required">事故时间：</label>
                            <input type="datetime-local" id="accidentTime" step="1">
                        </div>

                        <div class="form-group">
                            <label for="accidentProvince" class="required">事故省市区：</label>
                            <select id="accidentProvince" onchange="loadCities(true)">
                                <option value="">请选择</option>
                            </select>
                            <select id="accidentCity" onchange="loadDistricts()">
                                <option value="">请选择</option>
                            </select>
                            <select id="accidentDistrict">
                                <option value="">请选择</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="accidentAddress" class="required">事故详细地址：</label>
                            <input type="text" id="accidentAddress" placeholder="请输入">
                        </div>
                        <div class="form-group">
                            <label for="accidentType" class="required">事故类型：</label>
                            <select id="accidentType">
                                <option value="">请选择</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="accidentCause" class="required">事故原因：</label>
                            <select id="accidentCause">
                                <option value="">请选择</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="accidentProcess" class="required">事故经过：</label>
                            <textarea id="accidentProcess" maxlength="100" placeholder="请输入（限100字）"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="accidentMaterials">事故附件：</label>
                            <div class="file-upload-container">
                                <div class="file-upload-area" onclick="document.getElementById('accidentMaterials').click()">
                                    <div class="file-upload-icon">📁</div>
                                    <div class="file-upload-text">
                                        <span class="file-upload-title">点击选择文件</span>
                                        <span class="file-upload-desc">支持多文件上传</span>
                                    </div>
                                    <input type="file" id="accidentMaterials" multiple style="display: none;">
                                </div>
                                <button onclick="uploadAccidentMaterials()" class="upload-btn">添加文件</button>
                            </div>
                            <div id="accidentMaterialsContainer">
                                <!-- 事故附件将通过 JavaScript 动态添加 -->
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="injuredParts">受伤部位：</label>
                            <input type="text" id="injuredParts" maxlength="50" placeholder="请输入（限50字）">
                        </div>
                        <div class="form-group">
                            <label for="diagnosisContent">诊断内容：</label>
                            <textarea id="diagnosisContent" maxlength="100" placeholder="请输入（限100字）"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="accidentTags">事故标签：</label>
                            <div id="accidentTagsContainer" class="tag-container">
                                <!-- 标签按钮将通过JavaScript动态添加 -->
                            </div>
                            <input type="hidden" id="accidentTags" name="accidentTags" value="">
                            <!-- 修改错误提示文本，从"请选择至少一个"改为提示性文本 -->
                            <div class="error-message" id="accidentTagsError" style="display: none;">
                                事故标签为选填项，可选择多个标签以便分类
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="declarationTips">申报剩余时间：</label>
                            <input type="text" id="declarationTips" readonly class="readonly-field">
                        </div>
                    </div>
                </div>
                <div class="info-section">
                    <h2>其它信息</h2>
                    <div class="info-content">
                        <div class="form-group">
                            <label>认领截图:</label>
                            <div id="screenshotMaterialsContainer">
                                <!-- 识别截图将通过 JavaScript 动态添加 -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="info-section">
                    <h2>首访信息</h2>
                    <div class="info-content">
                        <div class="form-group">
                            <label for="firstVisitTime">首访时间：</label>
                            <input type="datetime-local" id="firstVisitTime" step="1" disabled class="readonly-field">
                        </div>
                        <div class="form-group">
                            <label for="firstVisitRemark">首访备注：</label>
                            <textarea id="firstVisitRemark" disabled class="readonly-field"
                                      placeholder="请输入"></textarea>
                        </div>
                    </div>
                </div>
                <div class="info-section">
                    <h2>撤案信息</h2>
                    <div class="info-content">
                        <div class="form-group">
                            <label for="caseClosedTime">撤案时间：</label>
                            <input type="datetime-local" id="caseClosedTime" step="1" disabled class="readonly-field">
                        </div>
                        <div class="form-group">
                            <label for="caseClosedRemark">撤案原因：</label>
                            <textarea id="caseClosedRemark" disabled class="readonly-field"
                                      placeholder="请输入"></textarea>
                        </div>
                    </div>
                </div>
                <div class="info-section">
                    <h2>材料收集</h2>
                    <div class="info-content">
                        <div class="file-upload-container">
                            <div class="file-upload-area" onclick="document.getElementById('materialFiles').click()">
                                <div class="file-upload-icon">📄</div>
                                <div class="file-upload-text">
                                    <span class="file-upload-title">点击选择案件材料</span>
                                    <span class="file-upload-desc">支持多文件上传</span>
                                </div>
                                <input type="file" id="materialFiles" multiple style="display: none;">
                            </div>
                            <div class="upload-actions">
                                <button onclick="uploadMaterials()" class="upload-btn">上传文件</button>
                                <button onclick="downloadAllMaterials()" class="download-btn">批量下载</button>
                            </div>
                        </div>
                        <table>
                            <thead>
                                <tr>
                                    <th>文件名称</th>
                                    <th>文件类型</th>
                                    <th>文件大小(KB)</th>
                                    <th>上传人</th>
                                    <th>上传时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="materialTableBody">
                                <!-- 案件附件将通过 JavaScript 动态添加 -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="info-section">
                    <div class="section-header">
                        <h2>案件备注</h2>
                        <button onclick="addRemark()" class="primary">
                            <span style="margin-right: 5px;">+</span>添加备注
                        </button>
                    </div>
                    <div class="info-content">
                        <table class="remark-table">
                            <thead>
                                <tr>
                                    <th style="width: 50%;">备注内容</th>
                                    <th style="width: 20%;">填写人</th>
                                    <th style="width: 30%;">填写时间</th>
                                </tr>
                            </thead>
                            <tbody id="remarkTableBody">
                                <!-- 备注信息将通过 JavaScript 动态添加 -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="info-section">
                    <h2>退回补材</h2>
                    <div class="info-content">
                        <div class="form-group">
                            <label for="returnTime">退回日期：</label>
                            <input type="date" id="returnTime" disabled class="readonly-field">
                        </div>
                        <div class="form-group">
                            <label>补材告知书：</label>
                            <div id="supplementMaterialContainer">
                                <!-- 补材告知书附件将通过 JavaScript 动态添加 -->
                            </div>
                            <!--                            <input type="file" id="supplementMaterialFile">-->
                            <!--                            <button onclick="uploadSupplementMaterial()" class="secondary">上传</button>-->
                        </div>
                    </div>
                </div>
                <div class="info-section">
                    <h2>职伤认定</h2>
                    <div class="info-content">
                        <div class="form-group">
                            <label>认定是否成功：</label>
                            <div class="radio-group">
                                <label for="determineSuccessYes">
                                    <input type="radio" id="determineSuccessYes" name="determineSuccess" value="1"
                                           disabled>
                                    是
                                </label>
                                <label for="determineSuccessNo">
                                    <input type="radio" id="determineSuccessNo" name="determineSuccess" value="0"
                                           disabled>
                                    否
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="determineTime">认定日期：</label>
                            <input type="date" id="determineTime" disabled class="readonly-field">
                        </div>
                        <div class="form-group">
                            <label for="determineFailureRemark">认定失败原因：</label>
                            <textarea id="determineFailureRemark" disabled class="readonly-field"
                                      placeholder="请输入"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="distributionAmount">待遇发放金额：</label>
                            <input type="text" id="distributionAmount" disabled class="readonly-field">
                            <button onclick="updateDistributionAmount()" class="secondary">修改</button>
                        </div>
                        <div class="form-group">
                            <label>职伤结论书：</label>
                            <div id="injuryConclusionContainer">
                                <!-- 职伤结论书附件将通过 JavaScript 动态添加 -->
                            </div>
                            <div class="file-upload-container">
                                <div class="file-upload-area" onclick="document.getElementById('injuryConclusionFile').click()">
                                    <div class="file-upload-icon">📋</div>
                                    <div class="file-upload-text">
                                        <span class="file-upload-title">点击选择职伤结论书</span>
                                        <span class="file-upload-desc">支持PDF、Word等格式</span>
                                    </div>
                                    <input type="file" id="injuryConclusionFile" style="display: none;">
                                </div>
                                <button onclick="uploadInjuryConclusion()" class="upload-btn">上传文件</button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>伤残鉴定书：</label>
                            <div id="disabilityAssessmentContainer">
                                <!-- 伤残鉴定书附件将通过 JavaScript 动态添加 -->
                            </div>
                            <div class="file-upload-container">
                                <div class="file-upload-area" onclick="document.getElementById('disabilityAssessmentFile').click()">
                                    <div class="file-upload-icon">🏥</div>
                                    <div class="file-upload-text">
                                        <span class="file-upload-title">点击选择伤残鉴定书</span>
                                        <span class="file-upload-desc">支持PDF、Word等格式</span>
                                    </div>
                                    <input type="file" id="disabilityAssessmentFile" style="display: none;">
                                </div>
                                <button onclick="uploadDisabilityAssessment()" class="upload-btn">上传文件</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="info-section">
                    <h2>结案信息</h2>
                    <div class="info-content">
                        <div class="form-group">
                            <label for="finishTime">结案日期：</label>
                            <input type="date" id="finishTime" disabled class="readonly-field">
                        </div>
                        <div class="form-group">
                            <label for="finishRemark">结案备注：</label>
                            <textarea id="finishRemark" disabled class="readonly-field" placeholder="请输入"></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 预览模态框 -->
        <div id="previewModal" class="preview-modal">
            <div class="preview-content">
                <div class="preview-header">
                    <h3 id="previewFileName">文件名</h3>
                    <button class="preview-close" onclick="closePreviewModal()">×</button>
                </div>
                <div class="preview-body" id="previewBody">
                    <!-- 预览内容将在这里动态生成 -->
                </div>
            </div>
        </div>

        <!-- 自定义弹窗 -->
        <div id="customModal" class="custom-modal">
            <div class="custom-modal-content">
                <div class="custom-modal-header">
                    <div class="custom-modal-icon" id="modalIcon">✓</div>
                    <h3 class="custom-modal-title" id="modalTitle">提示</h3>
                </div>
                <div class="custom-modal-body" id="modalBody">
                    消息内容
                </div>
                <div class="custom-modal-footer" id="modalFooter">
                    <button class="custom-modal-btn primary" onclick="closeCustomModal()">确定</button>
                </div>
            </div>
        </div>

        <!-- 确认对话框 -->
        <div id="confirmModal" class="custom-modal confirm-modal">
            <div class="custom-modal-content">
                <div class="custom-modal-header">
                    <div class="custom-modal-icon warning">⚠</div>
                    <h3 class="custom-modal-title">确认操作</h3>
                </div>
                <div class="custom-modal-body" id="confirmBody">
                    确定要执行此操作吗？
                </div>
                <div class="custom-modal-footer">
                    <button class="custom-modal-btn secondary" onclick="closeConfirmModal()">取消</button>
                    <button class="custom-modal-btn danger" id="confirmBtn">确定</button>
                </div>
            </div>
        </div>

        <!-- 备注输入弹窗 -->
        <div id="remarkModal" class="custom-modal">
            <div class="custom-modal-content remark-modal-content">
                <div class="custom-modal-header">
                    <div class="custom-modal-icon info">📝</div>
                    <h3 class="custom-modal-title">添加案件备注</h3>
                </div>
                <div class="custom-modal-body">
                    <div class="remark-input-container">
                        <textarea 
                            id="remarkTextarea" 
                            class="remark-textarea" 
                            placeholder="请输入案件备注内容..."
                            maxlength="500"
                            rows="6"
                        ></textarea>
                        <div class="remark-counter">
                            <span id="remarkCharCount">0</span>/500
                        </div>
                    </div>
                </div>
                <div class="custom-modal-footer">
                    <button class="custom-modal-btn secondary" onclick="closeRemarkModal()">取消</button>
                    <button class="custom-modal-btn primary" onclick="submitRemark()">提交</button>
                </div>
            </div>
        </div>

        <script>
            // 自定义弹窗函数
            function showCustomModal(type, title, message, callback) {
                const modal = document.getElementById('customModal');
                const icon = document.getElementById('modalIcon');
                const modalTitle = document.getElementById('modalTitle');
                const modalBody = document.getElementById('modalBody');
                const modalFooter = document.getElementById('modalFooter');
                
                // 设置图标和样式
                switch(type) {
                    case 'success':
                        icon.className = 'custom-modal-icon success';
                        icon.textContent = '✓';
                        break;
                    case 'error':
                        icon.className = 'custom-modal-icon error';
                        icon.textContent = '✕';
                        break;
                    case 'warning':
                        icon.className = 'custom-modal-icon warning';
                        icon.textContent = '⚠';
                        break;
                    case 'info':
                        icon.className = 'custom-modal-icon info';
                        icon.textContent = 'ℹ';
                        break;
                    default:
                        icon.className = 'custom-modal-icon info';
                        icon.textContent = 'ℹ';
                }
                
                modalTitle.textContent = title;
                modalBody.textContent = message;
                
                // 设置按钮
                if (callback) {
                    modalFooter.innerHTML = `
                        <button class="custom-modal-btn primary" onclick="closeCustomModal(); ${callback}()">确定</button>
                    `;
                } else {
                    modalFooter.innerHTML = `
                        <button class="custom-modal-btn primary" onclick="closeCustomModal()">确定</button>
                    `;
                }
                
                modal.classList.add('show');
            }
            
            function closeCustomModal() {
                const modal = document.getElementById('customModal');
                modal.classList.remove('show');
            }
            
            function showConfirmModal(message, onConfirm) {
                const modal = document.getElementById('confirmModal');
                const confirmBody = document.getElementById('confirmBody');
                const confirmBtn = document.getElementById('confirmBtn');
                
                confirmBody.textContent = message;
                
                // 移除之前的事件监听器
                const newConfirmBtn = confirmBtn.cloneNode(true);
                confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
                
                // 添加新的事件监听器
                newConfirmBtn.addEventListener('click', () => {
                    closeConfirmModal();
                    if (onConfirm) onConfirm();
                });
                
                modal.classList.add('show');
            }
            
            function closeConfirmModal() {
                const modal = document.getElementById('confirmModal');
                modal.classList.remove('show');
            }
            
            // 替换alert的便捷函数
            function showSuccess(message) {
                showCustomModal('success', '成功', message);
            }
            
            function showError(message) {
                showCustomModal('error', '错误', message);
            }
            
            function showWarning(message) {
                showCustomModal('warning', '警告', message);
            }
            
            function showInfo(message) {
                showCustomModal('info', '提示', message);
            }
            
            function showConfirm(message, onConfirm) {
                showConfirmModal(message, onConfirm);
            }

            // 添加键盘事件监听器
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Escape') {
                    closeCustomModal();
                    closeConfirmModal();
                    closeRemarkModal();
                }
            });

            // 点击背景关闭弹窗
            document.addEventListener('click', function(event) {
                if (event.target.classList.contains('custom-modal')) {
                    closeCustomModal();
                    closeConfirmModal();
                    closeRemarkModal();
                }
            });

            // 从URL参数中获取案件ID
            function getCaseIdFromUrl() {
                const params = new URLSearchParams(window.location.search);
                return params.get('id');
            }

            const caseId = getCaseIdFromUrl();
            console.log('当前案件ID:', caseId);

            // 附件类型枚举
            const AttachmentTypeEnum = {
                SUPPLEMENTARY_MATERIAL: '补材告知书附件',
                CASE_MATERIALS: '案件材料附件',
                OCCUPATIONAL_INJURY_MATERIALS: '职伤结论书附件',
                DISABILITY_IDENTIFICATION_MATERIALS: '伤残鉴定书附件',
                ACCIDENT_MATERIALS: '事故附件'
            };

            // 支持预览的文件类型
            const SUPPORTED_PREVIEW_TYPES = [
                'image/jpeg', 
                'image/png', 
                'image/gif', 
                'image/svg+xml', 
                'application/pdf'
            ];

            // 存储省市区数据映射表（名称-选项索引）
            let provinceNameIndexMap = {};
            let cityNameIndexMap = {};
            let districtNameIndexMap = {};

            // 存储省市区数据
            let provinceData = [];
            let cityData = {};
            let districtData = {};

            // 存储事故类型和原因数据（修改为存储对象数组，包含id和name）
            let accidentTypeData = [];
            let accidentCauseData = [];
            let riderTypeData = [];

            // 存储事故标签数据（ID-名称映射）
            let accidentLabelData = {};
            // 存储事故标签名称数组（用于提交）
            let accidentLabelNames = [];

            // 存储模板文件数据
            let templateFileData = [];

            // 获取案件详情
            function getCaseDetail() {
                if (!caseId) {
                    showError('未获取到案件ID');
                    return;
                }

                console.log('开始获取案件详情，案件ID:', caseId);

                fetch(`/api/cases/${caseId}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('API响应数据:', data);
                        
                        // 检查API响应状态
                        if (data.code !== 200) {
                            console.error('API响应错误:', data);
                            showError(`获取案件详情失败：${data.message || '未知错误'}`);
                            return;
                        }
                        
                        const caseInfo = data.data;
                        console.log('案件详情数据:', caseInfo);
                        
                        // 检查caseInfo是否存在
                        if (!caseInfo) {
                            console.error('案件详情数据为空:', data);
                            showError('获取案件详情失败：数据为空');
                            return;
                        }
                        
                        // 填充报案人信息
                        document.getElementById('reporterName').value = caseInfo.reporterName || '';
                        document.getElementById('reporterMobile').value = caseInfo.reporterMobile || '';
                        // 填充案件信息
                        document.getElementById('caseNumber').value = caseInfo.caseNumber || '';
                        document.getElementById('caseServiceType').value = caseInfo.caseServiceType || '';
                        document.getElementById('accidentTime').value = formatDateTime(caseInfo.accidentTime);
                        document.getElementById('reportTime').value = formatDateTime(caseInfo.reportTime);
                        document.getElementById('accidentAddress').value = caseInfo.accidentAddress || '';
                        document.getElementById('declarationTips').value = caseInfo.declarationTips || '';

                        // 填充省市区下拉框 - 修复后的逻辑
                        const provinceSelect = document.getElementById('accidentProvince');
                        const citySelect = document.getElementById('accidentCity');
                        const districtSelect = document.getElementById('accidentDistrict');

                        // 匹配省份
                        if (caseInfo.accidentProvince) {
                            // 确保省份选项已经加载
                            const provinceOptions = Array.from(provinceSelect.options);
                            const provinceOption = provinceOptions.find(opt => opt.text === caseInfo.accidentProvince);

                            if (provinceOption) {
                                provinceSelect.value = provinceOption.value;

                                // 加载城市
                                loadCities(true).then(() => {
                                    // 匹配城市
                                    if (caseInfo.accidentCity) {
                                        const cityOptions = Array.from(citySelect.options);
                                        const cityOption = cityOptions.find(opt => opt.text === caseInfo.accidentCity);

                                        if (cityOption) {
                                            citySelect.value = cityOption.value;

                                            // 加载区县
                                            loadDistricts().then(() => {
                                                // 匹配区县
                                                if (caseInfo.accidentDistrict) {
                                                    const districtOptions = Array.from(districtSelect.options);
                                                    const districtOption = districtOptions.find(opt => opt.text === caseInfo.accidentDistrict);

                                                    if (districtOption) {
                                                        districtSelect.value = districtOption.value;
                                                    }
                                                }
                                            });
                                        }
                                    }
                                });
                            }
                        }

                        // 匹配事故类型（现在存储的是名称）
                        const accidentTypeSelect = document.getElementById('accidentType');
                        if (caseInfo.accidentType) {
                            const options = Array.from(accidentTypeSelect.options);
                            const option = options.find(opt => opt.value === caseInfo.accidentType);
                            if (option) option.selected = true;
                        }

                        // 匹配事故原因（现在存储的是名称）
                        const accidentCauseSelect = document.getElementById('accidentCause');
                        if (caseInfo.accidentCause) {
                            const options = Array.from(accidentCauseSelect.options);
                            const option = options.find(opt => opt.value === caseInfo.accidentCause);
                            if (option) option.selected = true;
                        }

                        // 匹配骑手类型（现在存储的是名称）
                        const riderTypeSelect = document.getElementById('riderType');
                        if (caseInfo.riderType) {
                            const options = Array.from(riderTypeSelect.options);
                            const option = options.find(opt => opt.value === caseInfo.riderType);
                            if (option) option.selected = true;
                        }

                        // 填充事故标签（使用名称而非ID）
                        if (caseInfo.accidentTags) {
                            // 假设caseInfo.accidentTags存储的是名称数组，如["标签1", "标签2"]
                            const tagsArray = caseInfo.accidentTags.split(',');
                            document.getElementById('accidentTags').value = tagsArray.join(',');
                            selectExistingTags(tagsArray);
                        }

                        document.getElementById('accidentProcess').value = caseInfo.accidentProcess || '';

                        // 填充骑手信息
                        document.getElementById('riderName').value = caseInfo.riderName || '';
                        document.getElementById('riderIdCard').value = caseInfo.riderIdCard || '';
                        document.getElementById('riderMobile').value = caseInfo.riderMobile || '';

                        // 填充运单相关信息
                        if (caseInfo.hasWaybill === '是') {
                            document.getElementById('hasWaybillYes').checked = true;
                        } else if (caseInfo.hasWaybill === '否') {
                            document.getElementById('hasWaybillNo').checked = true;
                        }
                        document.getElementById('relatedWaybillNumber').value = caseInfo.relatedWaybillNumber || '';
                        document.getElementById('orderAcceptTime').value = formatDateTime(caseInfo.orderAcceptTime);
                        document.getElementById('merchantAddress').value = caseInfo.merchantAddress || '';
                        document.getElementById('noWaybillScenarioType').value = caseInfo.noWaybillScenarioType || '';

                        // 填充服务商信息
                        document.getElementById('merchantType').value = caseInfo.merchantType || '';
                        document.getElementById('merchantName').value = caseInfo.merchantName || '';
                        document.getElementById('merchantId').value = caseInfo.merchantId || '';
                        document.getElementById('siteCity').value = caseInfo.siteCity || '';
                        document.getElementById('siteName').value = caseInfo.siteName || '';

                        // 填充商联系人信息
                        document.getElementById('contactName').value = caseInfo.contactName || '';
                        document.getElementById('contactPhone').value = caseInfo.contactPhone || '';
                        document.getElementById('stationManagerName').value = caseInfo.stationManagerName || '';
                        document.getElementById('stationManagerPhone').value = caseInfo.stationManagerPhone || '';

                        document.getElementById('injuredParts').value = caseInfo.injuredParts || '';
                        document.getElementById('diagnosisContent').value = caseInfo.diagnosisContent || '';

                        // 填充异地医疗机构信息
                        document.getElementById('isRemoteMedicalYes').checked = caseInfo.isRemoteMedical === 1;
                        document.getElementById('isRemoteMedicalNo').checked = caseInfo.isRemoteMedical === 0;
                        document.getElementById('medicalInstitutionName').value = caseInfo.medicalInstitutionName || '';
                        document.getElementById('medicalInstitutionLevel').value = caseInfo.medicalInstitutionLevel || '';
                        document.getElementById('medicalInstitutionAddress').value = caseInfo.medicalInstitutionAddress || '';
                        document.getElementById('medicalInstitutionContact').value = caseInfo.medicalInstitutionContact || '';

                        // 填充平台企业意见
                        document.getElementById('platformEnterpriseOpinion').value = caseInfo.platformEnterpriseOpinion || '';

                        // 填充首访信息
                        document.getElementById('firstVisitTime').value = formatDateTime(caseInfo.firstVisitTime);
                        document.getElementById('firstVisitRemark').value = caseInfo.firstVisitRemark || '';

                        // 填充撤案信息
                        document.getElementById('caseClosedTime').value = formatDateTime(caseInfo.caseClosedTime);
                        document.getElementById('caseClosedRemark').value = caseInfo.caseClosedRemark || '';

                        // 填充退回补材信息
                        document.getElementById('returnTime').value = formatDate(caseInfo.returnTime);

                        // 填充职伤认定信息
                        document.getElementById('determineSuccessYes').checked = caseInfo.determineIfSuccessful === 1;
                        document.getElementById('determineSuccessNo').checked = caseInfo.determineIfSuccessful === 0;
                        document.getElementById('determineTime').value = formatDate(caseInfo.determineTime);
                        document.getElementById('determineFailureRemark').value = caseInfo.determineFailureRemark || '';

                        // 填充结案信息
                        document.getElementById('finishTime').value = formatDate(caseInfo.finishTime);
                        document.getElementById('distributionAmount').value = caseInfo.distributionAmount || '';
                        document.getElementById('finishRemark').value = caseInfo.finishRemark || '';

                        // 按类型挂载附件
                        mountAttachmentsByType(caseInfo.attachments);

                        // 填充案件备注信息
                        const remarkTableBody = document.getElementById('remarkTableBody');
                        remarkTableBody.innerHTML = '';
                        
                        if (caseInfo.remarks && caseInfo.remarks.length > 0) {
                            caseInfo.remarks.forEach(remark => {
                                const row = document.createElement('tr');
                                row.innerHTML = `
                                    <td>
                                        <div class="remark-content">${remark.content}</div>
                                    </td>
                                    <td>
                                        <div class="remark-meta">${remark.operator}</div>
                                    </td>
                                    <td>
                                        <div class="remark-meta">${formatDateTime(remark.createTime)}</div>
                                    </td>
                                `;
                                remarkTableBody.appendChild(row);
                            });
                        } else {
                            // 显示空状态
                            const emptyRow = document.createElement('tr');
                            emptyRow.innerHTML = `
                                <td colspan="3">
                                    <div class="remark-empty">
                                        <div class="remark-empty-icon">📝</div>
                                        <div>暂无备注信息</div>
                                        <div style="font-size: 12px; margin-top: 5px;">点击标题右侧"添加备注"按钮开始添加</div>
                                    </div>
                                </td>
                            `;
                            remarkTableBody.appendChild(emptyRow);
                        }

                        // 新增：渲染案件状态标签
                        renderCaseStatusTags(caseInfo);
                    })
                    .catch(error => {
                        console.error('获取案件详情失败:', error);
                        showError(`获取案件详情失败：${error.message || '网络错误'}`);
                    });
            }

            // 渲染案件状态标签
            function renderCaseStatusTags(caseInfo) {
                const statusContainer = document.getElementById('caseStatusContainer');
                statusContainer.innerHTML = '';

                // 确保caseStatusName和externalStateName存在
                if (caseInfo.caseStatusName) {
                    const caseStatusTag = createStatusTag(
                        '案件状态',
                        caseInfo.caseStatusName,
                        'status-primary'
                    );
                    statusContainer.appendChild(caseStatusTag);
                }

                if (caseInfo.externalStateName) {
                    const externalStatusTag = createStatusTag(
                        '风神状态',
                        caseInfo.externalStateName,
                         'status-warning'
                    );
                    statusContainer.appendChild(externalStatusTag);
                }
            }

            // 创建状态标签
            function createStatusTag(label, status, className) {
                const tag = document.createElement('div');
                tag.className = `case-status-tag ${className}`;
                tag.innerHTML = `
                    <span>${label}：</span>
                    <span>${status}</span>
                `;
                return tag;
            }

            // 按类型挂载附件
            function mountAttachmentsByType(attachments) {
                // 清空所有附件容器
                document.getElementById('materialTableBody').innerHTML = '';
                document.getElementById('supplementMaterialContainer').innerHTML = '';
                document.getElementById('injuryConclusionContainer').innerHTML = '';
                document.getElementById('disabilityAssessmentContainer').innerHTML = '';
                document.getElementById('accidentMaterialsContainer').innerHTML = '';
                document.getElementById('screenshotMaterialsContainer').innerHTML = '';

                if (!attachments || attachments.length === 0) {
                    return;
                }

                // 处理每种类型的附件
                attachments.forEach(attachment => {
                    // 只在材料收集表格中显示案件材料附件
                    if (attachment.attachmentType === 'CASE_MATERIALS') {
                        const materialRow = document.createElement('tr');
                        materialRow.innerHTML = `
                                        <td>${attachment.fileName}</td>
                                        <td>${AttachmentTypeEnum[attachment.attachmentType] || attachment.attachmentType}</td>
                                        <td>${(attachment.fileSize / 1024).toFixed(2)}</td>
                                        <td>${attachment.uploader}</td>
                                        <td>${formatDateTime(attachment.uploadTime)}</td>
                                        <td>
                                            <button onclick="previewAttachment('${attachment.id}', '${attachment.fileName}', '${attachment.fileType}')" ${isPreviewable(attachment.fileType) ? '' : 'disabled'} class="secondary ${isPreviewable(attachment.fileType) ? '' : 'opacity-50 cursor-not-allowed'}">预览</button>
                                            <button onclick="deleteMaterial('${attachment.id}')" class="danger">删除</button>
                                            <button onclick="downloadMaterial('${attachment.id}')" class="secondary">下载</button>
                                        </td>
                                    `;
                        document.getElementById('materialTableBody').appendChild(materialRow);
                    }

                    // 根据附件类型挂载到对应区域
                    switch (attachment.attachmentType) {
                        case 'SUPPLEMENTARY_MATERIAL':
                            const supplementItem = createAttachmentItem(attachment);
                            document.getElementById('supplementMaterialContainer').appendChild(supplementItem);
                            break;
                        case 'OCCUPATIONAL_INJURY_MATERIALS':
                            const injuryItem = createAttachmentItem(attachment);
                            document.getElementById('injuryConclusionContainer').appendChild(injuryItem);
                            break;
                        case 'DISABILITY_IDENTIFICATION_MATERIALS':
                            const disabilityItem = createAttachmentItem(attachment);
                            document.getElementById('disabilityAssessmentContainer').appendChild(disabilityItem);
                            break;
                        case 'ACCIDENT_MATERIALS':
                            const accidentItem = createAttachmentItem(attachment);
                            document.getElementById('accidentMaterialsContainer').appendChild(accidentItem);
                            break;
                        case 'SCREENSHOT_MATERIAL':
                            const screenshotItem = createNotAllowedDeleteAttachmentItem(attachment);
                            document.getElementById('screenshotMaterialsContainer').appendChild(screenshotItem);
                            break;

                    }
                });
            }

            // 创建附件项
            function createAttachmentItem(attachment) {
                const item = document.createElement('div');
                item.className = 'attachment-item';
                item.innerHTML = `
                                <span>${attachment.fileName}</span>
                                <div class="attachment-actions">
                                    <button onclick="previewAttachment('${attachment.id}', '${attachment.fileName}', '${attachment.fileType}')" ${isPreviewable(attachment.fileType) ? '' : 'disabled'} class="secondary ${isPreviewable(attachment.fileType) ? '' : 'opacity-50 cursor-not-allowed'}">预览</button>
                                    <button onclick="downloadMaterial('${attachment.id}')" class="secondary">下载</button>
                                    <button onclick="deleteMaterial('${attachment.id}')" class="danger">删除</button>
                                </div>
                            `;
                return item;
            }

            function createNotAllowedDeleteAttachmentItem(attachment) {
                const item = document.createElement('div');
                item.className = 'attachment-item';
                item.innerHTML = `
                                <span>${attachment.fileName}</span>
                                <div class="attachment-actions">
                                    <button onclick="previewAttachment('${attachment.id}', '${attachment.fileName}', '${attachment.fileType}')" ${isPreviewable(attachment.fileType) ? '' : 'disabled'} class="secondary ${isPreviewable(attachment.fileType) ? '' : 'opacity-50 cursor-not-allowed'}">预览</button>
                                    <button onclick="downloadMaterial('${attachment.id}')" class="secondary">下载</button>
                                </div>
                            `;
                return item;
            }

            // 检查文件是否支持预览
            function isPreviewable(fileType) {
                return SUPPORTED_PREVIEW_TYPES.includes(fileType);
            }

            // 格式化日期
            function formatDate(dateTime) {
                if (!dateTime) return '';
                const date = new Date(dateTime);
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                return `${year}-${month}-${day}`;
            }

            // 格式化日期时间
            function formatDateTime(dateTime) {
                if (!dateTime) return '';
                const date = new Date(dateTime);
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');
                const seconds = String(date.getSeconds()).padStart(2, '0');
                return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
            }

            // 预览附件
            function previewAttachment(attachmentId, fileName, fileType) {
                const previewModal = document.getElementById('previewModal');
                const previewFileName = document.getElementById('previewFileName');
                const previewBody = document.getElementById('previewBody');

                previewFileName.textContent = fileName;
                previewBody.innerHTML = '';

                // 显示加载状态
                const loadingIndicator = document.createElement('div');
                loadingIndicator.textContent = '加载中...';
                previewBody.appendChild(loadingIndicator);

                const body = {
                    id: caseId,
                    attachmentIdList: [attachmentId]
                };

                // 发送请求到后端获取预览URL
                fetch('/api/cases/downloadInjuryMaterial', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(body)
                    })
                    .then(response => response.json())
                    .then(data => {
                        previewBody.removeChild(loadingIndicator);

                        if (data.code === 200 && data.data.fileList && data.data.fileList.length > 0) {
                            const previewUrl = data.data.fileList[0].previewUrl || data.data.fileList[0].downloadUrl;

                            if (fileType.startsWith('image/')) {
                                // 图片预览
                                const img = document.createElement('img');
                                img.src = previewUrl;
                                img.className = 'preview-image';
                                previewBody.appendChild(img);
                            } else if (fileType === 'application/pdf') {
                                // PDF预览
                                const pdfIframe = document.createElement('iframe');
                                pdfIframe.src = previewUrl;
                                pdfIframe.className = 'preview-pdf';
                                previewBody.appendChild(pdfIframe);

                            } else {
                                // 不支持的文件类型
                                const unsupportedMsg = document.createElement('div');
                                unsupportedMsg.className = 'unsupported-file';
                                unsupportedMsg.textContent = '不支持预览该类型文件';
                                previewBody.appendChild(unsupportedMsg);
                            }
                        } else {
                            const errorMsg = document.createElement('div');
                            errorMsg.className = 'unsupported-file';
                            errorMsg.textContent = '无法加载文件进行预览';
                            previewBody.appendChild(errorMsg);
                        }

                        // 显示预览模态框
                        previewModal.style.display = 'flex';
                    })
                    .catch(error => {
                        console.error('预览附件失败:', error);
                        previewBody.removeChild(loadingIndicator);
                        const errorMsg = document.createElement('div');
                        errorMsg.className = 'unsupported-file';
                        errorMsg.textContent = '预览失败，请重试';
                        previewBody.appendChild(errorMsg);
                        previewModal.style.display = 'flex';
                    });
            }

            // 关闭预览模态框
            function closePreviewModal() {
                document.getElementById('previewModal').style.display = 'none';
            }

            // 下载单个材料 - 优化版本
            // 优化要点：
            // 1. 优化Base64处理，提高内存使用效率
            // 2. 改进错误处理和用户反馈
            // 3. 统一MIME类型处理逻辑
            function downloadMaterial(attachmentId) {
                const body = {
                    id: caseId,
                    attachmentIdList: [attachmentId]
                };

                // 显示下载提示
                const downloadTip = document.createElement('div');
                downloadTip.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #00b42a;
                    color: white;
                    padding: 10px 15px;
                    border-radius: 4px;
                    z-index: 10000;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                `;
                downloadTip.textContent = '正在获取下载信息...';
                document.body.appendChild(downloadTip);

                // 发送请求到后端
                fetch('/api/cases/downloadInjuryMaterial', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(body)
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.code === 200 && data.data.fileList && data.data.fileList.length > 0) {
                            const downloadUrl = data.data.fileList[0].downloadUrl;
                            const fileName = data.data.fileList[0].fileName;
                            
                            downloadTip.textContent = `正在下载: ${fileName}`;
                            
                            // 使用优化的代理下载
                            const proxyUrl = `/api/cases/proxyDownload?url=${encodeURIComponent(downloadUrl)}`;
                            
                            fetch(proxyUrl)
                                .then(response => {
                                    if (!response.ok) {
                                        throw new Error(`代理下载失败，状态码: ${response.status}`);
                                    }
                                    return response.json();
                                })
                                .then(proxyData => {
                                    if (proxyData.code === 200 && proxyData.data) {
                                        // 优化：使用更高效的方式处理Base64
                                        const binaryString = atob(proxyData.data);
                                        const bytes = new Uint8Array(binaryString.length);
                                        
                                        // 使用更快的循环方式
                                        for (let i = 0; i < binaryString.length; i++) {
                                            bytes[i] = binaryString.charCodeAt(i);
                                        }
                                        
                                        // 根据文件扩展名设置MIME类型
                                        const fileExtension = fileName.toLowerCase().split('.').pop();
                                        const mimeType = getMimeType(fileExtension);
                                        
                                        const blob = new Blob([bytes], { type: mimeType });
                                        
                                        // 创建下载链接
                                        const url = window.URL.createObjectURL(blob);
                                        const a = document.createElement('a');
                                        a.href = url;
                                        a.download = fileName;
                                        a.style.display = 'none';
                                        a.setAttribute('download', fileName);
                                        a.setAttribute('target', '_blank');
                                        a.setAttribute('rel', 'noopener noreferrer');
                                        document.body.appendChild(a);
                                        
                                        // 触发下载
                                        try {
                                            a.click();
                                            console.log(`单个文件下载成功: ${fileName}`);
                                        } catch (error) {
                                            console.error('下载触发失败:', error);
                                            const event = new MouseEvent('click', {
                                                view: window,
                                                bubbles: true,
                                                cancelable: true
                                            });
                                            a.dispatchEvent(event);
                                            console.log('使用备用方式触发单个文件下载');
                                        }
                                    
                                        // 清理
                                        setTimeout(() => {
                                            if (document.body.contains(a)) {
                                                document.body.removeChild(a);
                                            }
                                            window.URL.revokeObjectURL(url);
                                            if (document.body.contains(downloadTip)) {
                                                document.body.removeChild(downloadTip);
                                            }
                                        }, 2000);
                                    } else {
                                        throw new Error(`代理下载失败: ${proxyData.message || '未知错误'}`);
                                    }
                                })
                                .catch(error => {
                                    console.error(`文件 ${fileName} 代理下载失败:`, error);
                                    // 如果代理下载失败，尝试直接使用链接下载
                                    const a = document.createElement('a');
                                    a.href = downloadUrl;
                                    a.download = fileName;
                                    a.style.display = 'none';
                                    a.setAttribute('download', fileName);
                                    a.setAttribute('target', '_blank');
                                    a.setAttribute('rel', 'noopener noreferrer');
                                    document.body.appendChild(a);
                                    
                                    try {
                                        a.click();
                                        console.log(`使用直接链接下载: ${fileName}`);
                                    } catch (directError) {
                                        console.error('直接下载也失败:', directError);
                                    }
                                    
                                    // 清理
                                    setTimeout(() => {
                                        if (document.body.contains(a)) {
                                            document.body.removeChild(a);
                                        }
                                        if (document.body.contains(downloadTip)) {
                                            document.body.removeChild(downloadTip);
                                        }
                                    }, 2000);
                                });
                        } else {
                            showError(data.message || '下载失败');
                            if (document.body.contains(downloadTip)) {
                                document.body.removeChild(downloadTip);
                            }
                        }
                    })
                    .catch(error => {
                        console.error('下载材料失败:', error);
                        showError('下载失败，请重试');
                        if (document.body.contains(downloadTip)) {
                            document.body.removeChild(downloadTip);
                        }
                    });
            }

                        // 删除单个材料
            function deleteMaterial(attachmentId) {
                showConfirm('确定要删除此材料吗？', () => {
                    // 显示加载状态
                    const buttons = document.querySelectorAll(`button[onclick="deleteMaterial(${attachmentId})"]`);
                    buttons.forEach(button => {
                        button.disabled = true;
                        button.textContent = '删除中...';
                    });

                    const body = {
                        id: caseId,
                        attachmentIdList: [attachmentId]
                    };

                    // 发送请求到后端
                    fetch('/api/cases/deleteInjuryMaterial', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(body)
                        })
                        .then(response => response.json())
                        .then(data => {
                            buttons.forEach(button => {
                                button.disabled = false;
                                button.textContent = '删除';
                            });

                            if (data.code === 200) {
                                showSuccess('删除成功');
                                // 刷新材料列表
                                getCaseDetail();
                            } else {
                                showError(data.message || '删除失败');
                            }
                        })
                        .catch(error => {
                            console.error('删除材料失败:', error);
                            showError('删除失败，请重试');
                            buttons.forEach(button => {
                                button.disabled = false;
                                button.textContent = '删除';
                            });
                        });
                });
            }

            // 批量下载所有材料 - 优化版本
            // 优化要点：
            // 1. 批量获取下载链接，减少API调用次数
            // 2. 并发下载文件，提高下载速度
            // 3. 动态调整并发数，根据浏览器性能优化
            // 4. 优化Base64处理，提高内存使用效率
            // 5. 添加下载进度和速度显示
            // 6. 改进错误处理和用户反馈
            function downloadAllMaterials() {
                // 显示加载状态
                const downloadButton = document.querySelector('button[onclick="downloadAllMaterials()"]');
                const originalText = downloadButton.textContent;
                downloadButton.disabled = true;
                downloadButton.textContent = '准备下载...';

                // 创建下载进度提示
                const progressDiv = document.createElement('div');
                progressDiv.id = 'downloadProgress';
                progressDiv.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: white;
                    border: 2px solid #2c7be5;
                    border-radius: 8px;
                    padding: 20px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                    z-index: 10000;
                    min-width: 300px;
                    text-align: center;
                `;
                progressDiv.innerHTML = `
                    <h3 style="margin: 0 0 15px 0; color: #2c7be5;">批量下载进度</h3>
                    <div id="downloadStatus">准备中...</div>
                    <div id="downloadCount" style="margin-top: 10px; color: #666;"></div>
                    <div id="downloadSpeed" style="margin-top: 5px; color: #666; font-size: 12px;"></div>
                    <div id="downloadTime" style="margin-top: 5px; color: #666; font-size: 12px;"></div>
                    <button onclick="cancelDownload()" style="margin-top: 15px; padding: 8px 16px; background: #f53f3f; color: white; border: none; border-radius: 4px; cursor: pointer;">取消下载</button>
                `;
                document.body.appendChild(progressDiv);

                // 收集所有案件附件的ID和文件名
                const attachmentIds = [];
                const fileNames = [];
                const materialRows = document.querySelectorAll('#materialTableBody tr');

                if (materialRows.length === 0) {
                    downloadButton.disabled = false;
                    downloadButton.textContent = originalText;
                    const progressDiv = document.getElementById('downloadProgress');
                    if (progressDiv) {
                        progressDiv.remove();
                    }
                    showWarning('没有可下载的案件附件');
                    return;
                }

                // 提取附件ID和文件名
                materialRows.forEach(row => {
                    const deleteButton = row.querySelector('button[onclick^="deleteMaterial"]');
                    if (deleteButton) {
                        const onclick = deleteButton.getAttribute('onclick');
                        const idMatch = onclick.match(/'([^']+)'/);
                        if (idMatch) {
                            attachmentIds.push(idMatch[1]);
                            // 获取文件名
                            const fileNameCell = row.querySelector('td:first-child');
                            if (fileNameCell) {
                                fileNames.push(fileNameCell.textContent.trim());
                            }
                        }
                    }
                });

                // 全局变量用于取消下载
                window.downloadCancelled = false;
                window.downloadStartTime = Date.now();
                window.downloadedCount = 0;

                // 优化：批量获取下载链接
                const batchGetDownloadUrls = async (ids) => {
                    try {
                        const body = {
                            id: caseId,
                            attachmentIdList: ids
                        };

                        const response = await fetch('/api/cases/downloadInjuryMaterial', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(body)
                        });

                        const data = await response.json();
                        if (data.code === 200 && data.data && data.data.fileList) {
                            return data.data.fileList;
                        } else {
                            throw new Error(data.message || '获取下载链接失败');
                        }
                    } catch (error) {
                        console.error('批量获取下载链接失败:', error);
                        throw error;
                    }
                };

                // 优化：并发下载文件
                const downloadFilesConcurrently = async (files, maxConcurrency = window.recommendedConcurrency || 3) => {
                    const downloadedFiles = [];
                    const chunks = [];
                    
                    // 将文件分批，每批最多maxConcurrency个
                    for (let i = 0; i < files.length; i += maxConcurrency) {
                        chunks.push(files.slice(i, i + maxConcurrency));
                    }

                    for (let chunkIndex = 0; chunkIndex < chunks.length; chunkIndex++) {
                        if (window.downloadCancelled) {
                            throw new Error('下载已取消');
                        }

                        const chunk = chunks[chunkIndex];
                        const chunkPromises = chunk.map(async (file, index) => {
                            const globalIndex = chunkIndex * maxConcurrency + index;
                            
                            // 更新进度
                            const statusDiv = document.getElementById('downloadStatus');
                            const countDiv = document.getElementById('downloadCount');
                            const speedDiv = document.getElementById('downloadSpeed');
                            
                            if (statusDiv) {
                                statusDiv.textContent = `正在下载: ${file.fileName}`;
                            }
                            if (countDiv) {
                                countDiv.textContent = `进度: ${globalIndex + 1} / ${files.length}`;
                            }

                            try {
                                // 使用优化的代理下载
                                const proxyUrl = `/api/cases/proxyDownload?url=${encodeURIComponent(file.downloadUrl)}`;
                                const proxyResponse = await fetch(proxyUrl);
                                
                                if (proxyResponse.ok) {
                                    const proxyData = await proxyResponse.json();
                                    
                                    if (proxyData.code === 200 && proxyData.data) {
                                        // 优化：使用更高效的方式处理Base64
                                        const binaryString = atob(proxyData.data);
                                        const bytes = new Uint8Array(binaryString.length);
                                        
                                        // 使用更快的循环方式
                                        for (let i = 0; i < binaryString.length; i++) {
                                            bytes[i] = binaryString.charCodeAt(i);
                                        }
                                        
                                        // 根据文件扩展名设置MIME类型
                                        const fileExtension = file.fileName.toLowerCase().split('.').pop();
                                        const mimeType = getMimeType(fileExtension);
                                        
                                        const fileBlob = new Blob([bytes], { type: mimeType });
                                        
                                        // 更新下载计数和速度
                                        window.downloadedCount++;
                                        const elapsed = Date.now() - window.downloadStartTime;
                                        const speed = (window.downloadedCount / (elapsed / 1000)).toFixed(1);
                                        const remainingFiles = files.length - window.downloadedCount;
                                        const estimatedTime = remainingFiles > 0 ? (remainingFiles / parseFloat(speed)).toFixed(1) : 0;
                                        
                                        if (speedDiv) {
                                            speedDiv.textContent = `下载速度: ${speed} 文件/秒`;
                                        }
                                        
                                        const timeDiv = document.getElementById('downloadTime');
                                        if (timeDiv) {
                                            if (remainingFiles > 0) {
                                                timeDiv.textContent = `预计剩余时间: ${estimatedTime} 秒`;
                                            } else {
                                                timeDiv.textContent = '正在打包文件...';
                                            }
                                        }
                                        
                                        return {
                                            name: file.fileName,
                                            blob: fileBlob,
                                            originalUrl: file.downloadUrl
                                        };
                                    } else {
                                        throw new Error(`代理下载失败: ${proxyData.message || '未知错误'}`);
                                    }
                                } else {
                                    // 创建下载链接文件
                                    const downloadInfo = `文件: ${file.fileName}\n下载链接: ${file.downloadUrl}\n\n由于浏览器安全限制，无法直接打包此文件。\n请使用上述链接手动下载文件。`;
                                    const infoBlob = new Blob([downloadInfo], { type: 'text/plain;charset=utf-8' });
                                    
                                    return {
                                        name: `${file.fileName}.txt`,
                                        blob: infoBlob,
                                        originalUrl: file.downloadUrl
                                    };
                                }
                            } catch (error) {
                                console.error(`文件 ${file.fileName} 下载失败:`, error);
                                
                                // 创建错误信息文件
                                const errorInfo = `文件: ${file.fileName}\n下载链接: ${file.downloadUrl}\n错误信息: ${error.message}\n\n请使用上述链接手动下载文件。`;
                                const errorBlob = new Blob([errorInfo], { type: 'text/plain;charset=utf-8' });
                                
                                return {
                                    name: `${file.fileName}_error.txt`,
                                    blob: errorBlob,
                                    originalUrl: file.downloadUrl
                                };
                            }
                        });

                        // 等待当前批次的所有文件下载完成
                        const chunkResults = await Promise.all(chunkPromises);
                        downloadedFiles.push(...chunkResults);
                        
                        // 短暂延迟，避免浏览器过载
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }

                    return downloadedFiles;
                };

                // 获取MIME类型的辅助函数
                const getMimeType = (extension) => {
                    const mimeTypes = {
                        'pdf': 'application/pdf',
                        'doc': 'application/msword',
                        'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                        'xls': 'application/vnd.ms-excel',
                        'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                        'ppt': 'application/vnd.ms-powerpoint',
                        'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                        'txt': 'text/plain',
                        'jpg': 'image/jpeg',
                        'jpeg': 'image/jpeg',
                        'png': 'image/png',
                        'gif': 'image/gif',
                        'zip': 'application/zip',
                        'rar': 'application/x-rar-compressed'
                    };
                    return mimeTypes[extension] || 'application/octet-stream';
                };

                // 全局MIME类型获取函数
                window.getMimeType = getMimeType;

                // 优化的ZIP创建和下载
                const createAndDownloadZip = async (files) => {
                    try {
                        console.log('开始创建ZIP文件，文件数量:', files.length);
                        
                        if (!files || files.length === 0) {
                            throw new Error('没有可下载的文件');
                        }

                        // 更新状态
                        const statusDiv = document.getElementById('downloadStatus');
                        if (statusDiv) {
                            statusDiv.textContent = '正在打包文件...';
                        }

                        // 创建zip文件
                        const zip = new JSZip();
                        
                        // 批量添加文件到zip，使用更高效的方式
                        const addFilePromises = files.map(async (file, index) => {
                            if (file.blob && file.blob.size > 0) {
                                const safeFileName = file.name.replace(/[<>:"/\\|?*]/g, '_');
                                zip.file(safeFileName, file.blob);
                                console.log(`文件已添加到ZIP: ${safeFileName}`);
                            } else {
                                console.warn(`文件 ${file.name} 的blob为空`);
                            }
                        });

                        // 等待所有文件添加完成
                        await Promise.all(addFilePromises);

                        console.log('ZIP文件创建完成，开始生成blob...');

                        // 生成zip文件，使用更高效的压缩选项
                        const zipBlob = await zip.generateAsync({
                            type: 'blob',
                            compression: 'DEFLATE',
                            compressionOptions: {
                                level: 6 // 平衡压缩率和速度
                            }
                        });
                        
                        console.log('ZIP blob生成完成，大小:', zipBlob.size, 'bytes');
                        
                        if (zipBlob.size === 0) {
                            throw new Error('生成的ZIP文件为空');
                        }

                        // 创建下载链接
                        const url = window.URL.createObjectURL(zipBlob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `案件材料_${caseId}_${new Date().toISOString().slice(0, 10)}.zip`;
                        a.style.display = 'none';
                        a.setAttribute('download', a.download);
                        a.target = '_blank';
                        a.rel = 'noopener noreferrer';
                        a.setAttribute('data-downloadurl', `application/zip:${a.download}:${url}`);
                        document.body.appendChild(a);
                        
                        // 触发下载
                        console.log('准备触发下载，文件名:', a.download);
                        
                        try {
                            a.click();
                            console.log('ZIP文件下载已触发，文件名:', a.download);
                        } catch (error) {
                            console.error('下载触发失败:', error);
                            const event = new MouseEvent('click', {
                                view: window,
                                bubbles: true,
                                cancelable: true
                            });
                            a.dispatchEvent(event);
                            console.log('使用备用方式触发下载');
                        }
                        
                        // 清理
                        setTimeout(() => {
                            if (document.body.contains(a)) {
                                document.body.removeChild(a);
                            }
                            window.URL.revokeObjectURL(url);
                            console.log('下载链接已清理');
                        }, 1000);

                        // 完成
                        console.log('批量下载完成，重置状态');
                        downloadButton.disabled = false;
                        downloadButton.textContent = originalText;
                        const progressDiv = document.getElementById('downloadProgress');
                        if (progressDiv) {
                            progressDiv.remove();
                        }
                        
                        // 统计结果
                        const successFiles = files.filter(f => !f.name.endsWith('.txt')).length;
                        const linkFiles = files.filter(f => f.name.endsWith('.txt')).length;
                        
                        if (linkFiles > 0) {
                            showWarning(`打包完成，共包含 ${files.length} 个文件。其中 ${successFiles} 个文件已打包，${linkFiles} 个文件因安全限制生成了下载链接文件。`);
                        } else {
                            showSuccess(`打包完成，共包含 ${files.length} 个文件`);
                        }
                        
                    } catch (error) {
                        console.error('创建ZIP文件失败:', error);
                        showError('打包文件失败，请重试');
                        downloadButton.disabled = false;
                        downloadButton.textContent = originalText;
                        const progressDiv = document.getElementById('downloadProgress');
                        if (progressDiv) {
                            progressDiv.remove();
                        }
                    }
                };

                // 主下载流程
                const startDownload = async () => {
                    try {
                        // 1. 批量获取所有文件的下载链接
                        const statusDiv = document.getElementById('downloadStatus');
                        if (statusDiv) {
                            statusDiv.textContent = '正在获取下载链接...';
                        }
                        
                        const fileList = await batchGetDownloadUrls(attachmentIds);
                        console.log(`获取到 ${fileList.length} 个文件的下载链接`);

                        // 2. 并发下载文件
                        const downloadedFiles = await downloadFilesConcurrently(fileList, 3);

                        // 3. 创建并下载ZIP文件
                        await createAndDownloadZip(downloadedFiles);

                    } catch (error) {
                        console.error('批量下载失败:', error);
                        showError(`下载失败: ${error.message}`);
                        downloadButton.disabled = false;
                        downloadButton.textContent = originalText;
                        const progressDiv = document.getElementById('downloadProgress');
                        if (progressDiv) {
                            progressDiv.remove();
                        }
                    }
                };

                // 开始下载
                startDownload();
            }

            // 取消下载函数
            function cancelDownload() {
                window.downloadCancelled = true;
                const progressDiv = document.getElementById('downloadProgress');
                if (progressDiv) {
                    progressDiv.remove();
                }
                const downloadButton = document.querySelector('button[onclick="downloadAllMaterials()"]');
                if (downloadButton) {
                    downloadButton.disabled = false;
                    downloadButton.textContent = '批量下载';
                }
                showInfo('下载已取消');
            }

            // 上传事故附件 - 上传成功后自动清空文件选择
            function uploadAccidentMaterials() {
                const files = document.getElementById('accidentMaterials').files;
                if (files.length === 0) {
                    showWarning('请选择文件');
                    return;
                }

                const formData = new FormData();
                formData.append('caseId', caseId);
                formData.append('attachmentType', 'ACCIDENT_MATERIALS');

                for (let i = 0; i < files.length; i++) {
                    formData.append('files', files[i]);
                }

                // 显示加载状态
                const uploadButton = document.querySelector('button[onclick="uploadAccidentMaterials()"]');
                const originalText = uploadButton.textContent;
                uploadButton.disabled = true;
                uploadButton.textContent = '上传中...';

                // 发送请求到后端
                fetch('/api/cases/uploadInjuryMaterial', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        uploadButton.disabled = false;
                        uploadButton.textContent = originalText;

                        if (data.code === 200) {
                            showSuccess('上传成功');
                            // 清空文件选择器
                            const accidentMaterialsInput = document.getElementById('accidentMaterials');
                            accidentMaterialsInput.value = '';
                            // 更新文件状态显示
                            updateFileUploadStatus('accidentMaterials', null);
                            // 刷新附件列表
                            getCaseDetail();
                        } else {
                            showError(data.message || '上传失败');
                        }
                    })
                    .catch(error => {
                        console.error('上传事故附件失败:', error);
                        showError('上传失败，请重试');
                        uploadButton.disabled = false;
                        uploadButton.textContent = '添加';
                    });
            }

            // 上传补材告知书 - 上传成功后自动清空文件选择
            function uploadSupplementMaterial() {
                const file = document.getElementById('supplementMaterialFile').files[0];
                if (!file) {
                    showWarning('请选择文件');
                    return;
                }

                const formData = new FormData();
                formData.append('caseId', caseId);
                formData.append('attachmentType', 'SUPPLEMENTARY_MATERIAL');
                formData.append('files', file);

                // 显示加载状态
                const uploadButton = document.querySelector('button[onclick="uploadSupplementMaterial()"]');
                const originalText = uploadButton.textContent;
                uploadButton.disabled = true;
                uploadButton.textContent = '上传中...';

                // 发送请求到后端
                fetch('/api/cases/uploadInjuryMaterial', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        uploadButton.disabled = false;
                        uploadButton.textContent = originalText;

                        if (data.code === 200) {
                            showSuccess('上传成功');
                            // 清空文件选择器
                            const supplementMaterialFileInput = document.getElementById('supplementMaterialFile');
                            supplementMaterialFileInput.value = '';
                            // 更新文件状态显示
                            updateFileUploadStatus('supplementMaterialFile', null);
                            // 刷新附件列表
                            getCaseDetail();
                        } else {
                            showError(data.message || '上传失败');
                        }
                    })
                    .catch(error => {
                        console.error('上传补材告知书失败:', error);
                        showError('上传失败，请重试');
                        uploadButton.disabled = false;
                        uploadButton.textContent = '上传';
                    });
            }

            // 上传职伤结论书 - 上传成功后自动清空文件选择
            function uploadInjuryConclusion() {
                const file = document.getElementById('injuryConclusionFile').files[0];
                if (!file) {
                    showWarning('请选择文件');
                    return;
                }

                const formData = new FormData();
                formData.append('caseId', caseId);
                formData.append('attachmentType', 'OCCUPATIONAL_INJURY_MATERIALS');
                formData.append('files', file);

                // 显示加载状态
                const uploadButton = document.querySelector('button[onclick="uploadInjuryConclusion()"]');
                const originalText = uploadButton.textContent;
                uploadButton.disabled = true;
                uploadButton.textContent = '上传中...';

                // 发送请求到后端
                fetch('/api/cases/uploadInjuryMaterial', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        uploadButton.disabled = false;
                        uploadButton.textContent = originalText;

                        if (data.code === 200) {
                            showSuccess('上传成功');
                            // 清空文件选择器
                            const injuryConclusionFileInput = document.getElementById('injuryConclusionFile');
                            injuryConclusionFileInput.value = '';
                            // 更新文件状态显示
                            updateFileUploadStatus('injuryConclusionFile', null);
                            // 刷新附件列表
                            getCaseDetail();
                        } else {
                            showError(data.message || '上传失败');
                        }
                    })
                    .catch(error => {
                        console.error('上传职伤结论书失败:', error);
                        showError('上传失败，请重试');
                        uploadButton.disabled = false;
                        uploadButton.textContent = '上传';
                    });
            }

            // 上传伤残鉴定书 - 上传成功后自动清空文件选择
            function uploadDisabilityAssessment() {
                const file = document.getElementById('disabilityAssessmentFile').files[0];
                if (!file) {
                    showWarning('请选择文件');
                    return;
                }

                const formData = new FormData();
                formData.append('caseId', caseId);
                formData.append('attachmentType', 'DISABILITY_IDENTIFICATION_MATERIALS');
                formData.append('files', file);

                // 显示加载状态
                const uploadButton = document.querySelector('button[onclick="uploadDisabilityAssessment()"]');
                const originalText = uploadButton.textContent;
                uploadButton.disabled = true;
                uploadButton.textContent = '上传中...';

                // 发送请求到后端
                fetch('/api/cases/uploadInjuryMaterial', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        uploadButton.disabled = false;
                        uploadButton.textContent = originalText;

                        if (data.code === 200) {
                            showSuccess('上传成功');
                            // 清空文件选择器
                            const disabilityAssessmentFileInput = document.getElementById('disabilityAssessmentFile');
                            disabilityAssessmentFileInput.value = '';
                            // 更新文件状态显示
                            updateFileUploadStatus('disabilityAssessmentFile', null);
                            // 刷新附件列表
                            getCaseDetail();
                        } else {
                            showError(data.message || '上传失败');
                        }
                    })
                    .catch(error => {
                        console.error('上传伤残鉴定书失败:', error);
                        showError('上传失败，请重试');
                        uploadButton.disabled = false;
                        uploadButton.textContent = '上传';
                    });
            }

            // 上传案件材料 - 上传成功后自动清空文件选择
            function uploadMaterials() {
                const files = document.getElementById('materialFiles').files;
                if (files.length === 0) {
                    showWarning('请选择文件');
                    return;
                }

                const formData = new FormData();
                formData.append('caseId', caseId);
                formData.append('attachmentType', 'CASE_MATERIALS');

                for (let i = 0; i < files.length; i++) {
                    formData.append('files', files[i]);
                }

                // 显示加载状态
                const uploadButton = document.querySelector('button[onclick="uploadMaterials()"]');
                const originalText = uploadButton.textContent;
                uploadButton.disabled = true;
                uploadButton.textContent = '上传中...';

                // 发送请求到后端
                fetch('/api/cases/uploadInjuryMaterial', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        uploadButton.disabled = false;
                        uploadButton.textContent = originalText;

                        if (data.code === 200) {
                            showSuccess('上传成功');
                            // 清空文件选择器
                            const materialFilesInput = document.getElementById('materialFiles');
                            materialFilesInput.value = '';
                            // 更新文件状态显示
                            updateFileUploadStatus('materialFiles', null);
                            // 刷新附件列表
                            getCaseDetail();
                        } else {
                            showError(data.message || '上传失败');
                        }
                    })
                    .catch(error => {
                        console.error('上传案件材料失败:', error);
                        showError('上传失败，请重试');
                        uploadButton.disabled = false;
                        uploadButton.textContent = '上传';
                    });
            }

            // 添加备注
            function addRemark() {
                showRemarkModal();
            }

            // 显示备注输入弹窗
            function showRemarkModal() {
                const modal = document.getElementById('remarkModal');
                const textarea = document.getElementById('remarkTextarea');
                const charCount = document.getElementById('remarkCharCount');
                
                // 清空之前的内容
                textarea.value = '';
                charCount.textContent = '0';
                charCount.className = 'remark-counter';
                
                // 添加字符计数监听
                textarea.addEventListener('input', function() {
                    const count = this.value.length;
                    charCount.textContent = count;
                    
                    // 根据字符数改变颜色
                    if (count > 400) {
                        charCount.className = 'remark-counter warning';
                    } else if (count > 480) {
                        charCount.className = 'remark-counter danger';
                    } else {
                        charCount.className = 'remark-counter';
                    }
                });
                
                // 添加回车键提交功能
                textarea.addEventListener('keydown', function(event) {
                    if (event.ctrlKey && event.key === 'Enter') {
                        submitRemark();
                    }
                });
                
                modal.classList.add('show');
                textarea.focus();
            }

            // 关闭备注弹窗
            function closeRemarkModal() {
                const modal = document.getElementById('remarkModal');
                modal.classList.remove('show');
            }

            // 提交备注
            function submitRemark() {
                const textarea = document.getElementById('remarkTextarea');
                const remark = textarea.value.trim();
                
                if (!remark) {
                    showWarning('请输入备注内容');
                    return;
                }
                
                if (remark.length > 500) {
                    showWarning('备注内容不能超过500个字符');
                    return;
                }

                // 收集基本案件信息
                const formData = {
                    id: caseId,
                    operateType: 'CASE_REMARKS',
                    remark: remark
                };

                // 创建多部分表单数据
                const multipartFormData = new FormData();
                multipartFormData.append('json', JSON.stringify(formData));

                // 显示加载状态
                const submitBtn = document.querySelector('#remarkModal .custom-modal-btn.primary');
                const originalText = submitBtn.textContent;
                submitBtn.disabled = true;
                submitBtn.textContent = '提交中...';

                // 发送请求到后端
                fetch('/api/cases/createOrUpdateInjuryCase', {
                        method: 'POST',
                        body: multipartFormData
                    })
                    .then(response => response.json())
                    .then(data => {
                        submitBtn.disabled = false;
                        submitBtn.textContent = originalText;

                        if (data.code === 200) {
                            showSuccess('案件备注添加成功');
                            closeRemarkModal();
                            // 刷新备注列表
                            getCaseDetail();
                        } else {
                            showError(data.message || '案件备注添加失败');
                        }
                    })
                    .catch(error => {
                        console.error('案件备注添加失败:', error);
                        showError('案件备注添加失败，请重试');
                        submitBtn.disabled = false;
                        submitBtn.textContent = '提交';
                    });
            }

            // 更新待遇发放金额
            function updateDistributionAmount() {
                const currentAmount = document.getElementById('distributionAmount').value;
                const newAmount = prompt('请输入新的待遇发放金额', currentAmount);

                if (newAmount === null) { // 用户点击取消
                    return;
                }

                if (!newAmount.trim()) {
                    showWarning('金额不能为空');
                    return;
                }

                if (isNaN(parseFloat(newAmount))) {
                    showWarning('请输入有效的金额');
                    return;
                }

                // 收集表单数据
                const formData = {
                    operateType: 'UPDATE',
                    id: caseId,
                    distributionAmount: newAmount
                };

                // 创建多部分表单数据
                const multipartFormData = new FormData();
                multipartFormData.append('json', JSON.stringify(formData));

                // 显示加载状态
                const updateButton = document.querySelector('button[onclick="updateDistributionAmount()"]');
                const originalText = updateButton.textContent;
                updateButton.disabled = true;
                updateButton.textContent = '更新中...';

                // 发送请求到后端
                fetch('/api/cases/createOrUpdateInjuryCase', {
                        method: 'POST',
                        body: multipartFormData
                    })
                    .then(response => response.json())
                    .then(data => {
                        updateButton.disabled = false;
                        updateButton.textContent = originalText;

                        if (data.code === 200) {
                            showSuccess('更新成功');
                            // 刷新页面或执行其他操作
                            getCaseDetail();
                        } else {
                            showError(data.message || '更新失败');
                        }
                    })
                    .catch(error => {
                        console.error('更新待遇发放金额失败:', error);
                        showError('更新失败，请重试');
                        updateButton.disabled = false;
                        updateButton.textContent = '修改';
                    });
            }

            // 加载省份
            function loadProvinces() {
                return new Promise((resolve) => {
                    if (provinceData.length > 0) {
                        resolve();
                        return;
                    }

                    fetch('/api/area/getAreaList?parentId=-1')
                        .then(response => response.json())
                        .then(data => {
                            provinceData = data.data || [];
                            const provincesSelect = document.getElementById('accidentProvince');
                            provincesSelect.innerHTML = '<option value="">请选择</option>';
                            provinceData.forEach(province => {
                                const option = document.createElement('option');
                                option.value = province.id;
                                option.textContent = province.name;
                                provincesSelect.appendChild(option);
                            });
                            resolve();
                        })
                        .catch(error => {
                            console.error('加载省份失败:', error);
                            resolve();
                        });
                });
            }


            // 加载城市
            function loadCities(clearDistrict = false) {
                return new Promise((resolve) => {
                    const provinceSelect = document.getElementById('accidentProvince');
                    const provinceId = provinceSelect.value;
                    const citiesSelect = document.getElementById('accidentCity');
                    citiesSelect.innerHTML = '<option value="">请选择</option>';

                    if (clearDistrict) {
                        const districtsSelect = document.getElementById('accidentDistrict');
                        districtsSelect.innerHTML = '<option value="">请选择</option>';
                    }

                    if (!provinceId) {
                        resolve();
                        return;
                    }

                    if (cityData[provinceId]) {
                        cityData[provinceId].forEach(city => {
                            const option = document.createElement('option');
                            option.value = city.id;
                            option.textContent = city.name;
                            citiesSelect.appendChild(option);
                        });
                        resolve();
                    } else {
                        fetch(`/api/area/getAreaList?parentId=${provinceId}`)
                            .then(response => response.json())
                            .then(data => {
                                cityData[provinceId] = data.data || [];
                                cityData[provinceId].forEach(city => {
                                    const option = document.createElement('option');
                                    option.value = city.id;
                                    option.textContent = city.name;
                                    citiesSelect.appendChild(option);
                                });
                                resolve();
                            })
                            .catch(error => {
                                console.error('加载城市失败:', error);
                                resolve();
                            });
                    }
                });
            }


            // 加载区县
            function loadDistricts() {
                return new Promise((resolve) => {
                    const citySelect = document.getElementById('accidentCity');
                    const cityId = citySelect.value;
                    const districtsSelect = document.getElementById('accidentDistrict');
                    districtsSelect.innerHTML = '<option value="">请选择</option>';

                    if (!cityId) {
                        resolve();
                        return;
                    }

                    if (districtData[cityId]) {
                        districtData[cityId].forEach(district => {
                            const option = document.createElement('option');
                            option.value = district.id;
                            option.textContent = district.name;
                            districtsSelect.appendChild(option);
                        });
                        resolve();
                    } else {
                        fetch(`/api/area/getAreaList?parentId=${cityId}`)
                            .then(response => response.json())
                            .then(data => {
                                districtData[cityId] = data.data || [];
                                districtData[cityId].forEach(district => {
                                    const option = document.createElement('option');
                                    option.value = district.id;
                                    option.textContent = district.name;
                                    districtsSelect.appendChild(option);
                                });
                                resolve();
                            })
                            .catch(error => {
                                console.error('加载区县失败:', error);
                                resolve();
                            });
                    }
                });
            }

            function findOptionByText(selectElement, text) {
                for (let i = 0; i < selectElement.options.length; i++) {
                    if (selectElement.options[i].text === text) {
                        return selectElement.options[i];
                    }
                }
                return null;
            }
            async function loadAndSetArea(provinceName, cityName, districtName) {
                // 确保省份数据已加载
                await loadProvinces();

                const provinceSelect = document.getElementById('accidentProvince');
                const provinceOption = findOptionByText(provinceSelect, provinceName);
                if (provinceOption) {
                    provinceSelect.value = provinceOption.value;
                    // 加载城市
                    await loadCities(true);

                    const citySelect = document.getElementById('accidentCity');
                    const cityOption = findOptionByText(citySelect, cityName);
                    if (cityOption) {
                        citySelect.value = cityOption.value;
                        // 加载区县
                        await loadDistricts();

                        const districtSelect = document.getElementById('accidentDistrict');
                        const districtOption = findOptionByText(districtSelect, districtName);
                        if (districtOption) {
                            districtSelect.value = districtOption.value;
                        }
                    }
                }
            }
            // 加载事故标签数据
            function loadAccidentLabels() {
                fetch(`/api/dictList/getDictList?dictType=ACCIDENT_LABEL`)
                    .then(response => response.json())
                    .then(data => {
                        accidentLabelData = data.data || {};
                        accidentLabelNames = Object.values(accidentLabelData); // 提取名称数组
                        renderAccidentTags();
                    })
                    .catch(error => {
                        console.error('加载事故标签失败:', error);
                        showError('加载事故标签失败，请刷新页面');
                    });
            }

            // 渲染事故标签按钮（使用名称）
// 修改标签选择交互提示
function renderAccidentTags() {
    const container = document.getElementById('accidentTagsContainer');
    container.innerHTML = '';

    accidentLabelNames.forEach(name => {
        const button = document.createElement('button');
        button.className = 'tag-button';
        button.textContent = name;
        button.dataset.name = name;

        button.addEventListener('click', function() {
            toggleTagSelection(name);
        });

        container.appendChild(button);
    });

    // 添加选填提示
    const tipElement = document.createElement('div');
    tipElement.className = 'tag-selection-tip';
    tipElement.style.color = '#86909c';
    tipElement.style.fontSize = '12px';
    tipElement.style.marginTop = '8px';
    tipElement.textContent = '提示：事故标签为选填项，可选择多个标签以便分类';
    container.parentNode.appendChild(tipElement);
}

            // 切换标签选中状态（使用名称）
            function toggleTagSelection(tagName) {
                const button = document.querySelector(`.tag-button[data-name="${tagName}"]`);
                if (!button) return;

                // 使用classList.toggle确保状态正确切换
                button.classList.toggle('selected');

                // 更新隐藏字段的值（存储名称，用逗号分隔）
                updateSelectedTags();

                // 隐藏错误提示
                document.getElementById('accidentTagsError').style.display = 'none';
            }

            // 更新选中的标签名称
            function updateSelectedTags() {
                const selectedButtons = document.querySelectorAll('.tag-button.selected');
                const selectedNames = Array.from(selectedButtons).map(btn => btn.dataset.name);
                document.getElementById('accidentTags').value = selectedNames.join(',');
            }

            // 选中已有案件的标签（使用名称）
            function selectExistingTags(tagsArray) {
                if (!tagsArray || tagsArray.length === 0) return;

                tagsArray.forEach(name => {
                    const button = document.querySelector(`.tag-button[data-name="${name}"]`);
                    if (button) {
                        button.classList.add('selected');
                    }
                });

                updateSelectedTags();
            }

            // 加载事故类型和事故原因（修改为获取名称并存储为数组）
            function loadAccidentTypeAndCause() {
                // 加载事故类型
                fetch(`/api/dictList/getDictList?dictType=ACCIDENT_TYPE`)
                    .then(response => response.json())
                    .then(data => {
                        // 转换为对象数组 [{"id": "1", "name": "类型1"}, ...]
                        accidentTypeData = Object.entries(data.data || {}).map(([id, name]) => ({
                            id,
                            name
                        }));
                        const accidentTypeSelect = document.getElementById('accidentType');
                        accidentTypeSelect.innerHTML = '<option value="">请选择</option>';

                        accidentTypeData.forEach(item => {
                            const optionElement = document.createElement('option');
                            optionElement.value = item.name; // 使用名称作为value
                            optionElement.textContent = item.name;
                            accidentTypeSelect.appendChild(optionElement);
                        });
                    })
                    .catch(error => console.error('加载事故类型失败:', error));

                // 加载事故原因
                fetch(`/api/dictList/getDictList?dictType=ACCIDENT_CAUSE`)
                    .then(response => response.json())
                    .then(data => {
                        // 转换为对象数组 [{"id": "1", "name": "原因1"}, ...]
                        accidentCauseData = Object.entries(data.data || {}).map(([id, name]) => ({
                            id,
                            name
                        }));
                        const accidentCauseSelect = document.getElementById('accidentCause');
                        accidentCauseSelect.innerHTML = '<option value="">请选择</option>';

                        accidentCauseData.forEach(item => {
                            const optionElement = document.createElement('option');
                            optionElement.value = item.name; // 使用名称作为value
                            optionElement.textContent = item.name;
                            accidentCauseSelect.appendChild(optionElement);
                        });
                    })
                    .catch(error => console.error('加载事故原因失败:', error));

                // 加载骑手类型
                fetch(`/api/dictList/getDictList?dictType=RIDER_TYPE`)
                    .then(response => response.json())
                    .then(data => {
                        // 转换为对象数组 [{"id": "1", "name": "类型1"}, ...]
                        riderTypeData = Object.entries(data.data || {}).map(([id, name]) => ({
                            id,
                            name
                        }));
                        const riderTypeSelect = document.getElementById('riderType');
                        riderTypeSelect.innerHTML = '<option value="">请选择</option>';

                        riderTypeData.forEach(item => {
                            const optionElement = document.createElement('option');
                            optionElement.value = item.name; // 使用名称作为value
                            optionElement.textContent = item.name;
                            riderTypeSelect.appendChild(optionElement);
                        });
                    })
                    .catch(error => console.error('加载骑手类型失败:', error));
            }

            // 启用可编辑字段
            function enableEditableFields() {
                const editableSections = ['案件信息', '骑手信息', '服务商信息', '异地医疗机构信息', '平台企业意见'];
                const sections = document.querySelectorAll('.info-section');
                sections.forEach(section => {
                    const sectionTitle = section.querySelector('h2').textContent.trim();

                    if (editableSections.includes(sectionTitle)) {
                        // 启用该区块内所有禁用的输入元素
                        const inputs = section.querySelectorAll('input[disabled], select[disabled], textarea[disabled]');
                        inputs.forEach(input => {
                            input.removeAttribute('disabled');
                            // 添加编辑样式，使其与普通输入框区分
                            input.classList.add('editable-field');
                        });

                        // 如果是异地医疗机构信息，启用单选按钮
                        if (sectionTitle === '异地医疗机构信息') {
                            const radios = section.querySelectorAll('input[type="radio"][disabled]');
                            radios.forEach(radio => {
                                radio.removeAttribute('disabled');
                            });
                        }
                    }
                });


                // 启用提交按钮
                document.getElementById('submitButton').disabled = false;

            }

            // 表单验证
            function validateForm() {
                let isValid = true;
                const requiredFields = [
                    'reporterName', 'reporterMobile', 'caseNumber', 'accidentTime',
                    'reportTime', 'accidentProvince', 'accidentCity', 'accidentDistrict',
                    'accidentAddress', 'accidentType', 'accidentCause', 'accidentProcess',
                    // 'injuredParts', 'diagnosisContent' ,'riderType'
                ];

                requiredFields.forEach(fieldId => {
                    const field = document.getElementById(fieldId);
                    if (!field.value.trim()) {
                        isValid = false;
                        field.classList.add('error');

                        // 添加错误提示
                        if (!field.nextElementSibling || !field.nextElementSibling.classList.contains('error-message')) {
                            const errorMsg = document.createElement('div');
                            errorMsg.className = 'error-message';
                            errorMsg.style.color = 'red';
                            errorMsg.style.fontSize = '0.9em';
                            errorMsg.textContent = '此字段为必填项';
                            field.parentNode.insertBefore(errorMsg, field.nextSibling);
                        }
                    } else {
                        field.classList.remove('error');

                        // 移除错误提示
                        const errorMsg = field.nextElementSibling;
                        if (errorMsg && errorMsg.classList.contains('error-message')) {
                            field.parentNode.removeChild(errorMsg);
                        }
                    }
                });

                // 验证手机号格式
                const mobileField = document.getElementById('reporterMobile');
                if (mobileField.value && !/^1[3-9]\d{9}$/.test(mobileField.value)) {
                    isValid = false;
                    mobileField.classList.add('error');

                    if (!mobileField.nextElementSibling || !mobileField.nextElementSibling.classList.contains('error-message')) {
                        const errorMsg = document.createElement('div');
                        errorMsg.className = 'error-message';
                        errorMsg.style.color = 'red';
                        errorMsg.style.fontSize = '0.9em';
                        errorMsg.textContent = '请输入有效的手机号';
                        mobileField.parentNode.insertBefore(errorMsg, mobileField.nextSibling);
                    }
                }

                // 验证事故标签
                // const accidentTags = document.getElementById('accidentTags').value;
                // if (!accidentTags.trim()) {
                //     isValid = false;
                //     document.getElementById('accidentTagsError').style.display = 'block';
                // }
                // 身份证号格式校验（如果填写）
                const idCardValue = document.getElementById('riderIdCard').value;
                if (idCardValue && !/^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[0-9Xx]$/.test(idCardValue)) {
                    isValid = false;
                    const idCardError = document.getElementById('idCardError');
                    idCardError.textContent = '请输入有效的身份证号';
                    idCardError.style.display = 'block';

                    // 添加错误样式
                    document.getElementById('riderIdCard').classList.add('error');
                } else {
                    document.getElementById('idCardError').style.display = 'none';
                    document.getElementById('riderIdCard').classList.remove('error');
                }
                return isValid;
            }

            // 提交表单（事故标签、骑手类型、事故原因和类型提交名称而非ID）
            function submitForm() {
                if (!validateForm()) {
                    showWarning('请填写所有必填字段并确保格式正确');
                    return;
                }

                // 收集表单数据
                const formData = {
                    operateType: 'UPDATE',
                    id: caseId,
                    // 报案人信息
                    reporterName: document.getElementById('reporterName').value,
                    reporterMobile: document.getElementById('reporterMobile').value,
                    // 案件信息
                    caseNumber: document.getElementById('caseNumber').value,
                    caseServiceType: document.getElementById('caseServiceType').value,
                    accidentTime: document.getElementById('accidentTime').value,
                    reportTime: document.getElementById('reportTime').value,
                    accidentProvince: document.getElementById('accidentProvince').selectedOptions[0]?.text || '',
                    accidentCity: document.getElementById('accidentCity').selectedOptions[0]?.text || '',
                    accidentDistrict: document.getElementById('accidentDistrict').selectedOptions[0]?.text || '',
                    accidentAddress: document.getElementById('accidentAddress').value,
                    accidentType: document.getElementById('accidentType').value, // 现在是名称
                    accidentCause: document.getElementById('accidentCause').value, // 现在是名称
                    accidentTags: document.getElementById('accidentTags').value, // 标签名称
                    accidentProcess: document.getElementById('accidentProcess').value,
                    // 骑手信息
                    riderName: document.getElementById('riderName').value,
                    riderIdCard: document.getElementById('riderIdCard').value,
                    riderMobile: document.getElementById('riderMobile').value,
                    riderType: document.getElementById('riderType').value, // 现在是名称
                    // 运单相关信息
                    hasWaybill: document.querySelector('input[name="hasWaybill"]:checked')?.value || '',
                    relatedWaybillNumber: document.getElementById('relatedWaybillNumber').value,
                    orderAcceptTime: document.getElementById('orderAcceptTime').value,
                    merchantAddress: document.getElementById('merchantAddress').value,
                    noWaybillScenarioType: document.getElementById('noWaybillScenarioType').value,
                    // 服务商信息
                    merchantType: document.getElementById('merchantType').value,
                    merchantName: document.getElementById('merchantName').value,
                    merchantId: document.getElementById('merchantId').value,
                    siteCity: document.getElementById('siteCity').value,
                    siteName: document.getElementById('siteName').value,
                    // 商联系人信息
                    contactName: document.getElementById('contactName').value,
                    contactPhone: document.getElementById('contactPhone').value,
                    stationManagerName: document.getElementById('stationManagerName').value,
                    stationManagerPhone: document.getElementById('stationManagerPhone').value,
                    injuredParts: document.getElementById('injuredParts').value,
                    diagnosisContent: document.getElementById('diagnosisContent').value,
                    // 异地医疗机构信息
                    isRemoteMedical: (document.querySelector('input[name="isRemoteMedical"]:checked')?.value) ?? '0',
                    medicalInstitutionName: document.getElementById('medicalInstitutionName').value,
                    medicalInstitutionLevel: document.getElementById('medicalInstitutionLevel').value,
                    medicalInstitutionAddress: document.getElementById('medicalInstitutionAddress').value,
                    medicalInstitutionContact: document.getElementById('medicalInstitutionContact').value,
                    // 平台企业意见
                    platformEnterpriseOpinion: document.getElementById('platformEnterpriseOpinion').value
                };

                // 创建多部分表单数据
                const multipartFormData = new FormData();
                multipartFormData.append('json', JSON.stringify(formData));

                // 显示加载状态
                const submitButton = document.getElementById('submitButton');
                submitButton.disabled = true;
                submitButton.textContent = '提交中...';

                // 发送请求到后端
                fetch('/api/cases/createOrUpdateInjuryCase', {
                        method: 'POST',
                        body: multipartFormData
                    })
                    .then(response => response.json())
                    .then(data => {
                        submitButton.disabled = false;
                        submitButton.textContent = '提交';

                        if (data.code === 200) {
                            showSuccess('提交成功');
                            // 刷新页面或执行其他操作
                            getCaseDetail();
                        } else {
                            showError(data.message || '提交失败');
                        }
                    })
                    .catch(error => {
                        console.error('提交表单失败:', error);
                        showError('提交失败，请重试');
                        submitButton.disabled = false;
                        submitButton.textContent = '提交';
                    });
            }

            function controlRemoteMedicalInputs() {
                const yesRadio = document.getElementById('isRemoteMedicalYes');
                const noRadio = document.getElementById('isRemoteMedicalNo');
                const medicalInputs = document.querySelectorAll('#medicalInstitutionName, #medicalInstitutionLevel, ' +
                    '#medicalInstitutionAddress, #medicalInstitutionContact');

                // 初始状态：默认选择"否"，禁用相关输入框
                disableMedicalInputs();

                // 监听单选按钮变化
                yesRadio.addEventListener('change', enableMedicalInputs);
                noRadio.addEventListener('change', disableMedicalInputs);

                // 启用医疗输入框
                function enableMedicalInputs() {
                    medicalInputs.forEach(input => {
                        input.disabled = false;
                        input.classList.remove('readonly-field');
                        input.classList.add('editable-field');
                    });
                }

                // 禁用医疗输入框
                function disableMedicalInputs() {
                    medicalInputs.forEach(input => {
                        input.disabled = true;
                        input.classList.remove('editable-field');
                        input.classList.add('readonly-field');
                        input.value = ''; // 清空输入框内容
                    });
                }
            }
            // 在页面加载部分添加手机号校验函数
            function validateMobile(inputFieldId) {
                const field = document.getElementById(inputFieldId);
                const value = field.value.trim();

                // 移除非数字字符
                const cleanedValue = value.replace(/\D/g, '');

                if (cleanedValue !== value) {
                    // 如果清理后值改变，更新输入框值（保留前11位数字）
                    field.value = cleanedValue.slice(0, 11);
                }

                // 移动光标到当前文本末尾
                setTimeout(() => {
                    field.selectionStart = field.selectionEnd = field.value.length;
                }, 0);
            }
            // 身份证号校验函数
            function validateIdCard() {
                const field = document.getElementById('riderIdCard');
                let value = field.value.trim();

                // 移除非数字和非X的字符，同时支持大小写X
                const cleanedValue = value.replace(/[^0-9Xx]/g, '');

                // 将小写x转换为大写X
                let formattedValue = cleanedValue.replace(/x/g, 'X');

                // 保留前17位数字和最后1位
                if (formattedValue.length > 18) {
                    formattedValue = formattedValue.substring(0, 18);
                }

                if (formattedValue !== value) {
                    field.value = formattedValue;

                    // 移动光标到当前文本末尾
                    setTimeout(() => {
                        field.selectionStart = field.selectionEnd = formattedValue.length;
                    }, 0);
                }
            }
            // 文件上传相关函数 - 包含文件选择取消功能
            // 功能特性：
            // 1. 实时显示已选择的文件数量和状态
            // 2. 支持查看详细文件列表
            // 3. 支持取消选择所有文件
            // 4. 支持移除单个文件
            // 5. 显示文件大小和名称
            // 6. 确认对话框防止误操作
            function initializeFileUploads() {
                // 为所有文件输入框添加change事件监听
                const fileInputs = [
                    'accidentMaterials',
                    'materialFiles', 
                    'injuryConclusionFile',
                    'disabilityAssessmentFile'
                ];

                fileInputs.forEach(inputId => {
                    const input = document.getElementById(inputId);
                    const uploadArea = input.closest('.file-upload-area');
                    
                    if (input) {
                        input.addEventListener('change', function(e) {
                            updateFileUploadStatus(inputId, e.target.files);
                        });
                    }

                    // 添加拖拽上传功能
                    if (uploadArea) {
                        uploadArea.addEventListener('dragover', function(e) {
                            e.preventDefault();
                            this.classList.add('drag-over');
                        });

                        uploadArea.addEventListener('dragleave', function(e) {
                            e.preventDefault();
                            this.classList.remove('drag-over');
                        });

                        uploadArea.addEventListener('drop', function(e) {
                            e.preventDefault();
                            this.classList.remove('drag-over');
                            
                            const files = e.dataTransfer.files;
                            if (files.length > 0) {
                                input.files = files;
                                updateFileUploadStatus(inputId, files);
                                
                                // 显示拖拽成功提示
                                showSuccess(`已选择 ${files.length} 个文件`);
                            }
                        });
                    }
                });
            }

            function updateFileUploadStatus(inputId, files) {
                const container = document.querySelector(`#${inputId}`).closest('.file-upload-container');
                let statusDiv = container.querySelector('.file-upload-status');
                
                if (!statusDiv) {
                    statusDiv = document.createElement('div');
                    statusDiv.className = 'file-upload-status';
                    container.appendChild(statusDiv);
                }

                if (files && files.length > 0) {
                    const fileCount = files.length;
                    const fileList = Array.from(files);
                    
                    statusDiv.className = 'file-upload-status success';
                    statusDiv.innerHTML = `
                        <div class="file-status-content">
                            <div class="file-status-info">
                                <span class="file-status-icon">✓</span>
                                <span class="file-status-text">已选择 ${fileCount} 个文件</span>
                            </div>
                            <div class="file-status-actions">
                                <button class="view-files-btn" onclick="toggleFileList('${inputId}')" title="查看文件列表">
                                    <span>👁</span>
                                </button>
                                <button class="clear-files-btn" onclick="clearSelectedFiles('${inputId}')" title="取消选择">
                                    <span>✕</span>
                                </button>
                            </div>
                        </div>
                        <div class="file-list-container" id="fileList_${inputId}" style="display: none;">
                            ${generateFileListHTML(fileList)}
                        </div>
                    `;
                } else {
                    statusDiv.remove();
                }
            }

            function generateFileListHTML(files) {
                return files.map((file, index) => `
                    <div class="file-item">
                        <div class="file-item-info">
                            <span class="file-icon">📄</span>
                            <span class="file-name">${file.name}</span>
                            <span class="file-size">(${formatFileSize(file.size)})</span>
                        </div>
                        <button class="remove-file-btn" onclick="removeSingleFile(event, ${index})" title="移除此文件">
                            <span>×</span>
                        </button>
                    </div>
                `).join('');
            }

            function formatFileSize(bytes) {
                if (bytes === 0) return '0 B';
                const k = 1024;
                const sizes = ['B', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            function toggleFileList(inputId) {
                const fileListContainer = document.getElementById(`fileList_${inputId}`);
                if (fileListContainer) {
                    const isVisible = fileListContainer.style.display !== 'none';
                    fileListContainer.style.display = isVisible ? 'none' : 'block';
                }
            }

            function removeSingleFile(event, fileIndex) {
                event.stopPropagation();
                const fileItem = event.target.closest('.file-item');
                const fileListContainer = fileItem.closest('.file-list-container');
                const inputId = fileListContainer.id.replace('fileList_', '');
                
                const input = document.getElementById(inputId);
                if (input && input.files) {
                    const dt = new DataTransfer();
                    const files = Array.from(input.files);
                    
                    // 移除指定索引的文件
                    files.splice(fileIndex, 1);
                    
                    // 重新设置文件列表
                    files.forEach(file => dt.items.add(file));
                    input.files = dt.files;
                    
                    // 更新显示
                    updateFileUploadStatus(inputId, input.files);
                    
                    showInfo(`已移除文件`);
                }
            }

            function clearSelectedFiles(inputId) {
                const input = document.getElementById(inputId);
                if (input && input.files && input.files.length > 0) {
                    showConfirm('确定要取消选择所有文件吗？', () => {
                        input.value = '';
                        updateFileUploadStatus(inputId, null);
                        showSuccess('已取消文件选择');
                    });
                }
            }

            // 页面加载完成后初始化
            window.onload = async function() {
                if (!caseId) {
                    showError('未获取到案件ID，请检查URL参数');
                    return;
                }
                
                // 检查浏览器性能并优化并发数
                const checkBrowserPerformance = () => {
                    const memory = performance.memory;
                    const connection = navigator.connection;
                    
                    // 根据内存和网络状况调整并发数
                    let recommendedConcurrency = 3; // 默认值
                    
                    if (memory && memory.usedJSHeapSize < 50 * 1024 * 1024) { // 小于50MB
                        recommendedConcurrency = 2;
                    } else if (memory && memory.usedJSHeapSize > 200 * 1024 * 1024) { // 大于200MB
                        recommendedConcurrency = 4;
                    }
                    
                    if (connection) {
                        if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
                            recommendedConcurrency = 1;
                        } else if (connection.effectiveType === '4g') {
                            recommendedConcurrency = Math.min(recommendedConcurrency + 1, 5);
                        }
                    }
                    
                    window.recommendedConcurrency = recommendedConcurrency;
                    console.log(`根据浏览器性能，推荐并发数: ${recommendedConcurrency}`);
                };
                
                checkBrowserPerformance();
                
                // 为身份证号字段添加校验
                document.getElementById('riderIdCard').addEventListener('input', validateIdCard);

                // 为手机号字段添加校验
                document.getElementById('reporterMobile').addEventListener('input', function() {
                    validateMobile('reporterMobile');
                });
                document.getElementById('riderMobile').addEventListener('input', function() {
                    validateMobile('riderMobile');
                });
                document.getElementById('contactPhone').addEventListener('input', function() {
                    validateMobile('contactPhone');
                });
                document.getElementById('stationManagerPhone').addEventListener('input', function() {
                    validateMobile('stationManagerPhone');
                });
                // 先加载省市区基础数据
                await loadProvinces();
                await loadAccidentTypeAndCause();
                await loadAccidentLabels();

                // 然后加载案件详情
                await getCaseDetail();

                // 启用可编辑字段
                enableEditableFields();
                controlRemoteMedicalInputs();
                // 加载模板文件下拉列表
                loadTemplateFiles();
                // 初始化文件上传功能
                initializeFileUploads();
                
                // 初始化时间选择器
                initializeDateTimeInputs();
            };

            // 初始化时间选择器 - 支持秒级选择
            function initializeDateTimeInputs() {
                const datetimeInputs = document.querySelectorAll('input[type="datetime-local"]');
                
                datetimeInputs.forEach(input => {
                    // 设置step属性为1，支持秒级选择
                    input.step = "1";
                    
                    // 如果没有值，设置当前时间作为默认值
                    if (!input.value) {
                        const now = new Date();
                        const year = now.getFullYear();
                        const month = String(now.getMonth() + 1).padStart(2, '0');
                        const day = String(now.getDate()).padStart(2, '0');
                        const hours = String(now.getHours()).padStart(2, '0');
                        const minutes = String(now.getMinutes()).padStart(2, '0');
                        const seconds = String(now.getSeconds()).padStart(2, '0');
                        
                        const datetimeValue = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
                        input.value = datetimeValue;
                    }
                    
                    // 添加输入事件监听，确保时间格式正确
                    input.addEventListener('input', function() {
                        // 验证时间格式
                        if (this.value && !this.value.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}$/)) {
                            console.warn('时间格式不正确:', this.value);
                        }
                    });

                    // 添加点击事件，确保时间选择器正常工作
                    input.addEventListener('click', function(e) {
                        // 确保原生时间选择器可以正常打开
                        this.focus();
                    });

                    // 添加键盘事件支持
                    input.addEventListener('keydown', function(e) {
                        if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            this.click();
                        }
                    });

                    // 添加失去焦点事件，确保选择器正常关闭
                    input.addEventListener('blur', function() {
                        // 可以在这里添加验证逻辑
                        console.log('时间选择器失去焦点:', this.value);
                    });

                    // 检测浏览器是否支持原生时间选择器
                    if (!input.showPicker) {
                        console.warn('浏览器不支持showPicker方法，使用原生时间选择器');
                    }
                });

                console.log('时间选择器初始化完成，支持秒级选择');
            }

            // 获取模板类型下拉列表
            function loadTemplateFiles() {
                fetch('/api/template/file/type/list', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.code === 200 && data.data) {
                        populateTemplateDropdown(data.data);
                    } else {
                        throw new Error(data.message || '获取模板类型列表失败');
                    }
                })
                .catch(error => {
                    console.error('获取模板类型列表失败:', error);
                    showError('获取模板类型列表失败: ' + error.message);
                });
            }

            // 填充下拉框选项
            function populateTemplateDropdown(templateFiles) {
                const dropdown = document.getElementById('templateType');

                // 清空现有选项（保留第一个"请选择"选项）
                while (dropdown.options.length > 1) {
                    dropdown.remove(1);
                }

                // 添加新选项
                templateFiles.forEach(template => {
                    if (template.code && template.name) {
                        const option = new Option(template.name, template.code);
                        dropdown.add(option);
                    }
                });
            }

            // 下载模板类型文件
            function downloadTemplateFile() {
                console.log('模板下载函数被调用');
                
                var typeCode = document.getElementById('templateType').value;
                if(typeCode === ''){
                    showWarning("请选择模板类型");
                    return;
                }
                if(caseId === ''){
                    showError("案件ID参数异常");
                    return;
                }
                // 获取模板类型select元素
                var templateTypeSelectElement = document.getElementById('templateType');
                // 获取当前选中的选项的文本
                var templateTypeSelectedText = templateTypeSelectElement.options[templateTypeSelectElement.selectedIndex].text;

                // 获取事故省select元素
                var accidentProvinceSelectElement = document.getElementById('accidentProvince');
                // 获取当前选中的选项的文本
                var accidentProvinceSelectedText = accidentProvinceSelectElement.options[accidentProvinceSelectElement.selectedIndex].text;

                // 骑手姓名
                var riderName = document.getElementById('riderName').value;
                // 下载文件名称
                var fileName = templateTypeSelectedText + "-" + accidentProvinceSelectedText + "-" + riderName + ".docx";
                // 构建JSON数据
                var jsonData = {
                    caseId: caseId,
                    templateTypeCode: typeCode
                };

                // 发送请求
                fetch('/api/data/export/word/template', {
                    method: 'POST',
                    body: JSON.stringify(jsonData),
                    headers: {
                        'Content-Type' : 'application/json'
                    }
                }).then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.blob(); // 这里返回Promise
                }).then(blob => {
                    downloadDocxFile(blob, fileName);
                }).catch(error => {
                    console.error('导出失败', error);
                    showError('导出失败: ' + error.message);
                });

                /**
                 * 下载文件到本地
                 * @param {Blob} blob - 文件数据
                 * @param {string} fileName - 文件名
                 */
                function downloadDocxFile(blob, fileName) {
                    try {
                        console.log('开始下载模板文件:', fileName);
                        
                        // 更严格的Blob检查
                        if (!blob || !(blob instanceof Blob)) {
                            throw new Error('无效的Blob对象');
                        }

                        const URL = window.URL || window.webkitURL;
                        if (!URL) {
                            throw new Error('浏览器不支持URL.createObjectURL');
                        }

                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = fileName || 'download.docx';
                        a.style.display = 'none';
                        // 确保下载行为正确，强制下载而不是打开
                        a.target = '_blank';
                        a.rel = 'noopener noreferrer';
                        // 添加额外的下载属性
                        const fileExtension = fileName.toLowerCase().split('.').pop();
                        let mimeType = 'application/octet-stream';
                        if (fileExtension === 'docx') {
                            mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
                        }
                        a.setAttribute('data-downloadurl', `${mimeType}:${fileName}:${url}`);

                        document.body.appendChild(a);
                        
                        // 防止重复下载
                        if (window.currentDownloadingFile === fileName) {
                            console.log('文件正在下载中，跳过重复下载');
                            return;
                        }
                        
                        // 防止在批量下载过程中触发模板下载
                        if (window.isDownloading) {
                            console.log('批量下载正在进行中，跳过模板下载');
                            return;
                        }
                        
                        window.currentDownloadingFile = fileName;
                        console.log('触发模板文件下载:', fileName);
                        
                        // 使用更强制性的下载方式
                        try {
                        a.click();
                            console.log('模板文件下载已触发');
                        } catch (error) {
                            console.error('模板下载触发失败:', error);
                            // 尝试备用下载方式
                            const event = new MouseEvent('click', {
                                view: window,
                                bubbles: true,
                                cancelable: true
                            });
                            a.dispatchEvent(event);
                            console.log('使用备用方式触发模板下载');
                        }

                        // 延迟清理以避免下载中断
                        setTimeout(() => {
                            if (document.body.contains(a)) {
                            document.body.removeChild(a);
                            }
                            URL.revokeObjectURL(url);
                            window.currentDownloadingFile = null;
                            console.log('模板文件下载链接已清理');
                        }, 200);

                    } catch (error) {
                        console.error('下载失败:', error);
                        showError('文件下载失败: ' + error.message);
                    }
                }
            }
        </script>
    </body>

</html>