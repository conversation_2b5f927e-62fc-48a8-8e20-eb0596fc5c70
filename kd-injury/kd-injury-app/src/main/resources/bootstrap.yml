####测试环境
eureka-env:
  host:
    local: 127.0.0.1
    test: *************
    pro: ************
eureka:
  instance:
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
  client:
    serviceUrl:
      #      defaultZone: http://pater:pater_123@${eureka-env.host.local}:8761/eureka/  # 本地
      defaultZone: http://pater:pater_123@${eureka-env.host.test}:8761/eureka/  # 测试
#      defaultZone: http://root:o5LzP32ZSBFBDe8v@${eureka-env.host.pro}:8761/eureka/  #生产

server:
  port: 8590
spring:
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 100MB
      enabled: true
  main:
    allow-bean-definition-overriding: true #允许bean重复
  application:
    name: kd-injury-app
  cloud:
    config:
      label: kd-cuisine # 对应配置中心文件的${label}部
      discovery:
        enabled: true # 通过服务发现的方式去找配置中心
        service-id: kd-config-server # 配置中心的名字，直接配置名称可以在配置中心集群的时候实现负载均衡
      #测试配置
      profile: test
      #本地配置
#      profile: local
      username: pater
      password: pater_123

      #生产配置
#      profile: pro  # 对应配置中心文件的${profile}部
#      username: root
#      password: F5XjWamhKzll8eB6