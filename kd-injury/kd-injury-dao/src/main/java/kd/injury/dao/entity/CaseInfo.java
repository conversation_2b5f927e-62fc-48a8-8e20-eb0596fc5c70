package kd.injury.dao.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 案件信息表实体类
 */
@Data
@TableName("t_case_info")
public class CaseInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private String id;

    /**
     * 备案号
     */
    @TableField(value = "case_number")
    private String caseNumber;

    /**
     * 骑手姓名
     */
    @TableField("rider_name")
    private String riderName;

    /**
     * 骑手身份证号
     */
    @TableField("rider_id_card")
    private String riderIdCard;

    /**
     * 责任人
     */
    @TableField("responsible_person")
    private String responsiblePerson;

    /**
     * 事故标签
     */
    @TableField("accident_tags")
    private String accidentTags;

    /**
     * 报案人姓名
     */
    @TableField("reporter_name")
    private String reporterName;

    /**
     * 报案人手机号
     */
    @TableField("reporter_mobile")
    private String reporterMobile;

    /**
     * 案件状态
     */
    @TableField("case_status")
    private String caseStatus;

    /**
     * 案件服务类型
     */
    @TableField("case_service_type")
    private String caseServiceType;

    /**
     * 外部状态（这里指风神的）
     */
    @TableField("external_state")
    private String externalState;

    /**
     * 报案时间
     */
    @TableField("report_time")
    private LocalDateTime reportTime;

    /**
     * 骑手手机号
     */
    @TableField("rider_mobile")
    private String riderMobile;

    /**
     * 骑手类型（如全职、兼职等）
     */
    @TableField("rider_type")
    private String riderType;

    /**
     * 是否有运单
     */
    @TableField("has_waybill")
    private String hasWaybill;

    /**
     * 关联运单号
     */
    @TableField("related_waybill_number")
    private String relatedWaybillNumber;

    /**
     * 接单时间
     */
    @TableField("order_accept_time")
    private LocalDateTime orderAcceptTime;

    /**
     * 商家地址
     */
    @TableField("merchant_address")
    private String merchantAddress;

    /**
     * 无运单场景类型
     */
    @TableField("no_waybill_scenario_type")
    private String noWaybillScenarioType;

    /**
     * 事故时间
     */
    @TableField("accident_time")
    private LocalDateTime accidentTime;

    /**
     * 事故详细地址
     */
    @TableField("accident_address")
    private String accidentAddress;

    /**
     * 事故类型（如碰撞、摔倒等）
     */
    @TableField("accident_type")
    private String accidentType;

    /**
     * 事故原因
     */
    @TableField("accident_cause")
    private String accidentCause;

    /**
     * 事故经过，限100字
     */
    @TableField("accident_process")
    private String accidentProcess;

    /**
     * 事故所在省
     */
    @TableField("accident_province")
    private String accidentProvince;

    /**
     * 事故所在市
     */
    @TableField("accident_city")
    private String accidentCity;

    /**
     * 事故所在区
     */
    @TableField("accident_district")
    private String accidentDistrict;

    /**
     * 服务商 - 商名称
     */
    @TableField("merchant_name")
    private String merchantName;

    /**
     * 服务商 - 站点名称
     */
    @TableField("site_name")
    private String siteName;

    /**
     * 商类型
     */
    @TableField("merchant_type")
    private String merchantType;

    /**
     * 商ID
     */
    @TableField("merchant_id")
    private String merchantId;

    /**
     * 站点城市
     */
    @TableField("site_city")
    private String siteCity;

    /**
     * 联系人姓名
     */
    @TableField("contact_name")
    private String contactName;

    /**
     * 联系人电话
     */
    @TableField("contact_phone")
    private String contactPhone;

    /**
     * 站长姓名
     */
    @TableField("station_manager_name")
    private String stationManagerName;

    /**
     * 站长电话
     */
    @TableField("station_manager_phone")
    private String stationManagerPhone;

    /**
     * 审核人
     */
    @TableField("reviewer")
    private String reviewer;

    /**
     * 是否异地医疗，0-否 1-是
     */
    @TableField("is_remote_medical")
    private Integer isRemoteMedical;

    /**
     * 异地医疗机构名称
     */
    @TableField("medical_institution_name")
    private String medicalInstitutionName;

    /**
     * 异地医疗机构级别
     */
    @TableField("medical_institution_level")
    private String medicalInstitutionLevel;

    /**
     * 异地医疗机构通讯地址
     */
    @TableField("medical_institution_address")
    private String medicalInstitutionAddress;

    /**
     * 异地医疗机构联系电话
     */
    @TableField("medical_institution_contact")
    private String medicalInstitutionContact;

    /**
     * 平台企业意见
     */
    @TableField("platform_enterprise_opinion")
    private String platformEnterpriseOpinion;

    /**
     * 受伤部位，限50字
     */
    @TableField("injured_parts")
    private String injuredParts;

    /**
     * 诊断内容，限100字
     */
    @TableField("diagnosis_content")
    private String diagnosisContent;

    /**
     * 首访时间
     */
    @TableField("first_visit_time")
    private LocalDateTime firstVisitTime;

    /**
     * 首访备注
     */
    @TableField("first_visit_remark")
    private String firstVisitRemark;

    /**
     * 撤案日期
     */
    @TableField("case_closed_time")
    private LocalDateTime caseClosedTime;

    /**
     * 撤案原因
     */
    @TableField("case_closed_remark")
    private String caseClosedRemark;

    /**
     * 中止时间
     */
    @TableField("suspension_time")
    private LocalDateTime suspensionTime;

    /**
     * 中止原因
     */
    @TableField("suspension_remark")
    private String suspensionRemark;

    /**
     * 申报时间
     */
    @TableField("declaration_time")
    private LocalDateTime declarationTime;

    /**
     * 申报备注
     */
    @TableField("declaration_remark")
    private String declarationRemark;

    /**
     * 退回时间
     */
    @TableField("return_time")
    private LocalDateTime returnTime;

    /**
     * 退回原因
     */
    @TableField("return_remark")
    private String returnRemark;

    /**
     * 结案时间
     */
    @TableField("finish_time")
    private LocalDateTime finishTime;

    /**
     * 结案备注
     */
    @TableField("finish_remark")
    private String finishRemark;

    /**
     * 职伤认定是否成功-0-否 1-是
     */
    @TableField("determine_if_successful")
    private Integer determineIfSuccessful;

    /**
     * 认定时间
     */
    @TableField("determine_time")
    private LocalDateTime determineTime;

    /**
     * 认定失败原因
     */
    @TableField("determine_failure_remark")
    private String determineFailureRemark;

    /**
     * 待遇发放金额
     */
    @TableField("distribution_amount")
    private BigDecimal distributionAmount;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


    /**
     * 判断骑手类型的描述
     */
    //TODO:董原绯
//    @TableField(value = "determine_player")
//    private String determinePlayer;

    /**
     * 案件备注列表（非数据库字段，查询时通过关联获取）
     */
    @TableField(exist = false)
    private List<CaseRemark> remarks;

    /**
     * 案件状态变更日志列表（非数据库字段，查询时通过关联获取）
     */
    @TableField(exist = false)
    private List<CaseStatusLog> statusLogs;

    /**
     * 案件附件列表（非数据库字段，查询时通过关联获取）
     */
    @TableField(exist = false)
    private List<CaseAttachment> attachments;
}