package kd.stinkyTofu.service;

import kd.entity.ClaimCaseObject;

import java.util.List;
import java.util.Map;

public interface ClaimCaseObjectService {

    /**
     * 根据Map查询赔付对象列表
     * @param paramMap
     * @return
     */
    List<ClaimCaseObject> findByParamMap(Map<String, Object> paramMap);

    /**
     * 根据案件号查询赔付对象
     * @param claimCaseNo
     * @return
     */
    List<ClaimCaseObject> findObjectByClaimCaseNo(String claimCaseNo);

    /**
     * 根据案件号查询赔付对象并且分配人员
     * @param claimCaseNo
     * @return
     */
    List<ClaimCaseObject> findAuditerByClaimCaseNo(String claimCaseNo);


}
