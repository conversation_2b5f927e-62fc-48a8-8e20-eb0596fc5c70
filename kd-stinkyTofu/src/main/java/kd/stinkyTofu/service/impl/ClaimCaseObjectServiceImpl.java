package kd.stinkyTofu.service.impl;

import com.alibaba.fastjson.JSON;
import kd.common.tool.ESHighLevelRESTClientTool;
import kd.entity.ClaimCaseObject;
import kd.main.common.ElasticIndexName;
import kd.stinkyTofu.service.ClaimCaseObjectService;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@Transactional
public class ClaimCaseObjectServiceImpl implements ClaimCaseObjectService {

    @Override
    public List<ClaimCaseObject> findByParamMap(Map<String, Object> paramMap) {
        if (paramMap.size() == 0) {
            return new ArrayList<>();
        }
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);
        BoolQueryBuilder boolBuilder = new BoolQueryBuilder();
        if (paramMap.containsKey("claimCaseId")) {
            boolBuilder.must(QueryBuilders.termQuery("claimCaseId.keyword", paramMap.get("claimCaseId")));
        }
        if (paramMap.containsKey("type")) {
            boolBuilder.must(QueryBuilders.termQuery("type", paramMap.get("type")));
        }
        if (paramMap.containsKey("claimCaseNoList")) {
            List<String> list = (List<String>) paramMap.get("claimCaseNoList");
            boolBuilder.must(QueryBuilders.termsQuery("claimCaseNo.keyword", list));
        }
        searchSourceBuilder.query(boolBuilder);
        SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
        SearchHits searchHits = searchResponse.getHits();
        Long searchHitsValue = searchHits.getTotalHits().value;
        int total = searchHitsValue.intValue();
        if (total == 0) {
            return new ArrayList<>();
        }
        searchSourceBuilder.size(total);

        searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
        searchHits = searchResponse.getHits();
        SearchHit[] hits = searchHits.getHits();

        List<ClaimCaseObject> claimCaseObjectList = new ArrayList<>();

        for (SearchHit searchHit : hits) {
            ClaimCaseObject claimCaseObject = JSON.parseObject(searchHit.getSourceAsString(), ClaimCaseObject.class);
            claimCaseObjectList.add(claimCaseObject);
        }

        return claimCaseObjectList;
    }

    @Override
    public List<ClaimCaseObject> findObjectByClaimCaseNo(String claimCaseNo) {
        if (StringUtils.isBlank(claimCaseNo)) {
            return null;
        }
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);
        searchSourceBuilder.size(0);
        BoolQueryBuilder boolBuilder = new BoolQueryBuilder();
        boolBuilder.must(QueryBuilders.termQuery("claimCaseNo.keyword", claimCaseNo));
        searchSourceBuilder.query(boolBuilder);
        SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
        SearchHits searchHits = searchResponse.getHits();
        Long total = searchHits.getTotalHits().value;
        if (total == 0l) {
            return null;
        }
        searchSourceBuilder.size(total.intValue());
        searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
        searchHits = searchResponse.getHits();
        SearchHit[] hits = searchHits.getHits();
        List<ClaimCaseObject> claimCaseObjectList = new ArrayList<>();
        for (SearchHit hit : hits) {
            claimCaseObjectList.add(JSON.parseObject(hit.getSourceAsString(), ClaimCaseObject.class));
        }
        return claimCaseObjectList;
    }

    @Override
    public List<ClaimCaseObject> findAuditerByClaimCaseNo(String claimCaseNo) {
        if (StringUtils.isBlank(claimCaseNo)) {
            return null;
        }
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);
        searchSourceBuilder.size(0);
        BoolQueryBuilder boolBuilder = new BoolQueryBuilder();
        boolBuilder.must(QueryBuilders.termQuery("claimCaseNo.keyword", claimCaseNo));
        boolBuilder.must(QueryBuilders.existsQuery("auditer"));
        searchSourceBuilder.query(boolBuilder);
        SearchResponse searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
        SearchHits searchHits = searchResponse.getHits();
        Long total = searchHits.getTotalHits().value;
        if (total == 0l) {
            return null;
        }
        searchSourceBuilder.size(total.intValue());
        searchResponse = ESHighLevelRESTClientTool.searchSyn(ESHighLevelRESTClientTool.gentSearchRequest(ElasticIndexName.CLAIM_CASE_OBJECT, searchSourceBuilder));
        searchHits = searchResponse.getHits();
        SearchHit[] hits = searchHits.getHits();
        List<ClaimCaseObject> claimCaseObjectList = new ArrayList<>();
        for (SearchHit hit : hits) {
            claimCaseObjectList.add(JSON.parseObject(hit.getSourceAsString(), ClaimCaseObject.class));
        }
        return claimCaseObjectList;
    }
}
