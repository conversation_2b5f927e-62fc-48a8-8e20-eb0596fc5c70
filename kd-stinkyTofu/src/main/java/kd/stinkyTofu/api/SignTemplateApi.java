package kd.stinkyTofu.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import kd.common.tool.JsonBizTool;
import kd.entity.SignTemplatePushLog;
import kd.main.common.ExRetEnum;
import kd.main.common.QueueName;
import kd.stinkyTofu.service.SignTemplatePushLogService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * @author: hoomik
 * @description: 签字任务
 * @date: 2024/11/07 14:12
 * @param
 * @return
 */
@Controller
@RequestMapping("/api/signTemplateApi")
public class SignTemplateApi {
    private final static Logger logger = LoggerFactory.getLogger(SignTemplateApi.class);

    @Autowired
    private SignTemplatePushLogService signTemplatePushLogService;

    @Autowired
    private AmqpTemplate amqpTemplate;

    /**
     * @author: hoomik
     * @description: 接收推送任务返回并校验唯一标识
     * @date: 2024/11/07 14:13
     * @param json 组装好的推送数据
     * @return
     */
    @RequestMapping(value = "signPushStatusCallBack")
    @ResponseBody
    public String signPushStatusCallBack(@RequestBody String json) {
        logger.info("=======接收签字中台状态回传数据,jsonStr{}==========",json);
        try {
            /*
            code字段描述
            1:填充生成成功
            2：填充生成失败
            3：发起签署成功
            4：发起签署失败
            5：签署完成
            6：签署已撤销
            7：签署已过期
            8：签署已拒签*/

            JSONObject jsonObject = JSON.parseObject(json);
            String signTemplatePushLogId = jsonObject.getString("id");
            String code = jsonObject.getString("code");
            //目前用于返回填充后的文件和签字完成后的文件，它的的签字中台的唯一标识
            String signFileId = jsonObject.getString("signFileId");
            if(StringUtils.isAnyBlank(signTemplatePushLogId,code)){
                return JsonBizTool.genJson(ExRetEnum.ERROR_PARAMETER_LOST);
            }

            Integer status = null;
            switch (code){
                case "1":
                    status = 2;
                    break;
                case "2":
                    status = 3;
                    break;
                case "3":
                    status = 4;
                    break;
                case "4":
                    status = 5;
                    break;
                case "5":
                    status = 6;
                    break;
                case "6":
                    status = 7;
                    break;
                case "7":
                    status = 8;
                    break;
                case "8":
                    status = 9;
                    break;
                default:
                    return JsonBizTool.genJson("-1","未知状态");
            }
            SignTemplatePushLog signTemplatePushLog = signTemplatePushLogService.selectByPrimaryKeySql(signTemplatePushLogId);

            logger.info("======当前签字推送记录callBack信息{}=======",signTemplatePushLog.getCallBackJson());
            if(signTemplatePushLog == null){
                return JsonBizTool.genJson(ExRetEnum.ERROR_PARAMETER);
            }
            String result = jsonObject.getString("result");

            SignTemplatePushLog signTemplatePushLogUpdate = new SignTemplatePushLog();
            signTemplatePushLogUpdate.setId(signTemplatePushLog.getId());
            signTemplatePushLogUpdate.setStatus(status);
            if(StringUtils.isNotBlank(result)){
                signTemplatePushLogUpdate.setErrorMsg(result);
            }

            //获取填充成功后的唯一标识
            JSONObject jsonObject4SignFile = new JSONObject();
            if(StringUtils.isNotBlank(signFileId) && "1".equals(code)){
                String callBackJson = signTemplatePushLog.getCallBackJson();

                if(StringUtils.isNotBlank(callBackJson)){
                    jsonObject4SignFile = JSON.parseObject(callBackJson);
                }
                jsonObject4SignFile.put("fillUnique",signFileId);
                //存返回文件标识信息
                signTemplatePushLogUpdate.setCallBackJson(jsonObject4SignFile.toJSONString());
            }

            //获取签字成功后的唯一标识
            if(StringUtils.isNotBlank(signFileId) && "5".equals(code)){
                String callBackJson = signTemplatePushLog.getCallBackJson();
                if(StringUtils.isNotBlank(callBackJson)){
                    jsonObject4SignFile = JSON.parseObject(callBackJson);
                }
                jsonObject4SignFile.put("signSuccessUnique",signFileId);
                //存返回文件标识信息
                signTemplatePushLogUpdate.setCallBackJson(jsonObject4SignFile.toJSONString());
                //签署完成后将pdf文件转换成photo
                amqpTemplate.convertAndSend(QueueName.E_SIGN_PDF_TO_PHOTO,json);

            }

            logger.info("======当前签字推送记录要更新信息{}=======",JSON.toJSONString(signTemplatePushLogUpdate));


            signTemplatePushLogService.updateByPrimaryKeySelective(signTemplatePushLogUpdate);

        }catch (Exception e){
            e.printStackTrace();
            return JsonBizTool.genJson(ExRetEnum.ERROR_SYSTEMERROR);
        }
        return JsonBizTool.genJson(ExRetEnum.SUCCESS);
    }

}
